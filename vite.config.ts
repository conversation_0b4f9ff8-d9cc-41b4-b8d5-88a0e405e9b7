import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import px2rem from 'postcss-px2rem'
import AutoImport from 'unplugin-auto-import/vite'

const postcss = px2rem({
  // 基准大小 baseSize，需要和rem.js中相同
  remUnit: 100,
})


// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // vueDevTools(),
    AutoImport({
      // 自动导入Vue相关函数
      imports: ['vue', 'vue-router','pinia'],
      // 自动导入的文件类型
      dts: true, // 生成类型声明文件
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },

  css: {
    postcss: {
      plugins: [
        postcss,
      ],
    },
  },

})
