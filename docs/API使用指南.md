# API接口优雅使用指南

## 概述

本项目提供了多种优雅的API使用方式，支持完整的TypeScript类型提示，让开发者能够更高效、更安全地调用接口。

## 核心特性

- ✅ **完整的TypeScript类型支持**：所有接口都有明确的参数和返回值类型定义
- ✅ **智能代码补全**：IDE会自动提示可用的接口和参数
- ✅ **统一的错误处理**：自动处理API调用错误和状态管理
- ✅ **加载状态管理**：内置loading状态，无需手动管理
- ✅ **模块化组织**：按业务功能分组，便于维护和使用

## 使用方式

### 方式一：基础API调用（推荐用于简单场景）

```typescript
<script setup lang="ts">
import { useApi } from '@/hooks/useApi'
import { ref } from 'vue'

const $api = useApi()
const loading = ref(false)

const getData = async () => {
  loading.value = true
  try {
    // 拥有完整的类型提示
    const result = await $api.user.getUser({
      projectId: '1',
      page: 1,
      size: 10
    })
    
    if (result?.success) {
      console.log('用户数据:', result.data)
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
```

### 方式二：带状态管理的API调用（推荐用于复杂场景）

```typescript
<script setup lang="ts">
import { useApiWithState } from '@/hooks/useApi'

const { loading, error, execute, api } = useApiWithState()

const getData = async () => {
  // 自动管理loading状态和错误处理
  const result = await execute(api.user.getUser, {
    projectId: '1',
    page: 1,
    size: 10
  })
  
  if (result?.success) {
    console.log('用户数据:', result.data)
  }
}
</script>

<template>
  <div>
    <el-button @click="getData" :loading="loading">
      获取数据
    </el-button>
    <div v-if="error" class="error">{{ error }}</div>
  </div>
</template>
```

### 方式三：专用API Hook（推荐用于特定业务场景）

```typescript
<script setup lang="ts">
import { useUserApi, useDeviceApi, useAlarmApi } from '@/hooks/useApi'

// 用户相关接口
const userApi = useUserApi()
const { loading: userLoading } = userApi

// 设备相关接口
const deviceApi = useDeviceApi()
const { loading: deviceLoading } = deviceApi

// 告警相关接口
const alarmApi = useAlarmApi()
const { loading: alarmLoading } = alarmApi

const loginUser = async () => {
  const result = await userApi.login({
    username: 'admin',
    password: '123456',
    params: {}
  })
  
  if (result?.success) {
    console.log('登录成功')
  }
}

const getDevices = async () => {
  const result = await deviceApi.getDevices({
    projectId: '1',
    page: 1,
    size: 10
  })
  
  if (result?.success) {
    console.log('设备列表:', result.data)
  }
}
</script>
```

### 方式四：直接导入使用（适用于特殊场景）

```typescript
<script setup lang="ts">
import { api } from '@/api'
import { login, getUser } from '@/api/user'

const directApiCall = async () => {
  // 使用统一的api实例
  const result1 = await api.user.getUser({ projectId: '1' })
  
  // 或者直接导入的单个接口
  const result2 = await login({
    username: 'admin',
    password: '123456',
    params: {}
  })
}
</script>
```

## 接口类型定义

### 通用类型

```typescript
// API响应格式
interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  msg?: string;
  code?: number;
}

// 分页参数
interface PageParams {
  page?: number;
  size?: number;
  projectId?: string | number;
}
```

### 用户相关类型

```typescript
interface LoginParams {
  username: string;
  password: string;
  params?: any;
  code?: string;
}

interface UserInfo {
  id: number;
  username: string;
  nickname?: string;
  email?: string;
  phone?: string;
  avatar?: string;
}
```

### 设备相关类型

```typescript
interface DeviceParams extends PageParams {
  id?: number;
  deviceTypeId?: number;
  areaId?: number;
  name?: string;
  status?: number;
}

interface DeviceInfo {
  id: number;
  name: string;
  code: string;
  model: string;
  factory: string;
  deviceTypeName: string;
  areaName: string;
  // ... 更多字段
}
```

## 最佳实践

### 1. 选择合适的使用方式

- **简单的一次性调用**：使用方式一（基础API调用）
- **需要状态管理的场景**：使用方式二（带状态管理）
- **特定业务模块**：使用方式三（专用Hook）
- **特殊定制需求**：使用方式四（直接导入）

### 2. 错误处理

```typescript
const { loading, error, execute } = useApiWithState(false) // 不自动显示错误

const getData = async () => {
  const result = await execute(api.user.getUser, { projectId: '1' })
  
  if (!result) {
    // 自定义错误处理
    ElMessage.error(`获取数据失败: ${error.value}`)
    return
  }
  
  // 处理成功结果
  console.log(result.data)
}
```

### 3. 类型安全

```typescript
// ✅ 正确 - 有类型提示和检查
const result = await api.user.getUser({
  projectId: '1',  // 类型: string | number
  page: 1,         // 类型: number
  size: 10         // 类型: number
})

// ❌ 错误 - TypeScript会报错
const result = await api.user.getUser({
  projectId: 123,
  invalidParam: 'test'  // 这个参数不存在，会有类型错误
})
```

### 4. 组合式使用

```typescript
<script setup lang="ts">
import { useUserApi, useDeviceApi } from '@/hooks/useApi'
import { computed } from 'vue'

const userApi = useUserApi()
const deviceApi = useDeviceApi()

// 组合多个API的loading状态
const globalLoading = computed(() => 
  userApi.loading || deviceApi.loading
)

const initData = async () => {
  // 并行调用多个接口
  const [userResult, deviceResult] = await Promise.all([
    userApi.getUser({ projectId: '1' }),
    deviceApi.getDevices({ projectId: '1' })
  ])
  
  // 处理结果...
}
</script>
```

## 扩展自定义Hook

如果需要为特定业务创建自定义Hook：

```typescript
// hooks/useCustomApi.ts
import { useApiWithState } from './useApi'
import { api } from '@/api'

export function useCustomBusinessApi() {
  const { execute, ...state } = useApiWithState()
  
  const customMethod = async (params: CustomParams) => {
    // 组合多个API调用
    const result1 = await execute(api.user.getUser, params)
    const result2 = await execute(api.device.getDevices, params)
    
    // 自定义业务逻辑
    return {
      users: result1?.data || [],
      devices: result2?.data || []
    }
  }
  
  return {
    ...state,
    customMethod
  }
}
```

## 总结

通过以上方案，您可以：

1. **享受完整的类型提示**：IDE会自动提示可用的接口和参数
2. **减少重复代码**：统一的错误处理和状态管理
3. **提高开发效率**：清晰的API组织结构和使用方式
4. **确保代码质量**：TypeScript类型检查避免运行时错误
5. **便于维护**：模块化的API组织方式

选择适合您场景的使用方式，让API调用变得更加优雅和高效！ 