/// <reference types="vite/client" />


declare module '*.vue' {
    import type { DefineComponent } from 'vue'
  
    const component: DefineComponent<{}, {}, any>
    export default component
  }
  
  interface Window {
    apiUrl: string,
    PROD_UE: string,
    ue: {
      web: {
        onhtmlmsg: (msg: string) => void
      }
    },
    // window.uecall = app.config.globalProperties.$myGlobalMethod
    uecall: (data: any) => void
  }
  
  declare module 'postcss-px2rem';