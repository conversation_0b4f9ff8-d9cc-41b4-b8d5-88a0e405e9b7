//设备扩展shux
class DeviceItem{
    id: number;
    name: string;
    propertyKey: string;
    propertyValue: string;
    deviceId: number;
    type: string;
    projectId: string;
    constructor(){
        this.id=0;
        this.name="";
        this.propertyKey="";
        this.propertyValue="";
        this.deviceId=0;
        this.type="",
        this.projectId="";
    }
}

export default DeviceItem