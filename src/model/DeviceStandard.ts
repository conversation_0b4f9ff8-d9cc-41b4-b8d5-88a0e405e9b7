class DeviceStandard{
    id: number;
    projectId: string;
    standardId: string;
    dataSource: string;
    name: string;
    dataSourceType: string;
    apiUrl: string;
    params: string;
    value: string;
    variable: string;
    deviceId: string;
    constructor(){
        this.id=0;
        this.projectId="";
        this.standardId="";
        this.dataSource="";
        this.name="";
        this.dataSourceType="";
        this.apiUrl="";
        this.params="";
        this.value="";
        this.variable="";
        this.deviceId="";
    }
}

export default DeviceStandard;