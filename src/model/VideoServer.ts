class VideoServer {
    id: number;
    serverName: string;
    port: string;
    ip: string;
    username: string;
    password: string;
    session: string;
    projectId: string;
    deviceType: string;
    constructor() {
        this.id = 0;
        this.serverName = "";
        this.port = "";
        this.ip = "";
        this.username = "";
        this.password = "";
        this.session = "";
        this.projectId = "";
        this.deviceType = ""
    }
}

export default VideoServer