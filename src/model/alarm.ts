class AlarmConfig {
  alarmHHEnable: boolean;
  alarmHH: number;
  alarmHEnable: boolean;
  alarmH: number;
  alarmLL: number;
  alarmLLEnable: boolean;
  alarmLEnable: boolean;
  alarmL: number;
  alarmST0: boolean;
  alarmST1: boolean;
  alarmST2: boolean;
  alarmST3: boolean;
  associateVideo: string;
  associateVideo2: string;
  associateForm: string;
  associateForm2: string;
  enableRecord: boolean;
  enableSnap: boolean;
  action1: string;
  action2: string;
  phone: string;
  standardId: string;
  sceneId: string;
  sourceId: string;
  alarmDesc: string;
  alarmLevel: number;
  constructor() {
    this.alarmHHEnable = false; //超高报警
    this.alarmHH = 0; //超高报警值
    // this.alarmLevelHH = 1; //超高报警级别
    this.alarmHEnable = false; //高限报警
    this.alarmH = 0; //高限值
    // this.alarmLevelH = 1; //高限报警级别

    this.alarmLL = 0; //超低报警值
    this.alarmLLEnable = false; //超低报警
    // this.alarmLevelLL = 1; //超低报警
    this.alarmLEnable = false; //底限报警
    this.alarmL = 0; //底限值
    // this.alarmLevelL = 1; //底限报警级别

    // this.alarmLevelST = 1; //状态报警级别
    this.alarmST0 = false;
    this.alarmST1 = false;
    this.alarmST2 = false;
    this.alarmST3 = false;

    this.associateVideo = "";
    this.associateVideo2 = "";
    this.associateForm = "";
    this.associateForm2 = "";
    this.enableRecord = false; //录像
    this.enableSnap = false; //抓拍
    this.action1 = ""; //动作1
    this.action2 = ""; //动作2
    this.phone = ""; //手机号

    this.standardId = "";
    this.sceneId = "";

    this.sourceId = "";
    this.alarmDesc = "";
    this.alarmLevel = 0;

  }

}

export { AlarmConfig };