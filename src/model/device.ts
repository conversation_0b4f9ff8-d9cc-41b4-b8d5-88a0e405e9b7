class Device{
    id: number;
    name: string;
    code: string;
    deviceType: string;
    position: string;
    factoryName: string;
    installTime: string;
    model: string;
    status: number;
    ip: string;
    phone: string;
    person: string;
    period: string;
    projectId: string;
    menuId: never[];
    areaId: never[];
    departmentId: never[];
    manualPath: string;
    cadPath: string;
    camFlag: boolean;
    projectName: string;
    menuName: string;
    departmentName: string;
    areaName: string;
    imgPath: string;
    professionType: string;
    constructor()
    {
        this.id= 0;
        this.name= '';
        this.code='';
        this.deviceType='';
        this.position='';
        this.factoryName='';
        this.installTime='';
        this.model='';
        this.status=0;
        this.ip='';
        this.phone='';
        this.person='';
        this.period='';
        this.projectId='';
        this.menuId=[];
        this.areaId=[];
        this.departmentId=[];
        this.manualPath='';
        this.cadPath='';
        this.camFlag=false;
        this.projectName='';
        this.menuName='';
        this.departmentName='';
        this.areaName=""
        this.imgPath="";
        this.professionType="";
    }
}

export default Device