class Spare {
    id: number;
    name: string;
    deviceType: string;
    spareModel: string;
    factory: string;
    price: string;
    count: number;
    unit: string;
    projectId: string;
    position: string;
    spares: never[];
    constructor() {
        this.id=0;
        this.name="";
        this.deviceType="";
        this.spareModel="";
        this.factory="";
        this.price="";
        this.count=0;
        this.unit="";
        this.projectId="";
        this.position="";
        this.spares=[]
    }
}

export default Spare;