export default class GisConfig{
    constructor(
       
        public  id:number,
    
        public  projectId:number,
        public  alarmColor:string,
      
        public  borderColor:string,
      
        public  fillColor:string,
  
        public  deviceId:number,
   
        public  alarmStd:number,

        public  statusStd:number,
       
        public  actionType:number,
    
        public  server:string,
  
        public  cam:string,

        public  icon:string,
    
        public  iconColor:string,
 
        public  drawType:string,
   
        public  points:string,
    
        public  configId:string,
        public  radius:number,
        public  conditionName:string,
        public  conditionValue:string,
        public  conditionValue1:string,
        public  strokeWeight:number,
        public  opacity:number,
        public  deviceType:number,
        public  deviceTypeName:string,
        public  stds: DeviceStd[],
        public  deviceName:string,
        public  deviceIcon:string,
        public  model:string,
        public  vr:string
    
    ){}
}

class DeviceStd{
    constructor(
    public  variable:string,
    public  stdName:string,
    public  stdId:number,
    public  dataType:string,
    public  paramValue:string,
    public  icon:string,
    public  unit:string,
    public max:string,
    public min:string
    ){}
}