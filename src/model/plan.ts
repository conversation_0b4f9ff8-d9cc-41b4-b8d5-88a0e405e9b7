
class Plan {
    id: number;
    name: string;
    type: number;
    projectId: string;
    lineId: string;
    staffId: string;
    level: number;
    status: boolean;
    patrolType: number;
    constructor() {
        this.id = 0;
        this.name = '';
        this.type = 1;
        this.projectId = '';

        this.lineId = "";
        this.staffId = "";
        this.level = 1;
        this.status = true;
        this.patrolType = 2;
    }
}

class PlanEvent{
    id: number;
    name: string;
    type: number;
    projectId: string;
    lineId: string;
    staff: string;
    level: number;
    status: boolean;
    patrolType: number;
    times: number;
    constructor(){
        this.id= 0;
        this.name= '';
        this.type= 1;
        this.projectId= '';
        this.lineId="";
        this.staff="";
        this.level=1;
        this.status=true;
        this.patrolType=2;
        this.times=1;
    }
}

export  {Plan,PlanEvent};