class AuthMenu{
    menuId: string;
    roleId: string;
    menuType: string;
    projectId: string;
    authId: string;
    half: boolean;
    constructor(){
        this.menuId="";
        this.roleId="";
        this.menuType="";
        this.projectId="";
        this.authId="";
        this.half=false;
    }
}

class AuthArea{
    roleId: string;
    areaId: string;
    projectId: string;
    half: boolean;
    constructor(){
        this.roleId="";
        this.areaId="";
        this.projectId="";
        this.half=false;
    }
}

class AuthDevice{
    deviceId: string;
    roleId: string;
    projectId: string;
    half: string;
    camToken: string;
    constructor(){
        this.deviceId="";
        this.roleId="";
        this.projectId="";
        this.half="";
        this.camToken="";
    }
}
export {AuthMenu,AuthArea,AuthDevice} ;