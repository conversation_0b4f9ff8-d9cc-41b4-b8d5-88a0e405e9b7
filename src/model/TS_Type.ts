export type StringInput={
    standardName:string,
    value:string,
    icon:string,
    unit:string|number,
    id:string,
    
}

export type NumOut={
    standardName:string,
    value:string,
    icon:string,
    variable:string,
    id:string
}
export type StringOut={
    standardName:string,
    icon:string,
    id:string,
    variable:string,
    max:(string|number),
    min:(string|number)
}
export type NumInput={
    name:string,
    id:string|number,
    [prop:string]:(string|number)
}

export class Subenum {
    constructor(
        public standardName:string,
        public value:string,
        public icon:string,
        public variable:string,
        public id:string,
        public name:string,
        public type:string
    ){}
 
}


export class SubData {
    constructor(
        public name:string,
        public state:NumInput[],
        public string_out:StringOut[],
        public num_out:NumOut[],
        public input:StringInput[],
        public enums:Subenum[][],
        public id?:number,
        public icon?:string,
        public location?:{}
    ){ }
    
}


export class LiveDataResponse{
    constructor(
        public  batchDefinitionId:string,
        public  clientId:string,
        public  code:number,
        public  message:string,
        public  success:boolean,
        public data:LiveData[]
    ){}
}

export class LiveData{
    constructor(
        public  id:string,
        public  itemStatus:number,
        public  value:(string|number),
        public  valueType:number,
        public  timestamp:string,
        public  level:number,
        public  area:number,
        public  success:boolean,
        public  desc:string,
        public  propName:string
    ){}
}

export class SubSendData{
    constructor(public id:string,
        public iosvrKey:string,
        public chlKey:string,
        public ctrlKey:string,
        public varKey:string,
        public realTime:boolean ){}
}

export class Response<T>{
    constructor(public success:boolean,
        public data:T,
        public msg:string,
        public total:number
        ){}
}

export class StandardParam{
    constructor(
        public  id:number,
        public  projectId:number,
        public  name:string,
        public  paramKey:string,
        public  paramValue:string,
        public  deviceTypeId:number,
        public  standardId:number,
        public  showType:number,
        public  unit:number,
        public  max:string,
        public  min:string,
        public  dataType:string,
        public  icon:string,
        public  stdName:string,
        public  config:string
    ){}
}

export class DeviceStandard{
   constructor(
    public   id:number,
    public  projectId:number,
    public  deviceId:number,
    public  standardId:number,
    public  variable:string,
    public  dataSource:string,
    public  dataSourceType:number,
    public  apiUrl:string,
    public  params:string,
    public  value:string,
    public  name:string,
    public deviceParams:StandardParam[]
   ){}
}


export class Device{
    constructor(
        public id:number,
        public name:string,
        public deviceStandards:DeviceStandard[],
        public icon?:string,
    ){}
}