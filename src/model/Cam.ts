class Camera{
    camToken: string;
    serverId: string;
    areaId: string;
    strName: string;
    strUser: string;
    strPasswd: string;
    strSrcIpAddress: string;
    strSrcPort: string;
    menuId: string;
    deviceType: string;
    projectId: string;
    videoType: string;
    strUrl: string;
    constructor(){
        this.camToken="";
        this.serverId="";
        this.areaId="";
        this.strName="";
        this.strUser="";
        this.strPasswd="";
        this.strSrcIpAddress="";
        this.strSrcPort="";
        this.menuId="";
        this.deviceType="";
        this.projectId="";
        this.videoType="";
        this.strUrl="";
    }
}

export default Camera