class Video {
    camToken: string;
    serverId: string;
    areaId: string;
    strName: string;
    strUrl: string;
    strUser: string;
    strPasswd: string;
    strSrcIpAddress: string;
    strSrcPort: string;
    nType: string;
    lating: string;
    ptz: string;
    menuId: string;
    constructor() {
       this.camToken="";
       this.serverId="";
       this.areaId="";
       this.strName="";
       this.strUrl="";
       this.strUser="";
       this.strPasswd="";
       this.strSrcIpAddress="";
       this.strSrcPort="";
       this.nType="";
       this.lating="";
       this.ptz="";
       this.menuId="";
    }
}