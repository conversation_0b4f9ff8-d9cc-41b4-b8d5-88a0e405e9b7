class DeviceParam {
    id: number;
    varName: string;
    type: string;
    unit: string;
    deviceId: string;
    standardId: string;
    dataSource: string;
    dataSourceType: string;
    apiUrl: string;
    params: string;
    value: string;
    constructor() {
        this.id = 0;
        this.varName = "";
        this.type = "";
        this.unit = "";
        this.deviceId = "";
        this.standardId = "";
        this.dataSource = "";

        this.dataSourceType = "";
        this.apiUrl = "";
        this.params = "";
        this.value = "";
    }
}

export default DeviceParam