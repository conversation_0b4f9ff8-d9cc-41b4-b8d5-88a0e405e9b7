export default  class BIMConfig {
    constructor(public id: number,
        public name: string,
        public deviceId: number,
        public objectId: string,
        public projectId: number,
        public  fileId: string,
        public areaId: number,
        public menuId: number,
        public floor: string,
        public actionType: number,
        public server: string,
        public cam: string,
        public panelId: number,
        public deviceType: number,
        public icon: string,
        public color: string,
        public position: string,
        public modelFileId: string,
        public standardId: number,
        public openFloor: string,
        public variable: string,
        public alarmConfig:AlarmConfig,
        public data:ConfigItemData[]
    ) { };

    //  AlarmConfig alarmConfig;

    //  List<ConfigItemData> data;
}

class AlarmConfig{
    constructor( public  id:number,
        public  configId:number,
        public  color:string,
        public  value:number,
        public  type:number){}
}
class ConfigItemData{
    constructor( 
    public  id:number,
    public  configId:number,
    public  name:string,

    public  type:number,
    public  unit:number,

    public  standardId:number,

    public conditions:ConfigCondition[];

    public  variable:string){}
}

class ConfigCondition{
    constructor(
    public  id:number,
    public  itemDataId:number,
    public  factor:string,
    public  value:number,
    public  icon:string,
    public  text:string){}
}
