import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { getCookie } from '@/utils/cookie';

export const useAppStore = defineStore('appStore', () => {
  const token = ref('')
  const funMenus=ref(null);
  const projectId=ref(null);
  const isClose=ref(false);
  const h_alarm=ref([]);
  const r_alarm=ref<any[]>([]);
  const area=ref<any>(null);
  const index=ref(-2);
  const name=ref('');
  const dialog=ref({
    component:null,
  });
  const mesData=ref([]);

  const SET_TOKEN=(data:any)=>{
    token.value=data;
  }



  const SET_FUN_MENU=(data:any)=>{
    funMenus.value=data;
  }

  const SET_PROJECT_ID=(data:any)=>{
    projectId.value=data;
  }

  const SET_ALARM_H=(data:any)=>{
    h_alarm.value=data;
  }

  const SET_ALARM_R=(alarm:any)=>{
    if (alarm.length === 0) {
      r_alarm.value = []
      return
    }
    r_alarm.value.unshift(alarm)
  }
  const SET_NAV_AREA=(data:any)=>{
    area.value=data;
  }

  const SET_DIALOG=(data:any)=>{
    dialog.value=data;
  }

  const SET_MESSAGE_DATA=(data:any)=>{
    mesData.value=data;
  }

  const SET_CLOSE_ALARM=(data:any)=>{
    isClose.value=data;
  } 
  const SET_MENU_INDEX=(data:any)=>{
    index.value=data;
  }
  const SET_NAME=(data:any)=>{
    name.value=data;
  }
  const RESET_STATE=()=>{
     projectId.value=null;
     name.value='';
     token.value='';
     funMenus.value=null;
     isClose.value=false;
     h_alarm.value=[];
     r_alarm.value=[];
     area.value=null;

  }

  return {
    token,
    funMenus,
    projectId,
    isClose,
    name,
    h_alarm,
    r_alarm,
    area,
    dialog,
    index,
    mesData,
    SET_FUN_MENU,
    SET_PROJECT_ID,
    SET_ALARM_H,
    SET_ALARM_R,  
    SET_NAV_AREA,
    SET_DIALOG,
    SET_MESSAGE_DATA,
    SET_CLOSE_ALARM,
    SET_MENU_INDEX,
    SET_NAME,
    RESET_STATE,
    SET_TOKEN
  }
})
