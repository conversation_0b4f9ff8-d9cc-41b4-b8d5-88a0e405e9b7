@use './variables.scss'as *;
@use './mixin.scss'as *;
@use './element-ui.scss'as *;
@use './btn.scss'as *;
@use './font.scss'as *;
@use './table.scss'as *;




html {
  height: 100%;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}


body {
  height: 100%;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  padding: 0;
  margin: 0;
  font-size: 14px;
  overflow: hidden;
}

#app {
  height: 100%;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.ml {
  margin-left: 10px !important;
}

.center {
  // text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}






.layout_bg {
  background: url('@/assets/images/home.jpg') no-repeat center fixed;
  width: 100%;
  height: 100%;
  background-size: cover;
}

.home_bg {
  background: url('@/assets/images/home_bg.png') no-repeat center fixed;
  width: 100%;
  height: 100%;
  background-size: cover;
}

.locat {
  color: $themeColor;
}

.play {
  color: #02e287;
}

.p1 {
  padding: 15px;
}




.layout_wrapper {
  position: relative;
  height: calc(100% - 100px);
  // height: 100%;
  top: 38px;
}



.auto {
  .el-checkbox__input.is-checked+.el-checkbox__label {
    font-weight: 400;
    color: #c7dfff;
  }
}

.BMap_bubble_pop {
  border: 1px solid #323a3e !important;
  background-color: #091822 !important;
  padding: 8px !important;
  border-radius: 4px !important;

  img {
    display: none;
  }
}

.BMap_bubble_title {
  font-size: 16px;
  font-weight: 500;
  color: $whiteColor !important;
}

.BMap_cpyCtrl {
  display: none;
}

.BMapLabel {
  background-color: rgba(255, 255, 255, 0) !important;
  border: none !important;
}

.anchorBL {
  display: none;
}

.bread {
  position: absolute;
  z-index: 9;
  top: 64px;
}

.top_tabs {
  position: relative;
  width: 75px;
  text-align: center;
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi';
  font-weight: 400;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;

  .active {
    position: absolute;
    width: 100%;
    height: 18px;
    left: 0;
    right: 0;
    bottom: 0px;
    background: url('../assets/images/btn_bak.png') no-repeat;
    background-size: contain;
  }
}

.form-item-btn {
  margin-right: 32px !important;
}



.sum {
  line-height: 40px;
  margin-top: 8px;
  text-align: center;
  color: #fff;

  .iconfont {
    font-size: 22px;
  }
}






// 已在全局 el-date-picker 样式中统一处理



.alarm_list {
  position: absolute;
  padding: 8px;
  width: 357px;
  right: 8px;
  background: rgba(9, 22, 30, 1);
  border: 1px solid #2b2e32;

  .alarm_scrollbar {
    height: calc(100% - 177px);

    .list {
      position: relative;
      background: rgba(47, 54, 60, 0.3);
      border: 1px solid #2f363c;
      color: $whiteColor;
      padding: 24px 15px 24px 15px;
      margin-bottom: 8px;
      cursor: pointer;

      .alarm_name {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-family: "PingFangSC-Medium";
        font-weight: 500;
        color: $whiteColor;
        margin-bottom: 6px;
      }

      .msg,
      .date {
        font-size: 14px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: 400;
        color: #9ca4b7;
      }

      .type {
        position: absolute;
        top: 0;
        font-size: 12px;
        border-radius: 0px 0px 8px 0px;
        left: 0;
        padding: 1px 5px;
      }

      .pt {
        background: #417edf;
      }

      .yz {
        background: #e3731b;
      }

      .jj {
        background: #b4061a;
      }
    }
  }

  .alarm_btn {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .btn {
      background: rgba(40, 107, 148, 0.9);
      font-size: 12px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      color: #9ca4b7;
      width: calc(20% - 8px);
      margin: 5px 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: 64px;
      cursor: pointer;

      i {
        color: $whiteColor;
        font-size: 18px;
      }

      &:last-child {
        margin-right: 0;
      }

      .label {
        margin-top: 8px;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.form_inline {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  height: 40px;
  padding: 5px 0;
  // background: rgba(68, 114, 141, 0.1);
  box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);

  .el-form-item--small.el-form-item {
    margin-bottom: 0px;
    vertical-align: middle !important;
  }
}

.noData {
  color: $whiteColor;
  font-size: 14px;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.custom-tree-node {
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi',
    'PingFang SC';
  font-weight: 400;
  color: #C7DFFF;

  .uploadBtn {
    color: #778897;
  }

  .uploadBtn:hover {
    color: $themeColor;
  }
}

.card-body {
  font-size: 16px;
  font-family: "Alibaba-PuHuiTi";
  font-weight: 400;
  padding: 10px 0;
  height: calc(100% - 49px);

  .card-data {
    display: flex;
    margin-bottom: 15px;
    padding-left: 15px;

    .card-data-title {
      color: #889cc3;
      margin-right: 10px;
    }

    .card-data-val {
      color: $whiteColor;
    }

    .val {
      font-size: 16px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      color: #1bc0ed;
    }
  }

  .link-data {
    display: flex;
    justify-content: space-between;
    padding: 0 8px 0 0;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: $whiteColor;

    .iconfont {
      color: #02e287;
    }
  }

  .progress-data {
    display: flex;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: $whiteColor;


  }

  .el-timeline {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-left: 10px;

    .el-timeline-item {
      flex: 1;
    }
  }
}

// 已在全局 el-date-picker 样式中统一处理

.el-input.is-disabled .el-input__inner {
  background-color: transparent !important;
}



.play_wrapper {
  .table {
    .el-checkbox {
      display: none;
    }
  }

}

// .diagram {
//   position: relative;
//   display: flex;
//   width: 1200px;
//   margin: 100px auto 70px auto;
//   height: calc(100% - 170px);
// }

// .device_dialog {
//   height: calc(100% - 150px);
//   background: url("@/assets/images/home.jpg") !important;
//   background-repeat: no-repeat;
//   background-size: cover;
// }

.content_tab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  height: 40px;
  margin-bottom: 9px;
  background: rgba(68, 114, 141, 0.1);
  box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);


  .left_tab {
    height: 100%;

    .btn {
      font-size: 14px;
      display: inline-block;
      cursor: pointer;
      height: 40px;
      color: $whiteColor;
      padding: 0 16px;
      line-height: 40px;
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.5);

      &.active {
        color: #c7dfff;
        background: linear-gradient(180deg,
            rgba(199, 223, 255, 0) 0%,
            rgba(199, 223, 255, 0.3) 100%);
        box-shadow: 0px 1px 0px 0px #c7dfff;
      }
    }
  }
}

.btn-group {
  display: flex;
  align-items: center;
  margin-bottom: 5px;

  .iconfont {
    margin: 0 5px;
  }

  .el-button--mini {
    padding: 2px 8px;
  }
}

// 表格容器全局样式 - 与 .left 和 .right 协调
.table {
  font-size: 14px;
  font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
  font-weight: 400;
  background: transparent;
  border-radius: 12px;
  overflow: hidden;
  position: relative;

  // 状态颜色定义 - 与主题色协调
  .onLine {
    color: #27edbb;
    text-shadow: 0 1px 2px rgba(39, 237, 187, 0.3);
  }

  .offLine {
    color: #b8bcbf;
    text-shadow: 0 1px 2px rgba(184, 188, 191, 0.3);
  }

  .gzhf {
    color: #e3731b;
    text-shadow: 0 1px 2px rgba(227, 115, 27, 0.3);
  }

  .gz {
    color: #ff4757;
    text-shadow: 0 1px 2px rgba(255, 71, 87, 0.3);
  }

  .cq {
    color: #3de9fa;
    text-shadow: 0 1px 2px rgba(61, 233, 250, 0.3);
  }

  // 表格容器增强样式
  &.h100 {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-table {
      flex: 1;
      min-height: 0;
    }

    .page {
      margin-top: 12px;
      padding: 8px 0;
      background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
      border-radius: 8px;
      border: 1px solid rgba(61, 233, 250, 0.2);
      backdrop-filter: blur(4px);
    }
  }

  // 搜索框样式
  .search_box {
    margin-bottom: 12px;
    padding: 8px 12px;
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%);
    border: 1px solid rgba(61, 233, 250, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(6px);

    .searchBtn,
    .addBtn {
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%) !important;
      border: 1px solid rgba(61, 233, 250, 0.4) !important;
      border-radius: 6px !important;
      color: #E6F4FF !important;
      font-family: "Alibaba-PuHuiTi" !important;
      font-size: 14px !important;
      padding: 8px 16px !important;
      transition: all 0.3s ease !important;
      position: relative !important;
      overflow: hidden !important;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        border-color: rgba(61, 233, 250, 0.6) !important;
        box-shadow: 0 4px 12px rgba(61, 233, 250, 0.3) !important;
        transform: translateY(-2px) !important;

        &::before {
          opacity: 1;
        }
      }
    }
  }
}

// 工作表格特定样式
.work-table,
.order,
.alarm-table {
  .table-container {
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%);
    border: 1px solid rgba(61, 233, 250, 0.3);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(8px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .el-table {
      background: transparent !important;
    }
  }

  // 按钮组样式
  .btn-group {
    margin-bottom: 16px;
    padding: 12px;
    background: linear-gradient(135deg, rgba(6, 30, 54, 0.6) 0%, rgba(12, 45, 78, 0.4) 100%);
    border: 1px solid rgba(61, 233, 250, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(4px);

    .el-button {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

// 表格状态标签样式
.el-table {
  .status-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

    &.status-pending {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.3) 0%, rgba(255, 193, 7, 0.1) 100%);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.4);
    }

    &.status-processing {
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(61, 233, 250, 0.1) 100%);
      color: #3de9fa;
      border: 1px solid rgba(61, 233, 250, 0.4);
    }

    &.status-completed {
      background: linear-gradient(135deg, rgba(39, 237, 187, 0.3) 0%, rgba(39, 237, 187, 0.1) 100%);
      color: #27edbb;
      border: 1px solid rgba(39, 237, 187, 0.4);
    }

    &.status-error {
      background: linear-gradient(135deg, rgba(255, 71, 87, 0.3) 0%, rgba(255, 71, 87, 0.1) 100%);
      color: #ff4757;
      border: 1px solid rgba(255, 71, 87, 0.4);
    }
  }

  // 操作按钮样式
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      min-width: 60px;
      font-size: 12px;
    }
  }
}

.content {
  .page {
    // margin-top: 10px;
    text-align: right;
  }
}

.wrapper {
  .page {
    height: 40px;
  }
}

.tree_search,
.select_date,
.container_header {
  .el-input__inner {
    background-color: rgba(97, 109, 148, 0.2) !important;
    font-size: 14px;
    font-family: 'Alibaba-PuHuiTi';
    font-weight: 400;
    color: $whiteColor;
  }
}

.tree_search .el-input__inner {
  border: none;
}

.select_date .el-input__inner {
  border: 1px solid $whiteColor;
}

.tree {
  .custom-tree-node {
    display: flex;
    width: 100%;
    color: $whiteColor;
    justify-content: space-between;
    padding-right: 15px;

    .iconfont {
      color: $themeColor;
      margin-right: 5px;
    }

    .icon {
      width: 10px;
      height: 10px;
      display: inline-block;
      border-radius: 50%;
    }

    .online {
      background: linear-gradient(205deg, #07ef35 0%, #017206 100%);
    }

    .offline {
      background: linear-gradient(205deg, #ef2a07 0%, #b2061b 100%);
    }
  }
}

.downLoad {
  height: 28px;
  min-height: 28px !important;
  background: rgba(61, 233, 250, 1) !important;
  border-radius: 2px !important;
  padding: 0 20px !important;
  border: none !important;
  color: #05090D !important;
  margin: 3px 0 !important;
}

.form-inline {
  font-family: 'Alibaba-PuHuiTi',
    'PingFang SC';
  font-weight: 400;
  color: $whiteColor;
}



.host_box,
.loginForm {
  .el-input__wrapper {
    font-family: 'Alibaba-PuHuiTi',
      'PingFang SC';
    font-size: 14px;
    font-weight: 400;
    color: $whiteColor !important;
    background-color: rgba(47, 54, 60, 0.3) !important;
    border: 1px solid #2F363C;
    box-shadow: none;
  }
}



.iconfont {
  line-height: 25px;
  cursor: pointer;
}

.list-item {
  .el-slider__button {
    width: 10px;
    height: 10px;
    border: 1px solid #13d4d9;
  }
}

.bf-panel {
  z-index: 99 !important;
  top: 64px !important;
  right: 80px !important;
}


.bf-panel,
.bf-setting .bf-setting-li,
.bf-panel .bf-title,
.bf-tree-header,
.bf-walk-panel {
  background-color: rgba(9, 22, 30, 0.55) !important;
}

.bf-house {
  display: none;
}

.clearfix::after {
  content: "";
  clear: both;
  display: block;
  height: 0px;
}

.select_panel,
.picker {
  background: none !important;
  border: none !important;
}


.flex {
  display: flex;
}



.type-icon {
  position: absolute;
  right: 1%;
  top: 70px;
  z-index: 29;
  color: #818a90;
  display: flex;

  i {
    font-size: 24px;
  }

  .active {
    color: #e3731b;
  }
}

.h100 {
  height: 100%;
}

iframe {
  height: 100%;
  width: 100%;
  border: 0;
}

div {

  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // background-color: #1c1b1b96;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    // -webkit-box-shadow: inset 0 0 6px #1c1b1b96;
    border-radius: 6px;
    // background-color: #1c1b1b96;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 6px rgba(144, 147, 153, 0.3);
    background-color: rgba(144, 147, 153, 0.3);
  }
}

.cursor {
  cursor: pointer;
}

.map_wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  .allmap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin: 0;
  }

  .panel {
    background-color: #091822 !important;
    position: absolute;
    z-index: 100;
    padding: 10px;
    border: 1px solid #323a3e;

    .header {
      color: #889cc3;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #889cc3;
      padding: 5px 0;
      margin-bottom: 10px;
    }

    el-poppe .panel_list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #889cc3;

      .slider {
        width: 100px;
      }

      .standardName {
        padding: 0 5px;
      }
    }
  }
}

.device-type {
  position: absolute;
  bottom: 70px;
  right: 390px;
  width: 100px;
  z-index: 100;
  background-color: #091822;

  .el-card {
    background-color: #091922 !important;
    border: 1px solid #323a3e;
  }
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}



//工单中心
.el-tabs__content {
  height: calc(100% - 50px);
}

.bim-box {
  &.hide_left {
    .bf-tree-toolbar {
      left: 10px !important;
      transition: left 0.5s;
    }
  }

  &.hide_right {
    // .bf-toolbar-bottom {
    //   right: 10px !important;
    //   transition: right 0.5s;
    // }
  }

  // .bf-toolbar-bottom {
  //   display: none !important;
  // }
  .bf-toolbar.bf-toolbar-bottom {
    border: none;
    background: rgba(0, 0, 0, 0.2);
    z-index: 99999;
  }

  .bf-toolbar.bf-toolbar-bottom,
  .bf-walk-panel {
    display: none;
    flex-direction: column;
    right: 130px;
    left: auto !important;
    top: 50%;
    transform: translateY(-50%);
    bottom: auto !important;
    opacity: 1;
    transition: right 0.5s;
  }


  .bf-tree-toolbar {
    display: none;
  }

  .bf-tips {
    z-index: 105;
  }
}

.w100 {
  width: 100%;
}


.purchase {
  .el-form-item {
    margin-bottom: 0 !important;
  }
}

.h_center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}



.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}




.left-top {
  border-top: 2px solid #29a3fb;
  border-left: 2px solid #29a3fb;
  top: 0;
  left: 0;

}

.right-top {
  border-top: 2px solid #29a3fb;
  border-right: 2px solid #29a3fb;
  top: 0;
  right: 0;
}

.left-buttom {
  border-bottom: 2px solid #29a3fb;
  border-left: 2px solid #29a3fb;
  bottom: 0;
  left: 0;
}

.right-buttom {
  border-bottom: 2px solid #29a3fb;
  border-right: 2px solid #29a3fb;
  bottom: 0;
  right: 0;
}

.menu {

  .el-dropdown-menu,
  .el-menu {
    color: #fff;
    background: unset !important;
  }

  .el-menu--horizontal>.el-menu-item {
    color: #fff;
  }

  .el-sub-menu,
  .el-menu-item {
    // background: url("@/assets/images/selected.png") no-repeat;
    background-size: contain;
  }


  li {
    width: 146px !important;
    height: 42px !important;
    padding: unset !important;
    font-size: 18px;
  }

  .el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
  .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    background-color: unset !important;
  }

  .el-menu--horizontal>.el-menu-item.is-active {
    border: 0px !important;
  }



  .el-menu--horizontal>.el-sub-menu .el-sub-menu__title:hover {
    background-color: unset !important;
  }
}

.el-menu--horizontal {
  border-bottom: 0px !important;
}

.el-menu--popup {
  background: linear-gradient(212.36deg, rgba(0, 52, 60, 0.76) 15.63%, rgba(0, 38, 60, 0.32) 96.91%);
  box-shadow: 0px 0px 8px rgba(26, 148, 217, 0.4) inset;
  min-width: 130px !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px !important;

  li:nth-of-type(1) {
    margin-top: 5px;
  }
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-sub-menu__title {
  background-color: unset !important;
  color: white !important;
  width: 120px !important;
  height: 30px !important;
  background: linear-gradient(212.36deg, rgba(0, 52, 60, 0.76) 15.63%, rgba(0, 38, 60, 0.32) 96.91%) !important;
  box-shadow: 0px 0px 16px rgba(26, 148, 217, 0.25) inset !important;
  margin-bottom: 4px;
}

.el-sub-menu .el-sub-menu__icon-arrow {
  display: none;
}

.el-sub-menu .el-menu-item {
  min-width: 120px !important;
}

.el-menu--horizontal>.el-sub-menu.is-active .el-sub-menu__title {
  border-bottom: 0px !important;
}

.el-menu--horizontal>.el-sub-menu .el-sub-menu__title {
  color: white !important;
  display: flex;
  justify-content: center;
  font-size: 18px;
}





.panel {
  .el-input__wrapper {
    background-color: unset !important;
    box-shadow: unset !important;
    transition: none !important;
    padding-left: 10px !important;
    width: 100px;

  }

  .el-input__inner {
    color: rgba(255, 255, 255, 0.9);
    font-family: Microsoft YaHei;
    font-weight: bold;
    // font-size: 18px;
  }
}




.menu {
  .el-popper {
    background: #021f35 !important;

    .selected {
      background-color: rgb(2, 50, 72) !important;
    }
  }


}







//symbol图标引用
.icon1 {
  width: 48px;
  height: 48px;
  cursor: pointer;
}


:root {
  --el-menu-active-color: white !important;
  --el-disabled-bg-color: transparent !important;
  --el-disabled-border-color: transparent !important;
}






.el-popover.el-popper {
  min-width: 0 !important;
  width: unset !important;
  --el-popover-padding: 5px !important;
  --el-bg-color-overlay: #03a9f44d !important;
  --el-text-color-regular: white
}

.wrapper_left,
.wrapper_right {
  // top: 70px;
  padding-top: 70px;
  // height: calc(100% - 70px);
  height: 100%
}

.left,
.right {
  margin-top: 70px;
  height: calc(100% - 100px);
  z-index: 100;
  position: absolute;
  background: url("/src/assets/images/dialog/panel_bg.png") no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(5px);

}



.left {
  left: 15px;
  width: 387px;
  border: 1px solid rgba(61, 233, 250, 0.3);
  border-radius: 15px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: slideInLeft 0.6s ease-out;

  .header {
    font-size: 18px;
    font-family: "DOUYU";
    font-weight: 500;
    color: #E6F4FF;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    padding: 12px 8px;
    border-bottom: 1px solid rgba(61, 233, 250, 0.2);
    margin-bottom: 15px;

    img {
      filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
    }
  }

  .input {
    margin-bottom: 15px;

    .el-input__wrapper {
      background: rgba(2, 20, 36, 0.8) !important;
      border: 1px solid rgba(61, 233, 250, 0.4) !important;
      border-radius: 8px !important;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
      transition: all 0.3s ease !important;

      &:hover {
        border-color: rgba(61, 233, 250, 0.6) !important;
        box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;
      }

      &.is-focus {
        border-color: #3de9fa !important;
        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4) !important;
      }
    }
  }

  .device {
    height: calc(100% - 160px);

    .list {
      background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%);
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: normal;
      color: #FFFFFF;
      margin-bottom: 10px;
      border-left: 3px solid #4274A3;
      border-radius: 8px;
      height: 56px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%);
        border-left-color: #3de9fa;
        transform: translateX(5px);
        box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2);

        &::before {
          opacity: 1;
        }
      }

      .name {
        width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .iconfont {
        background: linear-gradient(180deg, #FFFFFF 0%, #8ED6FF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 12px;
        font-size: 18px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
      }

      .state {
        div {
          margin-right: 20px;
          white-space: nowrap;
          padding: 4px 8px;
          border-radius: 4px;
          background: rgba(0, 0, 0, 0.2);
          font-size: 12px;
        }

        &>:last-child {
          margin-right: unset;
        }
      }
    }
  }


  //element plus
  .el-input {
    --el-input-bg-color: #021424 !important;
    --el-input-icon-color: #7A9BBD !important;
    --el-input-placeholder-color: #7A9BBD !important;
    --el-input-hover-border-color: #3E5B7B !important;
    --el-border-color: #3E5B7B !important;
    --el-color-primary: #3E5B7B !important;
  }

  .el-input__inner {
    color: #7A9BBD;
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: normal;
  }

  .el-pagination {
    --el-fill-color-blank: transparent !important;
    --el-pagination-text-color: #5C7992 !important;
    --el-color-primary: #ffffff !important;
  }
}

.right {
  right: 15px;
  width: 393px;
  border: 1px solid rgba(61, 233, 250, 0.3);
  border-radius: 15px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;
  animation: slideInRight 0.6s ease-out;

  .item {
    height: calc(100% / 3);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    border: 1px solid rgba(61, 233, 250, 0.3);
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: rgba(61, 233, 250, 0.5);
      box-shadow: 0 6px 20px rgba(61, 233, 250, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);

      &::before {
        opacity: 1;
      }
    }

    &-body {
      display: flex;
      justify-content: center;
      height: calc(100% - 40px);
      position: relative;
      z-index: 1;
    }
  }

  .order {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;

    .text {
      font-size: 16px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 500;
      color: #EAEBEC;
      margin-right: 20px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .num {
      font-size: 28px;
      font-family: "BEBAS";
      font-weight: 400;
      color: #F3F3F3;
      width: 100px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    }

    &-left {
      background: linear-gradient(135deg, rgba(17, 150, 252, 0.2) 0%, rgba(61, 233, 250, 0.1) 100%);
      border: 2px solid rgba(17, 150, 252, 0.3);
      border-radius: 12px;
      width: 155px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: conic-gradient(from 0deg, transparent, rgba(61, 233, 250, 0.1), transparent);
        animation: rotate 4s linear infinite;
      }

      &::after {
        content: '';
        position: absolute;
        inset: 2px;
        background: linear-gradient(135deg, rgba(6, 30, 54, 0.9) 0%, rgba(12, 45, 78, 0.8) 100%);
        border-radius: 10px;
      }

      .center {
        font-size: 36px;
        font-family: "BEBAS";
        font-weight: 400;
        color: #FFFFFF;
        background: linear-gradient(135deg, #FFFFFF 0%, #1196FC 50%, #3de9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 8px rgba(61, 233, 250, 0.3);
      }
    }

    &-right {
      width: 214px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      gap: 8px;
    }

    &-text {
      margin: 8px 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 8px 12px;
      background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%);
      backdrop-filter: blur(4px);
      border-radius: 8px;
      border-left: 3px solid transparent;
      border: 1px solid rgba(61, 233, 250, 0.2);
      transition: all 0.3s ease;
      width: 100%;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.02);

      &:hover {
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%);
        border-left-color: #3de9fa;
        border-color: rgba(61, 233, 250, 0.4);
        transform: translateX(3px);
        box-shadow: 0 4px 12px rgba(61, 233, 250, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.05);
      }

      .dot {
        width: 8px;
        height: 8px;
        margin-right: 10px;
        border-radius: 50%;
        box-shadow: 0 0 8px currentColor;
      }
    }

    .total {
      font-size: 16px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 500;
      color: #E9EAEB;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      margin-bottom: 8px;
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  // 左侧面板滑入动画
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  // 右侧面板滑入动画
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .kong {
    font-family: "Alibaba-PuHuiTi";
    font-weight: normal;
    color: #E6F0F6;
    flex-direction: column;
    height: calc(100% - 40px);

    .name {
      margin-right: 10px;
      display: flex;
      align-items: center;
      white-space: nowrap;
      font-weight: 500;

      img {
        filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
        margin-right: 8px;
      }
    }

    .btn {
      width: 90px;
      height: 48px;
      background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
      border: 1px solid rgba(61, 233, 250, 0.3);
      border-radius: 8px;
      margin-left: 10px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        border-color: rgba(61, 233, 250, 0.6);
        box-shadow: 0 4px 12px rgba(61, 233, 250, 0.2);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }
      }

      &>div {
        flex: 1;
        position: relative;
        z-index: 1;
      }

      &>div>div {
        height: 36px;
        width: 36px;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
        border: 1px solid rgba(61, 233, 250, 0.4);
        border-radius: 6px;
        color: #E6F0F6;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
          border-color: #3de9fa;
          box-shadow: 0 0 8px rgba(61, 233, 250, 0.4);
        }
      }
    }

    .list {
      width: 100%;
      background: linear-gradient(135deg, rgba(16, 52, 87, 0.5) 0%, rgba(24, 64, 104, 0.4) 100%);
      backdrop-filter: blur(6px);
      border: 1px solid rgba(61, 233, 250, 0.25);
      border-radius: 8px;
      margin-bottom: 8px;
      padding: 12px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.03);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        border-color: rgba(61, 233, 250, 0.4);
        box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }
      }
    }

    .dot {
      height: 2px;
      background: linear-gradient(90deg, transparent 0%, #6E94BA 50%, transparent 100%);
      flex: 1;
      margin: 0 10px;
      border-radius: 1px;
    }
  }

  .event {
    font-family: "Alibaba-PuHuiTi";
    font-weight: normal;
    color: #E6F0F6;
    flex-direction: column;
    height: calc(100% - 40px);

    .name {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 500;
      color: #F0F9FF;
      width: 120px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

      .iconfont {
        margin-right: 8px;
        filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
      }

      img {
        margin-right: 8px;
        filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
      }
    }

    .time {
      font-size: 18px;
      font-family: "BEBAS";
      font-weight: 400;
      color: #C3D2E0;
      width: 130px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .list {
      width: 100%;
      background: linear-gradient(135deg, rgba(16, 52, 87, 0.5) 0%, rgba(24, 64, 104, 0.4) 100%);
      backdrop-filter: blur(6px);
      border: 1px solid rgba(61, 233, 250, 0.25);
      border-radius: 8px;
      margin-bottom: 8px;
      height: 44px;
      position: relative;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: all 0.3s ease;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.03);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        border-color: rgba(61, 233, 250, 0.4);
        box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.05);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
        }
      }
    }

    .bar {
      width: 4px;
      height: 100%;
      background: linear-gradient(180deg, #3de9fa 0%, #1196FC 50%, transparent 100%);
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 0 8px 8px 0;
      box-shadow: 0 0 8px rgba(61, 233, 250, 0.4);
    }
  }

  .month_repair {
    display: flex;
    height: 48px;
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
    backdrop-filter: blur(4px);
    border-radius: 8px;
    border: 1px solid rgba(61, 233, 250, 0.15);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.02);

    .list {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      background: transparent !important;
      transition: all 0.3s ease;
      border-radius: 8px;

      &:hover {
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%) !important;
      }

      .icon {
        width: 8px;
        height: 8px;
        margin-right: 6px;
        border-radius: 50%;
        box-shadow: 0 0 6px currentColor;
      }

      .value {
        font-size: 25px;
        margin-left: 10px;
        font-family: "DINAlternate-Bold";
        font-weight: bold;
        color: #ffffff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .sub_echart {
    height: calc(100% - 48px);
  }



}



//设备详情弹窗
.diagram {
  .el-pagination {
    --el-fill-color-blank: transparent !important;
    --el-pagination-text-color: #5C7992 !important;
    --el-color-primary: #ffffff !important;
  }
}




.wrapper_right {
  right: 0;
  //  background: linear-gradient(270deg,rgb(0 8 18 / 80%),rgb(255 255 255 / 0%));
}

.wrapper_left {
  // background: linear-gradient(90deg,rgb(0 8 18 / 80%),rgb(255 255 255 / 0%));
  left: 15px;
}






//显示与隐藏动画

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0 !important;
}




.z100 {

  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
  // background: linear-gradient(90deg, rgba(0, 8, 18, 1) 0%, rgba(0, 14, 30, 0.9) 72.19%, rgba(0, 15, 32, 0.68) 83.65%, rgba(0, 12, 26, 0) 100%);

}


//菜单 swiper style

.swiper-slide {
  // width: 74px !important;
}

.swiper-button-next {
  background: url("@/assets/images/menu/next.png") no-repeat center;
  right: -23px !important;

}

.swiper-button-prev {
  background: url("@/assets/images/menu/prev.png") no-repeat center;
  left: -23px !important;
}





//设置弹窗页面二级弹窗
.addDiagram {

  .el-dialog__body,
  .el-dialog__footer {
    opacity: 1 !important;
  }

  .el-input__wrapper,
  .el-textarea__inner {
    background-color: #0C1B26 !important;
  }

  .el-input,
  .el-textarea {
    --el-input-border-color: #283F52 !important;
    --el-input-hover-border-color: #283F52 !important;
    --el-border-color-hover: #283F52 !important;

  }

  // 已在全局 el-select 样式中统一处理


  .el-input__inner {
    border: 0 !important;
    background-color: unset !important;
  }


  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }


}

.border0 {
  .el-dialog__body {
    border-bottom: 0px !important;
  }

}


input {
  text-align: unset !important;
}

.el-pager li {
  color: #8ca1ca !important;
}


.el-badge__content.is-fixed {
  right: 0 !important;
}

.plan_container .fc-toolbar-chunk div {
  width: 200px !important;
}

.energy_container {
  position: absolute;
  z-index: 101;
  top: 70px;
  height: calc(100% - 190px);
  width: 100%;
  background: linear-gradient(270deg, #000812 0%, rgba(0, 14, 30, 0.8) 72.19%, rgba(0, 15, 32, 0.5) 83.65%, rgba(0, 12, 26, 0) 100%);

}

// .swiper-wrapper{
//   display: flex;
//   justify-content: center;
// }


.cctv-dialog {
  width: 835px !important;
}

// 全局滚动条美化
.el-scrollbar__bar {
  &.is-vertical {
    .el-scrollbar__thumb {
      background: linear-gradient(180deg, rgba(61, 233, 250, 0.6) 0%, rgba(17, 150, 252, 0.4) 100%);
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(180deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%);
        box-shadow: 0 0 8px rgba(61, 233, 250, 0.4);
      }
    }
  }

  &.is-horizontal {
    .el-scrollbar__thumb {
      background: linear-gradient(90deg, rgba(61, 233, 250, 0.6) 0%, rgba(17, 150, 252, 0.4) 100%);
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(90deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%);
        box-shadow: 0 0 8px rgba(61, 233, 250, 0.4);
      }
    }
  }
}

.el-scrollbar__track {
  background: rgba(16, 52, 87, 0.3);
  border-radius: 4px;
}

// 全局统计卡片样式
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  height: 100%;

  .stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg,
      rgba(16, 52, 87, 0.6) 0%,
      rgba(24, 64, 104, 0.4) 50%,
      rgba(32, 76, 121, 0.3) 100%);
    backdrop-filter: blur(12px);
    border-radius: 12px;
    padding: 10px 12px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 2px 8px rgba(61, 233, 250, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
    cursor: pointer;
    animation: statCardFadeIn 0.6s ease-out;

    // 为每个卡片添加不同的延迟
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }

    // 主要光效背景
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle at center,
        rgba(61, 233, 250, 0.08) 0%,
        rgba(61, 233, 250, 0.04) 30%,
        transparent 70%);
      opacity: 0;
      transition: all 0.4s ease;
      transform: scale(0.8);
    }

    // 边框光效
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 12px;
      padding: 1px;
      background: linear-gradient(135deg,
        rgba(61, 233, 250, 0.4) 0%,
        rgba(17, 150, 252, 0.2) 50%,
        transparent 100%);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: xor;
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    &:hover {
      border-color: rgba(61, 233, 250, 0.6);
      box-shadow:
        0 8px 32px rgba(61, 233, 250, 0.25),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      transform: translateY(-4px) scale(1.02);

      &::before {
        opacity: 1;
        transform: scale(1);
      }

      &::after {
        opacity: 1;
      }

      .stat-icon .iconfont {
        transform: scale(1.1);
        filter: drop-shadow(0 0 12px rgba(61, 233, 250, 0.6));
      }

      .stat-number {
        transform: scale(1.05);
        text-shadow: 0 4px 16px rgba(61, 233, 250, 0.6);
      }
    }

    // 激活状态动画
    &:active {
      transform: translateY(-2px) scale(0.98);
      transition: all 0.1s ease;
    }

    .stat-icon {
      margin-bottom: 12px;
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg,
        rgba(61, 233, 250, 0.15) 0%,
        rgba(61, 233, 250, 0.05) 100%);
      backdrop-filter: blur(4px);
      border: 1px solid rgba(61, 233, 250, 0.2);

      .iconfont {
        font-size: 22px;
        color: #3de9fa;
        filter: drop-shadow(0 0 8px rgba(61, 233, 250, 0.4));
        transition: all 0.3s ease;
      }
    }

    .stat-number {
      font-size: 28px;
      font-family: "DOUYU";
      font-weight: 700;
      color: #3de9fa;
      text-shadow: 0 4px 12px rgba(61, 233, 250, 0.5);
      margin-bottom: 6px;
      position: relative;
      z-index: 2;
      transition: all 0.3s ease;
      line-height: 1;
      animation: numberPulse 2s ease-in-out infinite;

      // 数字变化时的闪烁效果
      &.number-changed {
        animation: numberPulse 0.6s ease-out;
      }
    }

    .stat-label {
      font-size: 13px;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 500;
      color: #E9EAEB;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
      position: relative;
      z-index: 2;
      letter-spacing: 0.5px;
    }

    // 不同类型的卡片特殊样式
    &:nth-child(1) {
      .stat-icon {
        background: linear-gradient(135deg,
          rgba(45, 135, 230, 0.15) 0%,
          rgba(45, 135, 230, 0.05) 100%);
        border-color: rgba(45, 135, 230, 0.3);
      }
      .stat-icon .iconfont {
        color: #2d87e6;
        filter: drop-shadow(0 0 8px rgba(45, 135, 230, 0.4));
      }
      .stat-number {
        color: #2d87e6;
        text-shadow: 0 4px 12px rgba(45, 135, 230, 0.5);
      }
    }

    &:nth-child(2) {
      .stat-icon {
        background: linear-gradient(135deg,
          rgba(82, 196, 26, 0.15) 0%,
          rgba(82, 196, 26, 0.05) 100%);
        border-color: rgba(82, 196, 26, 0.3);
      }
      .stat-icon .iconfont {
        color: #52c41a;
        filter: drop-shadow(0 0 8px rgba(82, 196, 26, 0.4));
      }
      .stat-number {
        color: #52c41a;
        text-shadow: 0 4px 12px rgba(82, 196, 26, 0.5);
      }
    }

    &:nth-child(3) {
      .stat-icon {
        background: linear-gradient(135deg,
          rgba(250, 173, 20, 0.15) 0%,
          rgba(250, 173, 20, 0.05) 100%);
        border-color: rgba(250, 173, 20, 0.3);
      }
      .stat-icon .iconfont {
        color: #faad14;
        filter: drop-shadow(0 0 8px rgba(250, 173, 20, 0.4));
      }
      .stat-number {
        color: #faad14;
        text-shadow: 0 4px 12px rgba(250, 173, 20, 0.5);
      }
    }

    &:nth-child(4) {
      .stat-icon {
        background: linear-gradient(135deg,
          rgba(245, 34, 45, 0.15) 0%,
          rgba(245, 34, 45, 0.05) 100%);
        border-color: rgba(245, 34, 45, 0.3);
      }
      .stat-icon .iconfont {
        color: #f5222d;
        filter: drop-shadow(0 0 8px rgba(245, 34, 45, 0.4));
      }
      .stat-number {
        color: #f5222d;
        text-shadow: 0 4px 12px rgba(245, 34, 45, 0.5);
      }
    }
  }
}

// 全局动画定义
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes statCardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes iconRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// 全局设备控制列表样式
.device-control-list {
  .list {
    width: 100%;
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.5) 0%, rgba(24, 64, 104, 0.4) 100%);
    backdrop-filter: blur(6px);
    border: 1px solid rgba(61, 233, 250, 0.25);
    border-radius: 8px;
    margin-bottom: 8px;
    height: 44px;
    position: relative;
    padding: 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.03);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: rgba(61, 233, 250, 0.4);
      box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
      transform: translateY(-2px);

      &::before {
        opacity: 1;
      }
    }

    .name {
      display: flex;
      align-items: center;
      margin-right: 10px;
      white-space: nowrap;
      font-weight: 500;

      img {
        filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
        margin-right: 8px;
      }

      div {
        font-weight: 500;
        color: #E6F4FF;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
      }
    }

    .dot {
      flex: 1;
      height: 1px;
      background: linear-gradient(90deg, rgba(61, 233, 250, 0.3) 0%, transparent 100%);
      margin: 0 12px;
    }

    .btn {
      display: flex;
      gap: 8px;

      .center {
        width: 32px;
        height: 24px;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
        border: 1px solid rgba(61, 233, 250, 0.3);
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        color: #3de9fa;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
          border-color: rgba(61, 233, 250, 0.5);
          box-shadow: 0 2px 8px rgba(61, 233, 250, 0.3);
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

// 全局事件列表样式
.event-list {
  .list {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 12px;
    margin-bottom: 8px;
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%);
    backdrop-filter: blur(6px);
    border: 1px solid rgba(61, 233, 250, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: "Alibaba-PuHuiTi";
    font-size: 14px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(61, 233, 250, 0.05) 0%, transparent 50%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      border-color: rgba(61, 233, 250, 0.3);
      transform: translateY(-1px);

      &::before {
        opacity: 1;
      }
    }

    .name {
      display: flex;
      align-items: flex-start;
      width: 35%;

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        margin-top: 2px;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        flex-shrink: 0;
      }

      > div {
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.3;
        max-height: 2.6em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-weight: 500;
        color: #E6F4FF;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    // 标准名称样式
    .standard-name {
      width: 20%;
      font-weight: 500;
      color: #E6F4FF;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      word-wrap: break-word;
      word-break: break-all;
      line-height: 1.3;
      max-height: 2.6em;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 13px;
    }

    // 数值样式
    .value {
      width: 10%;
      font-weight: 600;
      color: #3de9fa;
      text-shadow: 0 1px 3px rgba(61, 233, 250, 0.5);
      font-family: "DOUYU";
      font-size: 16px;
      text-align: center;
      line-height: 1.3;
      max-height: 2.6em;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .time {
      width: 35%;
      font-weight: 500;
      color: #B8C5D1;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      font-size: 12px;
      text-align: right;
      word-wrap: break-word;
      word-break: break-all;
      line-height: 1.3;
      max-height: 2.6em;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .bar {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, rgba(61, 233, 250, 0.3) 0%, transparent 100%);
    }
  }
}

// 全局右侧面板动画
.right {
  animation: slideInRight 0.6s ease-out;
}

// 面板项目动画效果
.item {
  .item-body>* {
    animation: fadeInUp 0.6s ease-out;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}