@use './variables.scss'as *;

// ============== 全局 Element Plus 组件统一样式 ==============

// 全局 input 包装器样式 - 所有输入类组件统一
.el-input__wrapper,
.el-textarea__inner,
.el-select .el-input__wrapper,
.el-select .el-select__wrapper,
.el-date-editor .el-input__wrapper,
.el-cascader .el-input__wrapper,
.el-autocomplete .el-input__wrapper,
.el-input-number .el-input__wrapper {
        // 参考 .left 面板的输入框样式
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 8px !important;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        backdrop-filter: blur(4px) !important;
        transition: all 0.3s ease !important;

        &:hover {
                border-color: rgba(61, 233, 250, 0.5) !important;
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                box-shadow: 0 0 8px rgba(61, 233, 250, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        }

        &.is-focus,
        &.is-focused {
                border-color: $themeColor !important;
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                box-shadow: 0 0 12px rgba(61, 233, 250, 0.4), inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        }

        &.is-disabled {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%) !important;
                border-color: rgba(61, 233, 250, 0.1) !important;
                cursor: not-allowed !important;
                opacity: 0.6 !important;
        }
}

// 全局输入框内容样式
.el-input__inner,
.el-textarea__inner,
.el-select .el-input__inner,
.el-date-editor .el-input__inner,
.el-cascader .el-input__inner,
.el-autocomplete .el-input__inner,
.el-input-number .el-input__inner {
        color: #E6F4FF !important;
        background-color: transparent !important;
        font-size: 14px !important;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
        font-weight: 400 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;

        &::placeholder {
                color: #7A9BBD !important;
                font-size: 14px !important;
        }

        &:disabled {
                color: #6a8fbd !important;
                cursor: not-allowed !important;
        }
}

// 全局图标样式
.el-input__prefix,
.el-input__suffix,
.el-select__suffix,
.el-date-editor .el-input__prefix,
.el-date-editor .el-input__suffix {
        .el-input__prefix-inner,
        .el-input__suffix-inner {
                color: #829CBD !important;
                transition: color 0.3s ease !important;

                .el-icon {
                        color: #829CBD !important;
                        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
                        transition: all 0.3s ease !important;

                        &:hover {
                                color: $themeColor !important;
                                filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4)) !important;
                        }
                }
        }
}

.el-textarea .el-input__count {
        background: transparent !important;
        color: #829CBD !important;
        font-size: 12px !important;
}

// 全局按钮样式统一
.el-button {
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 6px !important;
        color: #E6F4FF !important;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        backdrop-filter: blur(4px) !important;
        transition: all 0.3s ease !important;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;

        &:hover {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                border-color: rgba(61, 233, 250, 0.5) !important;
                box-shadow: 0 0 8px rgba(61, 233, 250, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
                transform: translateY(-1px) !important;
        }

        &:active {
                transform: translateY(0) !important;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        }

        &.is-disabled {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%) !important;
                border-color: rgba(61, 233, 250, 0.1) !important;
                color: #6a8fbd !important;
                cursor: not-allowed !important;
                opacity: 0.6 !important;
                transform: none !important;
        }

        // 主要按钮样式
        &--primary {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                border-color: $themeColor !important;
                color: #FFFFFF !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%) !important;
                        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4) !important;
                }
        }

        // 成功按钮样式
        &--success {
                background: linear-gradient(135deg, rgba(39, 237, 187, 0.8) 0%, rgba(16, 185, 129, 0.6) 100%) !important;
                border-color: #27edbb !important;
                color: #FFFFFF !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(39, 237, 187, 0.9) 0%, rgba(16, 185, 129, 0.7) 100%) !important;
                        box-shadow: 0 0 12px rgba(39, 237, 187, 0.4) !important;
                }
        }

        // 警告按钮样式
        &--warning {
                background: linear-gradient(135deg, rgba(227, 115, 27, 0.8) 0%, rgba(245, 158, 11, 0.6) 100%) !important;
                border-color: #e3731b !important;
                color: #FFFFFF !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(227, 115, 27, 0.9) 0%, rgba(245, 158, 11, 0.7) 100%) !important;
                        box-shadow: 0 0 12px rgba(227, 115, 27, 0.4) !important;
                }
        }

        // 危险按钮样式
        &--danger {
                background: linear-gradient(135deg, rgba(255, 71, 87, 0.8) 0%, rgba(239, 68, 68, 0.6) 100%) !important;
                border-color: #ff4757 !important;
                color: #FFFFFF !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(255, 71, 87, 0.9) 0%, rgba(239, 68, 68, 0.7) 100%) !important;
                        box-shadow: 0 0 12px rgba(255, 71, 87, 0.4) !important;
                }
        }
}

// 全局表单样式统一
.el-form {
        .el-form-item__label {
                color: #E6F4FF !important;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
                font-weight: 500 !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
                font-size: 14px !important;
        }

        .el-form-item__error {
                color: #ff4757 !important;
                font-size: 12px !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }
}

// 全局下拉菜单样式
.el-dropdown {
        color: #E6F4FF !important;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;

        .el-dropdown__caret-button {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-radius: 0 6px 6px 0 !important;
                color: #829CBD !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                        border-color: rgba(61, 233, 250, 0.5) !important;
                        color: $themeColor !important;
                }
        }
}

// 全局下拉菜单面板样式
.el-dropdown-menu {
        background: linear-gradient(135deg, rgba(9, 24, 34, 0.95) 0%, rgba(16, 52, 87, 0.9) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        backdrop-filter: blur(12px) !important;
        border-radius: 8px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        padding: 4px 0 !important;

        .el-dropdown-menu__item {
                color: #E6F4FF !important;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
                font-size: 14px !important;
                padding: 8px 16px !important;
                transition: all 0.3s ease !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                        color: #E6F4FF !important;
                        border-left: 3px solid rgba(61, 233, 250, 0.6) !important;
                        padding-left: 13px !important;
                }

                &.is-disabled {
                        color: #6a8fbd !important;
                        cursor: not-allowed !important;
                        opacity: 0.5 !important;

                        &:hover {
                                background: transparent !important;
                                border-left: none !important;
                                padding-left: 16px !important;
                        }
                }
        }
}

.project_name .el-dropdown {
        font-size: 16px;
        font-family: 'Alibaba-PuHuiTi';
        font-weight: 400;
        color: #778897;
}

// 全局标签样式
.el-tag {
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.4) !important;
        border-radius: 4px !important;
        color: #E6F4FF !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        backdrop-filter: blur(2px) !important;
        transition: all 0.3s ease !important;

        &:hover {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%) !important;
                border-color: $themeColor !important;
                box-shadow: 0 2px 6px rgba(61, 233, 250, 0.2) !important;
        }

        .el-tag__close {
                color: #829CBD !important;
                transition: all 0.3s ease !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;

                &:hover {
                        color: $themeColor !important;
                        filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4)) !important;
                }
        }

        // 不同类型的标签
        &--success {
                background: linear-gradient(135deg, rgba(39, 237, 187, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%) !important;
                border-color: rgba(39, 237, 187, 0.4) !important;
                color: #27edbb !important;
        }

        &--warning {
                background: linear-gradient(135deg, rgba(227, 115, 27, 0.2) 0%, rgba(245, 158, 11, 0.1) 100%) !important;
                border-color: rgba(227, 115, 27, 0.4) !important;
                color: #e3731b !important;
        }

        &--danger {
                background: linear-gradient(135deg, rgba(255, 71, 87, 0.2) 0%, rgba(239, 68, 68, 0.1) 100%) !important;
                border-color: rgba(255, 71, 87, 0.4) !important;
                color: #ff4757 !important;
        }
}

// 全局开关样式
.el-switch {
        .el-switch__core {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.3) !important;
                backdrop-filter: blur(4px) !important;
                transition: all 0.3s ease !important;

                &::after {
                        background: linear-gradient(135deg, #E6F4FF 0%, #829CBD 100%) !important;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
                        transition: all 0.3s ease !important;
                }
        }

        &.is-checked {
                .el-switch__core {
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                        border-color: $themeColor !important;
                        box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;

                        &::after {
                                background: linear-gradient(135deg, #FFFFFF 0%, #E6F4FF 100%) !important;
                                box-shadow: 0 2px 8px rgba(61, 233, 250, 0.4) !important;
                        }
                }
        }
}

// 全局复选框样式
.el-checkbox {
        .el-checkbox__input {
                .el-checkbox__inner {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                        border: 1px solid rgba(61, 233, 250, 0.3) !important;
                        border-radius: 4px !important;
                        backdrop-filter: blur(4px) !important;
                        transition: all 0.3s ease !important;

                        &:hover {
                                border-color: rgba(61, 233, 250, 0.5) !important;
                                box-shadow: 0 0 6px rgba(61, 233, 250, 0.2) !important;
                        }

                        &::after {
                                border-color: #FFFFFF !important;
                                border-width: 2px !important;
                        }
                }

                &.is-checked {
                        .el-checkbox__inner {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                                border-color: $themeColor !important;
                                box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;
                        }
                }
        }

        .el-checkbox__label {
                color: #E6F4FF !important;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
                font-weight: 400 !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }
}

// 全局单选框样式
.el-radio {
        .el-radio__input {
                .el-radio__inner {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                        border: 1px solid rgba(61, 233, 250, 0.3) !important;
                        backdrop-filter: blur(4px) !important;
                        transition: all 0.3s ease !important;

                        &:hover {
                                border-color: rgba(61, 233, 250, 0.5) !important;
                                box-shadow: 0 0 6px rgba(61, 233, 250, 0.2) !important;
                        }

                        &::after {
                                background: linear-gradient(135deg, $themeColor 0%, #1196FC 100%) !important;
                                box-shadow: 0 0 4px rgba(61, 233, 250, 0.5) !important;
                        }
                }

                &.is-checked {
                        .el-radio__inner {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                                border-color: $themeColor !important;
                                box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;
                        }
                }
        }

        .el-radio__label {
                color: #E6F4FF !important;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
                font-weight: 400 !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }
}

// 全局滑块样式
.el-slider {
        .el-slider__runway {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-radius: 4px !important;
                backdrop-filter: blur(4px) !important;
        }

        .el-slider__bar {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                border-radius: 4px !important;
                box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;
        }

        .el-slider__button {
                background: linear-gradient(135deg, #FFFFFF 0%, #E6F4FF 100%) !important;
                border: 2px solid $themeColor !important;
                box-shadow: 0 2px 8px rgba(61, 233, 250, 0.4) !important;
                transition: all 0.3s ease !important;

                &:hover {
                        transform: scale(1.1) !important;
                        box-shadow: 0 4px 12px rgba(61, 233, 250, 0.6) !important;
                }
        }
}

// 全局分页样式
.el-pagination {
        --el-pagination-text-color: #E6F4FF !important;
        --el-pagination-bg-color: transparent !important;
        --el-pagination-border-radius: 6px !important;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;

        .el-pager {
                li {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                        border: 1px solid rgba(61, 233, 250, 0.3) !important;
                        border-radius: 6px !important;
                        color: #E6F4FF !important;
                        margin: 0 2px !important;
                        backdrop-filter: blur(4px) !important;
                        transition: all 0.3s ease !important;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;

                        &:hover {
                                background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                                border-color: rgba(61, 233, 250, 0.5) !important;
                                box-shadow: 0 0 8px rgba(61, 233, 250, 0.2) !important;
                                transform: translateY(-1px) !important;
                        }

                        &.is-active {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                                border-color: $themeColor !important;
                                color: #FFFFFF !important;
                                box-shadow: 0 0 12px rgba(61, 233, 250, 0.4) !important;
                                font-weight: 600 !important;
                        }
                }
        }

        .btn-prev,
        .btn-next {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-radius: 6px !important;
                color: #E6F4FF !important;
                backdrop-filter: blur(4px) !important;
                transition: all 0.3s ease !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%) !important;
                        border-color: rgba(61, 233, 250, 0.5) !important;
                        box-shadow: 0 0 8px rgba(61, 233, 250, 0.2) !important;
                        color: $themeColor !important;
                }

                &:disabled {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%) !important;
                        border-color: rgba(61, 233, 250, 0.1) !important;
                        color: #6a8fbd !important;
                        opacity: 0.6 !important;
                }
        }

        .el-pagination__total,
        .el-pagination__jump {
                color: #E6F4FF !important;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        }
}

// 全局弹窗样式
.el-dialog {
        background: linear-gradient(135deg, rgba(9, 24, 34, 0.95) 0%, rgba(16, 52, 87, 0.9) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 12px !important;
        backdrop-filter: blur(12px) !important;
        box-shadow: 0 16px 64px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;

        .el-dialog__header {
                background: linear-gradient(135deg, rgba(6, 30, 54, 0.9) 0%, rgba(12, 45, 78, 0.8) 100%) !important;
                border-bottom: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-radius: 12px 12px 0 0 !important;
                padding: 16px 20px !important;

                .el-dialog__title {
                        color: #E6F4FF !important;
                        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
                        font-size: 18px !important;
                        font-weight: 600 !important;
                        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
                }

                .el-dialog__headerbtn {
                        .el-dialog__close {
                                color: #829CBD !important;
                                font-size: 20px !important;
                                transition: all 0.3s ease !important;
                                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;

                                &:hover {
                                        color: $themeColor !important;
                                        filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4)) !important;
                                        transform: scale(1.1) !important;
                                }
                        }
                }
        }

        .el-dialog__body {
                padding: 20px !important;
                color: #E6F4FF !important;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
        }

        .el-dialog__footer {
                border-top: 1px solid rgba(61, 233, 250, 0.2) !important;
                padding: 16px 20px !important;
                background: linear-gradient(135deg, rgba(6, 30, 54, 0.6) 0%, rgba(12, 45, 78, 0.4) 100%) !important;
                border-radius: 0 0 12px 12px !important;
        }
}

// 全局消息提示样式
.el-message {
        background: linear-gradient(135deg, rgba(9, 24, 34, 0.95) 0%, rgba(16, 52, 87, 0.9) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 8px !important;
        backdrop-filter: blur(12px) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        color: #E6F4FF !important;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;

        .el-message__icon {
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
        }

        &--success {
                border-color: rgba(39, 237, 187, 0.4) !important;

                .el-message__icon {
                        color: #27edbb !important;
                }
        }

        &--warning {
                border-color: rgba(227, 115, 27, 0.4) !important;

                .el-message__icon {
                        color: #e3731b !important;
                }
        }

        &--error {
                border-color: rgba(255, 71, 87, 0.4) !important;

                .el-message__icon {
                        color: #ff4757 !important;
                }
        }

        &--info {
                border-color: rgba(61, 233, 250, 0.4) !important;

                .el-message__icon {
                        color: $themeColor !important;
                }
        }
}

// 全局提示框样式
.el-tooltip__popper {
        background: linear-gradient(135deg, rgba(9, 24, 34, 0.95) 0%, rgba(16, 52, 87, 0.9) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 6px !important;
        backdrop-filter: blur(8px) !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4) !important;
        color: #E6F4FF !important;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC' !important;
        font-size: 12px !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        padding: 8px 12px !important;

        .el-tooltip__arrow {
                &::before {
                        border-color: rgba(61, 233, 250, 0.3) !important;
                }
        }
}

.lxxj {
        .el-form-item__label {
                float: none;
        }
}


.content {
        .page {
                height: 40px;

                .el-pagination__total {
                        font-size: 14px;
                        font-family: 'Alibaba-PuHuiTi',
                                'PingFang SC';
                        font-weight: 400;
                        color: #fff;

                }

                .el-pagination.is-background .btn-next,
                .el-pagination.is-background .btn-prev,
                .el-pagination.is-background .el-pager li {
                        background-color: rgba(255, 255, 255, 0);
                        border: 1px solid #34373a;
                        color: #fff;
                }

                .el-pagination.is-background .el-pager li:not(.disabled).active {
                        background-color: transparent;
                        color: rgba(61, 233, 250, 0.5);
                }
        }





        .el-collapse {
                border-bottom: none;
                border-top: none;
        }

        .el-collapse-item__header {
                border-bottom: none;
                line-height: 20px;
                background-color: transparent;
        }

        .el-collapse-item__wrap {
                background-color: transparent;
                border-bottom: none;
        }

        .el-collapse-item__arrow {
                color: #fff;
        }
}

.el-step__head.is-success {
        color: #3de9fa !important;
        border-color: #3de9fa !important;

        .el-step__icon {
                background: #3de9fa;
                color: #05090D !important;
        }
}

.el-step__head.is-process,
.el-step__head.is-wait {
        border-color: #616D94 !important;
        color: #9CA4B7 !important;

        .el-step__icon {
                background: rgba(97, 109, 148, 0.5);
                font-size: 24px;
                font-family: 'PingFangSC-Medium',
                        'PingFang SC';
                font-weight: 500;
        }
}

.el-step__title.is-process {
        color: #9CA4B7 !important;
}

.el-step__line {
        width: 86%;
        left: 50px !important;
        top: 20px !important;
        background-color: rgba(255, 255, 255, 0.4) !important;
}

.el-step__title {
        font-size: 12px !important;
}

.el-step__icon {
        width: 40px !important;
        height: 40px !important;
}

.el-steps {
        justify-content: center;
}


.el-step__title.is-success {
        color: #3de9fa !important;
        font-family: 'Alibaba-PuHuiTi',
                'PingFang SC';
        font-weight: 400;
}

.layout-header {
        .el-badge__content--danger {
                background-color: #B4061A;
                border: none;
        }
}

.el-form-item__label {
        color: #c7dfff;
}

.plan_container {
        .fc-theme-standard td {
                border: 1px solid rgba(240, 240, 240, 0.2) !important;
        }

        .fc-theme-standard th,
        .fc-theme-standard .fc-scrollgrid {
                border: none !important
        }

        .fc-col-header {
                height: 48px !important;
                line-height: 48px !important;
                background: rgba(255, 255, 255, 0.03);
                box-shadow: 0px 1px 0px 0px rgba(240, 240, 240, 0.2);
        }

        .fc .fc-button {
                padding: 0;
        }

        .fc .fc-button-primary,
        .fc .fc-button-primary:focus,
        .fc .fc-button:focus {
                background-color: rgba(0, 0, 0, 0) !important;
                border-color: rgba(0, 0, 0, 0) !important;
                box-shadow: none !important;
        }

        .fc-toolbar-chunk {
                display: flex;

                div {
                        display: flex;
                        width: 350px;
                        justify-content: space-between;
                }
        }

        .fc .fc-toolbar-title {
                font-size: 18px;
                font-weight: 500;
        }
}

.scrollbar {
        .el-scrollbar__view {
                height: 100%;
                overflow-x: hidden;
        }
}

.el-message-box {
        background-color: #091822 !important;
        border: 1px solid rgba(228, 240, 255, 0.1) !important;
}

.el-message-box__title {
        font-size: 16px !important;
        font-family: 'PingFangSC-Medium',
                'PingFang SC';
        font-weight: 500;
        color: #fff !important;
}

// 已在全局 el-date-picker 样式中统一处理

.el-message-box__message p {
        font-size: 14px;
        font-family: 'Alibaba-PuHuiTi',
                'PingFang SC';
        font-weight: 400;
        color: #fff;
}

.pageForm {
        .el-form-item {
                display: inline-block;
                width: 35%;

                .el-form-item__content .el-textarea {
                        width: 86%;
                }
        }
}

.form {
        .el-form-item__label {
                color: #fff;
        }

        .el-cascader {
                display: block !important;
        }

        .el-input__inner,
        .el-textarea__inner,
        .el-input.is-disabled .el-input__inner,
        .el-textarea.is-disabled .el-textarea__inner {
                background-color: rgba(47, 54, 60, 0.3);
                color: #fff;
                border: 1px solid #2F363C;
        }

        .el-input.is-disabled .el-input__inner {
                background-color: rgba(47, 54, 60, 0.3);
        }

        .el-upload--picture-card {
                background-color: transparent;
                width: 100px;
                height: 100px;
                line-height: 100px;
        }
}

.search_sub {
        .el-form-item--small.el-form-item {
                margin-bottom: 0px;
                vertical-align: middle !important;
        }

        // 已在全局 el-date-picker 样式中统一处理

        .el-range-editor--small .el-range-input {
                background-color: transparent;
        }

        .el-input__inner {
                background-color: rgba(68, 114, 141, 0.1);
        }

        .el-date-editor {
                --el-input-bg-color: rgba(68, 114, 141, 0.1);

                .el-input__wrapper {
                        background-color: rgba(68, 114, 141, 0.1) !important;
                        border: 1px solid #829CBD;

                        &:hover {
                                border-color: $themeColor;
                                background-color: rgba(68, 114, 141, 0.2) !important;
                        }

                        &.is-focus {
                                border-color: $themeColor;
                                background-color: rgba(68, 114, 141, 0.2) !important;
                                box-shadow: 0 0 0 1px rgba(61, 233, 250, 0.1);
                        }
                }

                .el-input__inner,
                .el-range-input {
                        color: #c7dfff !important;

                        &::placeholder {
                                color: #6A8FBD;
                        }
                }

                .el-range-separator {
                        color: #829CBD !important;
                }
        }

        // 已在全局 el-select 样式中统一处理

}



// 已在全局 el-date-picker 样式中统一处理

.el-picker-panel {
        .el-input {
                --el-input-bg-color: transparent !important;
                --el-input-border-color: transparent
        }

        .el-button {
                --el-button-bg-color: rgba(61, 233, 250, .5);
                --el-button-border-color: #3de9fa;
                --el-button-text-color: white
        }
}

.cascader {
        background: rgba(47, 54, 60, 0.8) !important;
        border: 1px solid #2F363C !important;

        .el-cascader-node__label {
                color: #fff;
        }

        .el-cascader-menu {
                border-right: 1px solid #2F363C;
        }

        .el-radio__inner {
                background-color: rgba(47, 54, 60, 0.3) !important;
                border: 1px solid #889CC3;
        }

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover {
                background: transparent;
        }
}



.loginForm {
        .el-input__inner:hover {
                border-color: #2F363C;
        }

        input:-webkit-autofill,
        textarea:-webkit-autofill,
        select:-webkit-autofill {
                -webkit-box-shadow: 0 0 0px 1000px rgba(47, 54, 60, 0.8) inset; //我这里的body背景色是#f8f8f8f
                -webkit-text-fill-color: #fff !important;
        }

        .el-form-item.is-error .el-input__inner {
                border-color: rgba(228, 240, 255, 0.1);
        }
}


.el-table {
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%) !important;
        backdrop-filter: blur(8px) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        font-family: "Alibaba-PuHuiTi", "PingFang SC" !important;

        // 表头样式
        .el-table__header-wrapper {
                background: linear-gradient(135deg, rgba(6, 30, 54, 0.9) 0%, rgba(12, 45, 78, 0.8) 100%) !important;
                border-radius: 12px 12px 0 0 !important;

                .el-table__header {
                        background: transparent !important;

                        th {
                                background: transparent !important;
                                border-bottom: 1px solid rgba(61, 233, 250, 0.3) !important;
                                border-right: 1px solid rgba(61, 233, 250, 0.2) !important;
                                color: #E6F4FF !important;
                                font-size: 14px !important;
                                font-weight: 500 !important;
                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
                                padding: 12px 8px !important;
                                position: relative !important;

                                &:last-child {
                                        border-right: none !important;
                                }

                                &::before {
                                        content: '';
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                        right: 0;
                                        bottom: 0;
                                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.05) 0%, transparent 50%);
                                        opacity: 0;
                                        transition: opacity 0.3s ease;
                                }

                                &:hover::before {
                                        opacity: 1;
                                }

                                .cell {
                                        color: #E6F4FF !important;
                                        font-weight: 500 !important;
                                }
                        }
                }
        }

        --el-table-border-color:transparent !important;
}

// 强制覆盖表格头部样式
.el-table th.el-table__cell {
        background: transparent !important;
        border-bottom: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-right: 1px solid rgba(61, 233, 250, 0.2) !important;
        color: #E6F4FF !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        padding: 12px 8px !important;
        position: relative !important;

        &:last-child {
                border-right: none !important;
        }

        .cell {
                color: #E6F4FF !important;
                font-weight: 500 !important;
        }

        &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.05) 0%, transparent 50%);
                opacity: 0;
                transition: opacity 0.3s ease;
        }

        &:hover::before {
                opacity: 1;
        }
}

// 表格头部容器强制样式
.el-table .el-table__header-wrapper {
        background: linear-gradient(135deg, rgba(6, 30, 54, 0.9) 0%, rgba(12, 45, 78, 0.8) 100%) !important;
        border-radius: 12px 12px 0 0 !important;

        .el-table__header {
                background: transparent !important;

                thead {
                        background: transparent !important;

                        tr {
                                background: transparent !important;
                        }
                }
        }

        // 表体样式
        .el-table__body-wrapper {
                background: transparent !important;

                .el-table__body {
                        background: transparent !important;

                        tr {
                                background: transparent !important;
                                transition: all 0.3s ease !important;
                                border-bottom: 1px solid rgba(61, 233, 250, 0.1) !important;

                                td {
                                        background: transparent !important;
                                        border-right: 1px solid rgba(61, 233, 250, 0.1) !important;
                                        color: #c7dfff !important;
                                        font-size: 14px !important;
                                        padding: 12px 8px !important;
                                        position: relative !important;

                                        &:last-child {
                                                border-right: none !important;
                                        }

                                        .cell {
                                                color: #c7dfff !important;
                                                line-height: 1.5 !important;
                                        }
                                }

                                // 悬停效果
                                &:hover {
                                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%) !important;
                                        transform: translateY(-1px) !important;
                                        box-shadow: 0 4px 16px rgba(61, 233, 250, 0.15) !important;

                                        td {
                                                background: transparent !important;
                                                color: #E6F4FF !important;

                                                .cell {
                                                        color: #E6F4FF !important;
                                                }
                                        }
                                }

                                // 当前行样式
                                &.current-row {
                                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%) !important;
                                        border-left: 3px solid #3de9fa !important;

                                        td {
                                                background: transparent !important;
                                                color: #3de9fa !important;

                                                .cell {
                                                        color: #3de9fa !important;
                                                        font-weight: 500 !important;
                                                }
                                        }
                                }
                        }
                }
        }

        // 固定列样式
        .el-table__fixed,
        .el-table__fixed-right {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%) !important;
                backdrop-filter: blur(8px) !important;
                border-right: 1px solid rgba(61, 233, 250, 0.3) !important;
                box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2) !important;

                &::before {
                        background: transparent !important;
                }
        }

        .el-table__fixed-right {
                border-left: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-right: none !important;
                box-shadow: -2px 0 8px rgba(0, 0, 0, 0.2) !important;
        }
}


// 表格选择框样式
.el-table {
   .el-checkbox {
         .el-checkbox__input {
                        .el-checkbox__inner {
                                background: rgba(2, 20, 36, 0.8) !important;
                                border: 1px solid rgba(61, 233, 250, 0.4) !important;
                                border-radius: 4px !important;
                                transition: all 0.3s ease !important;

                                &:hover {
                                        border-color: rgba(61, 233, 250, 0.6) !important;
                                        box-shadow: 0 0 6px rgba(61, 233, 250, 0.3) !important;
                                }

                                &::after {
                                        border-color: #E6F4FF !important;
                                        border-width: 2px !important;
                                }
                        }

                        &.is-checked .el-checkbox__inner {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.8) 0%, rgba(17, 150, 252, 0.6) 100%) !important;
                                border-color: #3de9fa !important;
                                box-shadow: 0 0 8px rgba(61, 233, 250, 0.4) !important;
                        }

                        &.is-indeterminate .el-checkbox__inner {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.6) 0%, rgba(17, 150, 252, 0.4) 100%) !important;
                                border-color: #3de9fa !important;

                                &::before {
                                        background: #E6F4FF !important;
                                }
                        }
                }
        }
}

// 表格排序样式
.el-table {
        th {
                .caret-wrapper {
                        .sort-caret {
                                border-color: rgba(199, 223, 255, 0.4) !important;
                                transition: all 0.3s ease !important;

                                &.ascending {
                                        border-bottom-color: #3de9fa !important;
                                }

                                &.descending {
                                        border-top-color: #3de9fa !important;
                                }
                        }

                        &:hover .sort-caret {
                                border-color: rgba(61, 233, 250, 0.6) !important;
                        }
                }
        }
}

// 表格筛选样式
.el-table-filter {
        background: linear-gradient(135deg, rgba(6, 30, 54, 0.95) 0%, rgba(12, 45, 78, 0.9) 100%) !important;
        border: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-radius: 8px !important;
        backdrop-filter: blur(8px) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4) !important;

        .el-table-filter__list {
                background: transparent !important;

                .el-table-filter__list-item {
                        background: transparent !important;
                        color: #c7dfff !important;
                        font-family: "Alibaba-PuHuiTi" !important;
                        transition: all 0.3s ease !important;

                        &:hover {
                                background: rgba(61, 233, 250, 0.1) !important;
                                color: #E6F4FF !important;
                        }

                        &.is-active {
                                background: rgba(61, 233, 250, 0.2) !important;
                                color: #3de9fa !important;
                                font-weight: 500 !important;
                        }
                }
        }

        .el-table-filter__bottom {
                background: transparent !important;
                border-top: 1px solid rgba(61, 233, 250, 0.2) !important;

                button {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                        border: 1px solid rgba(61, 233, 250, 0.3) !important;
                        border-radius: 4px !important;
                        color: #c7dfff !important;
                        font-family: "Alibaba-PuHuiTi" !important;
                        transition: all 0.3s ease !important;

                        &:hover {
                                border-color: rgba(61, 233, 250, 0.6) !important;
                                color: #E6F4FF !important;
                                box-shadow: 0 2px 6px rgba(61, 233, 250, 0.2) !important;
                        }

                        &.is-default {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%) !important;
                                border-color: #3de9fa !important;
                                color: #E6F4FF !important;
                        }
                }
        }
}

// 表格展开行样式
.el-table {
        .el-table__expand-icon {
                color: #c7dfff !important;
                transition: all 0.3s ease !important;

                &:hover {
                        color: #3de9fa !important;
                        transform: scale(1.1) !important;
                }

                &.el-table__expand-icon--expanded {
                        color: #3de9fa !important;
                        transform: rotate(90deg) !important;
                }
        }

        .el-table__expanded-cell {
                background: linear-gradient(135deg, rgba(6, 30, 54, 0.6) 0%, rgba(12, 45, 78, 0.4) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.2) !important;
                border-radius: 8px !important;
                margin: 8px !important;
                padding: 16px !important;
                color: #c7dfff !important;
                font-family: "Alibaba-PuHuiTi" !important;
        }
}



.el-tabs__item {
        color: rgba(255, 255, 255, .5) !important;
}

.el-tabs__nav-wrap::after {
        height: 1px !important;
        background-color: #7289a0 !important;
}

.el-tabs__header {
        // background: rgba(68, 114, 141, 0.1);
        background: #103457 !important;
}

// 表格空数据样式
.el-table__empty-block {
        background: transparent !important;

        .el-table__empty-text {
                color: #7A9BBD !important;
                font-size: 14px !important;
                font-family: "Alibaba-PuHuiTi" !important;
        }
}

// 表格加载样式
.el-table__loading-wrapper {
        background: rgba(16, 52, 87, 0.8) !important;
        backdrop-filter: blur(4px) !important;

        .el-loading-spinner {
                .el-loading-text {
                        color: #c7dfff !important;
                        font-family: "Alibaba-PuHuiTi" !important;
                }

                .circular {
                        stroke: #3de9fa !important;
                }
        }
}

// 表格按钮样式
.el-table {
        .el-button {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-radius: 6px !important;
                color: #c7dfff !important;
                font-size: 12px !important;
                padding: 4px 12px !important;
                transition: all 0.3s ease !important;
                position: relative !important;
                overflow: hidden !important;

                &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%);
                        opacity: 0;
                        transition: opacity 0.3s ease;
                }

                &:hover {
                        border-color: rgba(61, 233, 250, 0.6) !important;
                        color: #E6F4FF !important;
                        box-shadow: 0 2px 8px rgba(61, 233, 250, 0.2) !important;
                        transform: translateY(-1px) !important;

                        &::before {
                                opacity: 1;
                        }
                }

                &.el-button--text {
                        background: transparent !important;
                        border: none !important;
                        color: #3de9fa !important;
                        padding: 4px 8px !important;

                        &:hover {
                                background: rgba(61, 233, 250, 0.1) !important;
                                color: #E6F4FF !important;
                                box-shadow: none !important;
                                transform: none !important;
                        }
                }

                &.el-button--primary {
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%) !important;
                        border-color: #3de9fa !important;
                        color: #E6F4FF !important;

                        &:hover {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.5) 0%, rgba(17, 150, 252, 0.3) 100%) !important;
                                box-shadow: 0 4px 12px rgba(61, 233, 250, 0.3) !important;
                        }
                }
        }
}

// 分页组件美化
.el-pagination {
        background: transparent !important;
        padding: 12px 0 !important;

        .el-pagination__total {
                color: #c7dfff !important;
                font-family: "Alibaba-PuHuiTi" !important;
                font-size: 14px !important;
        }

        .el-pager {
                li {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%) !important;
                        border: 1px solid rgba(61, 233, 250, 0.3) !important;
                        border-radius: 6px !important;
                        color: #c7dfff !important;
                        margin: 0 4px !important;
                        transition: all 0.3s ease !important;
                        font-family: "Alibaba-PuHuiTi" !important;

                        &:hover {
                                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%) !important;
                                border-color: rgba(61, 233, 250, 0.6) !important;
                                color: #E6F4FF !important;
                                transform: translateY(-1px) !important;
                                box-shadow: 0 2px 8px rgba(61, 233, 250, 0.2) !important;
                        }

                        &.is-active {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%) !important;
                                border-color: #3de9fa !important;
                                color: #E6F4FF !important;
                                font-weight: 500 !important;
                                box-shadow: 0 0 12px rgba(61, 233, 250, 0.4) !important;
                        }
                }
        }

        .btn-prev,
        .btn-next {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.4) 0%, rgba(24, 64, 104, 0.3) 100%) !important;
                border: 1px solid rgba(61, 233, 250, 0.3) !important;
                border-radius: 6px !important;
                color: #c7dfff !important;
                margin: 0 4px !important;
                transition: all 0.3s ease !important;

                &:hover {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%) !important;
                        border-color: rgba(61, 233, 250, 0.6) !important;
                        color: #E6F4FF !important;
                        transform: translateY(-1px) !important;
                        box-shadow: 0 2px 8px rgba(61, 233, 250, 0.2) !important;
                }

                &:disabled {
                        background: rgba(16, 52, 87, 0.2) !important;
                        border-color: rgba(61, 233, 250, 0.1) !important;
                        color: rgba(199, 223, 255, 0.3) !important;
                        cursor: not-allowed !important;

                        &:hover {
                                transform: none !important;
                                box-shadow: none !important;
                        }
                }
        }

        .el-pagination__jump {
                color: #c7dfff !important;
                font-family: "Alibaba-PuHuiTi" !important;

                .el-input__inner {
                        background: rgba(2, 20, 36, 0.8) !important;
                        border: 1px solid rgba(61, 233, 250, 0.4) !important;
                        border-radius: 6px !important;
                        color: #c7dfff !important;
                        text-align: center !important;

                        &:focus {
                                border-color: #3de9fa !important;
                                box-shadow: 0 0 8px rgba(61, 233, 250, 0.3) !important;
                        }
                }
        }

        .el-pagination__sizes {
                .el-select {
                        .el-input__inner {
                                background: rgba(2, 20, 36, 0.8) !important;
                                border: 1px solid rgba(61, 233, 250, 0.4) !important;
                                border-radius: 6px !important;
                                color: #c7dfff !important;
                        }
                }
        }
}

// 额外的表格头部样式强制覆盖 - 最高优先级
.el-table__header th,
.el-table__header-wrapper th,
.el-table thead th,
.el-table thead th.el-table__cell,
.el-table .el-table__header th.el-table__cell,
.el-table .el-table__header-wrapper .el-table__header th.el-table__cell {
        background: transparent !important;
        background-color: transparent !important;
        border-bottom: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-right: 1px solid rgba(61, 233, 250, 0.2) !important;
        color: #E6F4FF !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;

        .cell {
                color: #E6F4FF !important;
                font-weight: 500 !important;
        }

        &:last-child {
                border-right: none !important;
        }
}

// 表格头部容器背景强制覆盖
.el-table .el-table__header-wrapper,
.el-table__header-wrapper {
        background: linear-gradient(135deg, rgba(6, 30, 54, 0.9) 0%, rgba(12, 45, 78, 0.8) 100%) !important;
        border-radius: 12px 12px 0 0 !important;
}

// 最强优先级的表格头部样式覆盖
th.el-table__cell {
        background: transparent !important;
        background-color: transparent !important;
        color: #E6F4FF !important;
        font-weight: 500 !important;
        border-bottom: 1px solid rgba(61, 233, 250, 0.3) !important;
        border-right: 1px solid rgba(61, 233, 250, 0.2) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

th.el-table__cell .cell {
        color: #E6F4FF !important;
        font-weight: 500 !important;
}

// 确保表格头部文字颜色
.el-table th,
.el-table th.el-table__cell,
.el-table .el-table__header th,
.el-table .el-table__header th.el-table__cell {
        color: #E6F4FF !important;

        * {
                color: #E6F4FF !important;
        }
}

// 强制覆盖表格行背景样式
.el-table tr,
.el-table tbody tr,
.el-table .el-table__body tr,
.el-table .el-table__body-wrapper tr,
.el-table__body-wrapper .el-table__body tr {
        background: transparent !important;
        background-color: transparent !important;
        transition: all 0.3s ease !important;
        border-bottom: 1px solid rgba(61, 233, 250, 0.1) !important;

        td {
                background: transparent !important;
                background-color: transparent !important;
                color: #c7dfff !important;
                border-right: 1px solid rgba(61, 233, 250, 0.1) !important;

                .cell {
                        color: #c7dfff !important;
                }
        }

        // 悬停效果
        &:hover {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%) !important;
                background-color: transparent !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 16px rgba(61, 233, 250, 0.15) !important;

                td {
                        background: transparent !important;
                        background-color: transparent !important;
                        color: #E6F4FF !important;

                        .cell {
                                color: #E6F4FF !important;
                        }
                }
        }

        // 当前行样式
        &.current-row {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%) !important;
                background-color: transparent !important;
                border-left: 3px solid #3de9fa !important;

                td {
                        background: transparent !important;
                        background-color: transparent !important;
                        color: #3de9fa !important;

                        .cell {
                                color: #3de9fa !important;
                                font-weight: 500 !important;
                        }
                }
        }

        // 斑马纹行样式
        &.el-table__row--striped {
                background: rgba(16, 52, 87, 0.2) !important;
                background-color: rgba(16, 52, 87, 0.2) !important;

                td {
                        background: transparent !important;
                        background-color: transparent !important;
                }
        }
}

// 最强优先级的表格行样式覆盖
tr.el-table__row {
        background: transparent !important;
        background-color: transparent !important;

        &:hover {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.5) 100%) !important;
                background-color: transparent !important;
        }

        &.current-row {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%) !important;
                background-color: transparent !important;
        }

        &.el-table__row--striped {
                background: rgba(16, 52, 87, 0.2) !important;
                background-color: rgba(16, 52, 87, 0.2) !important;
        }
}

// 表格单元格强制样式
td.el-table__cell {
        background: transparent !important;
        background-color: transparent !important;
        color: #c7dfff !important;

        .cell {
                color: #c7dfff !important;
        }
}

// 表格体容器强制透明
.el-table__body-wrapper,
.el-table__body {
        background: transparent !important;
        background-color: transparent !important;
}

.el-tabs__item.is-active {
        color: #c7dfff !important;
        background: linear-gradient(180deg,
                        rgba(199, 223, 255, 0) 0%,
                        rgba(199, 223, 255, 0.3) 100%);
        box-shadow: 0px 1px 0px 0px #c7dfff;
}

.el-tabs__active-bar {
        height: 0 !important;
}

.el-tabs--bottom .el-tabs__item.is-bottom:last-child,
.el-tabs--bottom .el-tabs__item.is-top:last-child,
.el-tabs--top .el-tabs__item.is-bottom:last-child,
.el-tabs--top .el-tabs__item.is-top:last-child {
        padding-right: 20px !important;
}

.el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
.el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
.el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
        padding-left: 20px !important;
}

.card-body {
        .el-timeline-item__tail {
                left: 5px !important;
                top: 13px;
                // height: 76% !important;
                border-left: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .el-timeline-item__node {
                background-color: rgba(47, 54, 60, 0.3) !important;
                border: 1px solid #889CC3;
        }

        .el-textarea__inner {
                font-size: 12px;
                font-family: 'Alibaba-PuHuiTi',
                        'PingFang SC';
                font-weight: 400;
                color: #fff;
        }
}


.el-timeline-item__tail {
        left: 5px !important;
        top: 5px;
        height: 80% !important;
        border-left: 1px solid #505356 !important;
}



.el-radio-button__inner {
        background: rgba(47, 54, 60, 0.3) !important;
        border: 1px solid #4a5966 !important;
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: rgba(27, 192, 237, 0.3) !important;
        border: 1px solid #1bc0ed !important;
}



.tree {
        .el-tree {
                background: rgba(255, 255, 255, 0) !important;
        }

        .el-checkbox__inner,
        .el-input__inner {
                background-color: rgba(255, 255, 255, 0) !important;
        }
}

.el-tree-node__content {
        font-size: 16px;
        color: #fff;
}

.el-tree-node__content:hover {
        background-color: rgba(255, 255, 255, 0) !important;
}

.el-tree-node:focus>.el-tree-node__content {
        background-color: rgba(255, 255, 255, 0) !important;
        color: #13d4d9;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        background-color: #66b1ff87;
}

.el-month-table td.in-range div,
.el-month-table td.in-range div:hover {
        background-color: #091822 !important;
}

.form-inline {
        .el-date-editor .el-range__icon {
                margin-left: 0;
        }

        .el-range-editor.el-input__inner {
                padding: 0;
        }

        .el-input__inner {
                height: 32px;
        }

        .el-form-item__label {
                color: #fff;
        }

        .el-form-item,
        .el-form-item--small.el-form-item,
        .el-form-item--mini.el-form-item {
                margin-bottom: 0px;
        }
}

.el-range-editor .el-range-input {
        font-size: 14px;
        color: #6A8FBD !important;
}

.el-input.is-disabled .el-input__inner {
        font-size: 14px !important;
        border: 1px solid #2F363C !important;
}

.search_box {
        display: flex;
        align-items: center;
        height: 50px;

        .el-form-item__label {
                align-items: center;
                font-size: 14px;
        }


        .el-form-item {
                margin-bottom: 5px !important;
        }


        .el-form-item__label {
                color: $whiteColor;
        }

        .el-form--inline .el-form-item {
                vertical-align: middle;
        }

        .searchBtn {
                background: #1B6DC2;
                border-radius: 4px;
                padding: 5px 15px;
                box-shadow: 0px 0px 3px 3px #2CBDEB inset;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 500;
                font-style: italic;
                color: #FFFFFF;
                cursor: pointer;
        }

        .delBtn {
                background: #CC2E2E;
                border-radius: 4px;
                padding: 5px 15px;
                box-shadow: 0px 0px 3px 3px #FF9696 inset;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 500;
                font-style: italic;
                color: #FFFFFF;
                cursor: pointer;
                margin-left: 15px;
        }

        .updateBtn {
                background: #3de9fa;
                border-radius: 4px;
                padding: 5px 15px;
                box-shadow: 0px 0px 3px 3px #3de9fa inset;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 500;
                font-style: italic;
                color: #FFFFFF;
                cursor: pointer;
                margin-left: 15px;
        }

        .el-input__wrapper {
                font-family: "Alibaba-PuHuiTi";
                font-size: 14px;
                font-weight: 400;
                color: $whiteColor !important;
                background-color: #1A395F !important;
                box-shadow: none;
        }

        .el-date-editor {
                --el-input-border-color: #829CBD !important;
                --el-input-hover-border: #829CBD !important;
                --el-input-focus-border: #829CBD !important;
                --el-input-focus-border-color: #829CBD !important;
                --el-input-hover-border-color: #829CBD !important;

                .el-input__wrapper {
                        background-color: #1A395F !important;
                        border: 1px solid #829CBD;

                        &:hover {
                                border-color: $themeColor !important;
                        }

                        &.is-focus {
                                border-color: $themeColor !important;
                                box-shadow: 0 0 0 1px rgba(61, 233, 250, 0.1);
                        }
                }

                .el-input__inner,
                .el-range-input {
                        font-family: "Alibaba-PuHuiTi";
                        font-size: 14px;
                        font-weight: 400;
                        color: $whiteColor !important;

                        &::placeholder {
                                color: #6A8FBD;
                        }
                }

                .el-range-separator {
                        color: #829CBD !important;
                }

                .el-input__prefix,
                .el-input__suffix {

                        .el-input__prefix-inner,
                        .el-input__suffix-inner {
                                color: #829CBD;

                                .el-icon {
                                        color: #829CBD;
                                }
                        }
                }
        }


        // 已在全局 el-select 样式中统一处理

        .el-form-item__content {
                line-height: unset !important;
        }


}

.control {
        .el-radio__inner {
                background: rgba(255, 255, 255, 0.5);
                border: 1px solid #fff;
        }

        .el-radio__input.is-checked .el-radio__inner {
                border-color: #fff;
                background: #13d4d9;
        }
}

//个人中心设置
.info {
        .el-form-item__label {
                color: rgba(199, 223, 255, 1);
                font-size: 14px;
        }

        .el-input__inner {
                background: rgba(47, 54, 60, 0.3);
                border: 1px solid #2f363c;
        }

        .el-radio-group {
                width: 100%;
        }

        .el-form-item.is-error .el-input__inner,
        .el-form-item.is-error .el-input__inner:focus,
        .el-form-item.is-error .el-textarea__inner,
        .el-form-item.is-error .el-textarea__inner:focus,
        .el-message-box__input div.invalid>input,
        .el-message-box__input div.invalid>input:focus {
                border-color: unset;
        }
}

.el-cascader-menu {
        min-width: 150px !important;
}

.navCascader .el-cascader-menu__wrap {
        height: 100% !important;
}


.select_panel {

        .el-cascader-node:not(.is-disabled):focus,
        .el-cascader-node:not(.is-disabled):hover {
                // background: url("@/assets/images/menu3.png") no-repeat;
                background-size: cover;
        }

        .el-cascader-node {
                color: #fff !important;
                text-align: center;
                // background: url("@/assets/images/menu2.png") no-repeat;
                background-size: cover;
        }

        .el-cascader-menu {
                border-right: none;
        }

        .el-cascader-node__postfix,
        .el-cascader-node__prefix {
                display: none;
        }
}






.el-cascader__dropdown.el-popper .el-popper__arrow::before {
        border-bottom-color: rgba(255, 255, 255, 0) !important;
        border-right-color: rgba(255, 255, 255, 0) !important;
}

.el-popper__arrow {
        display: none !important;
}

// 已在全局 el-date-picker 样式中统一处理

.el-popper,
.el-dropdown__popper.el-popper {
        border: none !important;
}

// 日期选择器底部
.el-picker-panel__footer {
        border-top: 1px solid #2F363C !important;
        background-color: transparent !important;
        padding: 8px 12px;

        .el-button--default,
        .el-button.is-plain:hover,
        .el-button.is-disabled.is-plain,
        .el-button.is-disabled.is-plain:focus,
        .el-button.is-disabled.is-plain:hover {
                background: rgba(61, 233, 250, 0.2);
                border-color: $themeColor;
                color: $themeColor;
                font-size: 14px;

                &:hover {
                        background: rgba(61, 233, 250, 0.3);
                        color: $whiteColor;
                }
        }

        .el-button--text {
                color: #829CBD;

                &:hover {
                        color: $themeColor;
                }
        }

        .el-button--primary {
                background: rgba(61, 233, 250, 0.8);
                border-color: $themeColor;
                color: $whiteColor;

                &:hover {
                        background: $themeColor;
                }
        }
}

// 时间选择器样式
.el-time-panel {
        background: rgba(9, 24, 34, 0.95) !important;
        border: 1px solid #2F363C !important;
        backdrop-filter: blur(8px);

        .el-time-panel__content {
                .el-time-spinner__item {
                        color: #c7dfff;

                        &:hover {
                                background-color: rgba(16, 52, 87, 0.6);
                                color: $themeColor;
                        }

                        &.is-active {
                                background-color: rgba(61, 233, 250, 0.2);
                                color: $themeColor;
                                font-weight: 500;
                        }
                }
        }
}

// 范围选择器特殊样式
.el-date-range-picker__content.is-left {
        border-right: 1px solid #2F363C !important;
}

.el-date-range-picker__time-header {
        border-bottom: 1px solid #2F363C !important;
        color: #c7dfff;
}

// 已在全局 el-date-picker 样式中统一处理

// 日期选择器面板样式
.el-picker-panel {
        background: rgba(9, 24, 34, 0.95) !important;
        border: 1px solid #2F363C !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
        backdrop-filter: blur(8px);
        border-radius: 6px;

        .el-input__inner {
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                font-weight: 400;
                color: #c7dfff;
                background-color: rgba(26, 57, 95, 0.6);
                border: 1px solid #2F363C;
                font-size: 14px;

                &:hover {
                        border-color: #829CBD;
                }

                &:focus {
                        border-color: $themeColor;
                }
        }

        // 面板头部
        .el-picker-panel__header {
                color: #c7dfff;
                border-bottom: 1px solid #2F363C;

                .el-picker-panel__icon-btn {
                        color: #829CBD !important;

                        &:hover {
                                color: $themeColor !important;
                        }
                }
        }

        // 日期表格
        .el-date-table {
                th {
                        color: #6A8FBD;
                        border-bottom: 1px solid #2F363C !important;
                }

                td {
                        &.available:hover {
                                color: $themeColor;
                        }

                        &.today .cell {
                                color: $themeColor;
                                font-weight: 500;
                        }

                        &.current:not(.disabled) .cell {
                                background-color: rgba(61, 233, 250, 0.2);
                                color: $themeColor;
                        }

                        &.in-range .cell {
                                background-color: rgba(16, 52, 87, 0.4);
                                color: #c7dfff;
                        }

                        &.start-date .cell,
                        &.end-date .cell {
                                background-color: rgba(61, 233, 250, 0.3);
                                color: $themeColor;
                        }

                        .cell {
                                color: #c7dfff;

                                &:hover {
                                        background-color: rgba(16, 52, 87, 0.6);
                                        color: $themeColor;
                                }
                        }
                }
        }

        // 月份表格
        .el-month-table {
                td .cell {
                        color: #c7dfff !important;

                        &:hover {
                                color: $themeColor !important;
                        }
                }

                td.current:not(.disabled) .cell {
                        color: $themeColor !important;
                        background-color: rgba(61, 233, 250, 0.2);
                }
        }

        // 年份表格
        .el-year-table {
                td .cell {
                        color: #c7dfff;

                        &:hover {
                                color: $themeColor;
                        }
                }

                td.current:not(.disabled) .cell {
                        color: $themeColor;
                        background-color: rgba(61, 233, 250, 0.2);
                }
        }
}

// 已在全局 el-date-picker 样式中统一处理

.el-dropdown-menu,
.el-menu {
        background-color: #091822 !important;
        color: #fff;
}

.el-dropdown-menu__item:hover {
        background-color: #282D34 !important;
        color: #fff;
}

// 告警容器特殊样式
.alarm_container {
        .el-table__body tr.current-row>td {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.25) 0%, rgba(17, 150, 252, 0.15) 100%) !important;
                color: #3de9fa !important;
                border-left: 3px solid #3de9fa !important;

                .cell {
                        color: #3de9fa !important;
                        font-weight: 500 !important;
                }
        }

        .el-table {
                .el-table__body tr:hover td {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.7) 0%, rgba(24, 64, 104, 0.6) 100%) !important;

                        .cell {
                                color: #E6F4FF !important;
                        }
                }
        }
}

.work {
        .el-checkbox__inner {
                background: transparent;
                border: 1px solid;
        }
}

.progress-data {
        .el-textarea__inner {
                background: rgba(47, 54, 60, 0.3);
                border: 1px solid #2f363c;
        }
}





.cctv-dialog {
        .el-dialog__body {
                padding: 10px !important;
                // background: #091822 !important;
                // opacity: 1 !important;
        }

        .el-dialog__footer {
                font-size: 12px;
                // background: #091822 !important;
        }

        .el-dialog__title {
                color: #fff;
        }

}

.nav_container {
        .el-input__inner {
                background-color: transparent;
                border: none;
                color: #fff;
                text-align: center;
        }
}

.left_search .el-input__inner {
        background-color: rgba(97, 109, 148, 0.2);
        border: none;
        font-size: 14px;
        font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
        font-weight: 400;
        color: #fff;
}

.el-input-group__append,
.el-input-group__prepend {
        background-color: rgba(47, 54, 60, 0.3) !important;
        border: 1px solid #2F363C !important;
}

.el-page-header {
        margin-bottom: 10px;
}

.el-page-header,
.el-page-header__content {
        color: #fff !important;
}
















//------------el-slider-------------------
.el-slider__bar {
        background: linear-gradient(-90deg, #1E79F2, #1CD3DE);

}

.el-slider__runway {
        background: #2D435E !important;

}

.el-switch__label.is-active,
.el-switch__label {
        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: bold;
        font-style: italic;
        color: #FFFFFF;
}

//------------el-dailog-------------------
.el-dialog {
        --el-dialog-bg-color: #0C1B26 !important;
}



.el-dialog__header {

        margin-right: 0 !important;


}

.el-dialog__headerbtn .el-dialog__close {
        color: white !important;
}



.el-dialog__title {
        font-size: 20px !important;
        font-family: "Alibaba-PuHuiTi" !important;
        font-weight: bold !important;
        font-style: italic !important;
        color: #FFFFFF !important;
        margin-left: 10px;
        margin-top: 10px;
}

.el-dialog__body,
.el-dialog__footer {
        // background: #0A1B29;

        .el-pagination {
                --el-fill-color-blank: transparent !important;
                --el-pagination-text-color: #5C7992 !important;
                --el-color-primary: #ffffff !important;
        }

        // opacity: 0.8;
}


//------------el-pagination-------------------
.el-pagination {
        --el-fill-color-blank: transparent !important;
        --el-pagination-text-color: #5C7992 !important;
        --el-color-primary: #ffffff !important;
}

//------------el-date-picker-------------------

// 全局 el-date-picker 样式统一 - 参考 .left 和 .right 面板配色
.el-date-editor {
        width: 100%;

        .el-input__wrapper {
                // 参考 .left 面板的输入框样式
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                border: 1px solid rgba(61, 233, 250, 0.3);
                border-radius: 8px;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(4px);
                transition: all 0.3s ease;

                &:hover {
                        border-color: rgba(61, 233, 250, 0.5);
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        box-shadow: 0 0 8px rgba(61, 233, 250, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                &.is-focus {
                        border-color: $themeColor;
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4), inset 0 2px 4px rgba(0, 0, 0, 0.2);
                }
        }

        .el-input__inner {
                color: #E6F4FF;
                background-color: transparent;
                font-size: 14px;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                font-weight: 400;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

                &::placeholder {
                        color: #7A9BBD;
                        font-size: 14px;
                }
        }

        .el-input__prefix,
        .el-input__suffix {
                .el-input__prefix-inner,
                .el-input__suffix-inner {
                        color: #829CBD;
                        transition: color 0.3s ease;

                        .el-icon {
                                color: #829CBD;
                                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
                                transition: all 0.3s ease;

                                &:hover {
                                        color: $themeColor;
                                        filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                                }
                        }
                }
        }

        // 范围选择器样式
        &.el-range-editor {
                .el-range-input {
                        color: #E6F4FF;
                        background-color: transparent;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

                        &::placeholder {
                                color: #7A9BBD;
                        }
                }

                .el-range-separator {
                        color: #829CBD;
                        font-size: 14px;
                        font-weight: 500;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }

                .el-range__icon {
                        color: #829CBD;
                        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
                }

                .el-range__close-icon {
                        color: #829CBD;
                        transition: all 0.3s ease;

                        &:hover {
                                color: $themeColor;
                                filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                        }
                }
        }

        // 禁用状态
        &.is-disabled {
                .el-input__wrapper {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
                        border-color: rgba(61, 233, 250, 0.1);
                        cursor: not-allowed;
                        opacity: 0.6;
                }

                .el-input__inner,
                .el-range-input {
                        color: #6a8fbd;
                        cursor: not-allowed;
                }
        }
}

// el-date-picker 下拉面板样式 - 与 el-select 保持一致
.el-picker-panel {
        background: linear-gradient(135deg, rgba(9, 24, 34, 0.95) 0%, rgba(16, 52, 87, 0.9) 100%);
        border: 1px solid rgba(61, 233, 250, 0.3);
        backdrop-filter: blur(12px);
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
        color: #E6F4FF;

        .el-picker-panel__body {
                .el-picker-panel__content {
                        .el-date-table {
                                th {
                                        color: #829CBD;
                                        font-weight: 500;
                                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                                        border-bottom: 1px solid rgba(61, 233, 250, 0.2);
                                }

                                td {
                                        &.available:hover {
                                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
                                                border-radius: 4px;
                                        }

                                        &.current {
                                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
                                                border-radius: 4px;

                                                .cell {
                                                        color: $themeColor;
                                                        font-weight: 600;
                                                        text-shadow: 0 1px 3px rgba(61, 233, 250, 0.5);
                                                }
                                        }

                                        .cell {
                                                color: #E6F4FF;
                                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                                                transition: all 0.3s ease;
                                        }
                                }
                        }
                }
        }

        .el-picker-panel__header {
                border-bottom: 1px solid rgba(61, 233, 250, 0.2);
                padding: 12px;

                .el-picker-panel__icon-btn {
                        color: #829CBD;
                        transition: all 0.3s ease;
                        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

                        &:hover {
                                color: $themeColor;
                                filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                        }
                }
        }

        .el-picker-panel__footer {
                border-top: 1px solid rgba(61, 233, 250, 0.2);
                padding: 8px 12px;

                .el-button {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                        border: 1px solid rgba(61, 233, 250, 0.3);
                        color: #E6F4FF;
                        transition: all 0.3s ease;

                        &:hover {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
                                border-color: $themeColor;
                                box-shadow: 0 2px 8px rgba(61, 233, 250, 0.2);
                        }
                }
        }
}

//------------el-select-------------------

// 全局 el-select 样式统一 - 参考 .left 和 .right 面板配色
.el-select {
        width: 100%;
        min-width: 160px; // 设置最小宽度

        // el-select 的包装器样式
        .el-select__wrapper {
                // 与 el-date-picker 保持一致的样式
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                border: 1px solid rgba(61, 233, 250, 0.3);
                border-radius: 8px;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(4px);
                transition: all 0.3s ease;

                &:hover {
                        border-color: rgba(61, 233, 250, 0.5);
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        box-shadow: 0 0 8px rgba(61, 233, 250, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                &.is-focused {
                        border-color: $themeColor;
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4), inset 0 2px 4px rgba(0, 0, 0, 0.2);
                }
        }

        // 兼容 el-input__wrapper 的样式
        .el-input__wrapper {
                background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
                border: 1px solid rgba(61, 233, 250, 0.3);
                border-radius: 8px;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(4px);
                transition: all 0.3s ease;

                &:hover {
                        border-color: rgba(61, 233, 250, 0.5);
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        box-shadow: 0 0 8px rgba(61, 233, 250, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                &.is-focus {
                        border-color: $themeColor;
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        box-shadow: 0 0 12px rgba(61, 233, 250, 0.4), inset 0 2px 4px rgba(0, 0, 0, 0.2);
                }
        }

        .el-input__inner {
                color: #E6F4FF;
                background-color: transparent;
                font-size: 14px;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                font-weight: 400;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

                &::placeholder {
                        color: #7A9BBD;
                        font-size: 14px;
                }
        }

        // el-select 特有的输入和选择样式
        .el-select__input-wrapper {
                .el-select__input {
                        color: #E6F4FF;
                        background-color: transparent;
                        font-size: 14px;
                        font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                        font-weight: 400;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

                        &::placeholder {
                                color: #7A9BBD;
                                font-size: 14px;
                        }
                }
        }

        .el-select__selection {
                color: #E6F4FF;
                font-size: 14px;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                font-weight: 400;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .el-select__placeholder {
                color: #7A9BBD;
                font-size: 14px;
        }

        // el-select 后缀样式（箭头、清除按钮等）
        .el-select__suffix {
                color: #829CBD;

                .el-select__caret {
                        color: #829CBD;
                        transition: all 0.3s ease;
                        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

                        &.is-reverse {
                                transform: rotateZ(180deg);
                        }

                        &:hover {
                                color: $themeColor;
                                filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                        }
                }

                .el-select__clear {
                        color: #829CBD;
                        transition: all 0.3s ease;
                        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

                        &:hover {
                                color: $themeColor;
                                filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                        }
                }
        }

        // 兼容 el-input__suffix 的样式
        .el-input__suffix {
                .el-input__suffix-inner {
                        .el-select__caret {
                                color: #829CBD;
                                transition: all 0.3s ease;
                                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

                                &.is-reverse {
                                        transform: rotateZ(180deg);
                                }

                                &:hover {
                                        color: $themeColor;
                                        filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                                }
                        }

                        .el-input__clear {
                                color: #829CBD;
                                transition: all 0.3s ease;
                                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

                                &:hover {
                                        color: $themeColor;
                                        filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                                }
                        }
                }
        }

        // 多选模式样式 - 参考 .left 和 .right 面板配色
        &.el-select--multiple {
                .el-select__tags {
                        .el-tag {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
                                border: 1px solid rgba(61, 233, 250, 0.4);
                                border-radius: 4px;
                                color: #E6F4FF;
                                font-size: 12px;
                                font-weight: 500;
                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                                backdrop-filter: blur(2px);
                                transition: all 0.3s ease;

                                &:hover {
                                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
                                        border-color: $themeColor;
                                        box-shadow: 0 2px 6px rgba(61, 233, 250, 0.2);
                                }

                                .el-tag__close {
                                        color: #829CBD;
                                        transition: all 0.3s ease;
                                        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));

                                        &:hover {
                                                color: $themeColor;
                                                filter: drop-shadow(0 1px 4px rgba(61, 233, 250, 0.4));
                                        }
                                }
                        }
                }
        }

        // 禁用状态
        &.is-disabled {
                .el-select__wrapper,
                .el-input__wrapper {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.3) 0%, rgba(24, 64, 104, 0.2) 100%);
                        border-color: rgba(61, 233, 250, 0.1);
                        cursor: not-allowed;
                        opacity: 0.6;
                }

                .el-select__selection,
                .el-input__inner {
                        color: #6a8fbd;
                        cursor: not-allowed;
                }

                .el-select__suffix,
                .el-input__suffix {
                        .el-select__caret {
                                color: #6a8fbd;
                                filter: none;
                        }

                        .el-input__suffix-inner {
                                .el-select__caret {
                                        color: #6a8fbd;
                                        filter: none;
                                }
                        }
                }
        }
}

// 内联表单中的组件特殊处理
.el-form--inline {
        .el-form-item {
                .el-select {
                        width: 160px !important; // 内联表单中设置固定宽度
                        min-width: 160px;
                }

                .el-date-editor {
                        width: 280px !important; // 日期范围选择器需要更宽
                        min-width: 280px;
                }
        }
}

// el-select 下拉选项样式 - 参考 .left 和 .right 面板配色
.el-select-dropdown {
        // 参考 .left 和 .right 面板的毛玻璃效果
        background: linear-gradient(135deg, rgba(9, 24, 34, 0.95) 0%, rgba(16, 52, 87, 0.9) 100%);
        border: 1px solid rgba(61, 233, 250, 0.3);
        backdrop-filter: blur(12px);
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
        overflow: hidden;

        .el-popper__arrow {
                display: none;
        }

        .el-select-dropdown__item {
                color: #E6F4FF;
                background-color: transparent;
                font-size: 14px;
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
                font-weight: 400;
                padding: 0 16px;
                height: 36px;
                line-height: 36px;
                transition: all 0.3s ease;
                position: relative;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

                &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.1) 0%, transparent 50%);
                        opacity: 0;
                        transition: opacity 0.3s ease;
                }

                &:hover {
                        background: linear-gradient(135deg, rgba(16, 52, 87, 0.8) 0%, rgba(24, 64, 104, 0.6) 100%);
                        color: #E6F4FF;
                        border-left: 3px solid rgba(61, 233, 250, 0.6);
                        padding-left: 13px;

                        &::before {
                                opacity: 1;
                        }
                }

                &.selected {
                        background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.1) 100%);
                        color: $themeColor;
                        font-weight: 500;
                        border-left: 3px solid $themeColor;
                        padding-left: 13px;
                        text-shadow: 0 1px 3px rgba(61, 233, 250, 0.5);

                        &:hover {
                                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.2) 100%);
                                box-shadow: 0 2px 8px rgba(61, 233, 250, 0.2);
                        }
                }

                &.is-disabled {
                        color: #6a8fbd;
                        cursor: not-allowed;
                        background-color: transparent;
                        opacity: 0.5;

                        &:hover {
                                background-color: transparent;
                                border-left: none;
                                padding-left: 16px;
                        }
                }
        }

        .el-select-dropdown__empty {
                color: #7A9BBD;
                font-size: 14px;
                text-align: center;
                padding: 24px 0;
                font-style: italic;
        }

        // 分组选项样式
        .el-select-group {
                .el-select-group__title {
                        color: #829CBD;
                        font-size: 12px;
                        font-weight: 600;
                        padding: 12px 16px 4px;
                        line-height: 20px;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                        border-bottom: 1px solid rgba(61, 233, 250, 0.2);
                        margin-bottom: 4px;
                }

                .el-select-group__wrap {
                        .el-select-dropdown__item {
                                padding-left: 28px;

                                &:hover {
                                        padding-left: 25px;
                                }

                                &.selected {
                                        padding-left: 25px;
                                }
                        }
                }
        }
}

//------------el-cascader-------------------
.el-cascader {
        width: 100%;
}


.el-dialog {
        background-image: url('@/assets/images/dialog/bg.png') !important;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
}

.el-dialog__header {
        background-image: url('@/assets/images/dialog/bg_title.png') !important;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
        background-position-y: 5px;
}


.el-image-viewer__mask:focus-visible {
        outline: none !important;
}

.el-image-viewer__wrapper:focus-visible {
        outline: none !important;
}