import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/views/Layout/index.vue'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: Layout,
      redirect: '/overview',
      children: [
       
        {
          path: 'overview',
          name: 'overview',
          component: () => import('@/views/overview/index.vue'),
          meta: {
            title: '总览',
            noCache: true,
          },
        },
        {
          path: 'home',
          name: 'home',
          component: () => import('@/views/overview/index.vue'),
        }
       
      ],
    },
    {
      path: "/login",
      name: "Login",
      component: () => import("@/views/Login/Login.vue"),
    },
    {
      path: "/404",
      name: "404",
      component: () => import("@/views/404.vue"),
    },
    {
      path: "/:catchAll(.*)", //重定向
      redirect: "/404",
    },
  ],
})

export default router
