import request from '@/utils/request'




export function getHistroyRecord(params: any) {
    return request({
        url: '/rtdb-service/alarm-history',
        method: 'get',
        params,
    })
}

export function getHistoryAnalysis(params: any) {
    return request({
        url: '/rtdb-service/alarm-history/analysis',
        method: 'get',
        params,
    })
}


export function getAlarmById(params: any) {
    return request({
        url: '/rtdb-service/alarm-history/info',
        method: 'get',
        params,
    })
}

// 一周统计
export function getAlarmWeek(){
    return request({
        url:'/rtdb-service/alarm-history/week',
        method:'get'
    })
}


export function getDeviceAlarm(params:any){
    return request({
        url:'/alarm-service/alarm-history/device',
        method:'get',
        params
    })
}


export function getSourceAlarm(params:any){
    return request({
        url:'/alarm-service/alarm-history/source',
        method:'get',
        params
    })
}

