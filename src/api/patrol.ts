import request from '@/utils/request'
import qs from 'qs'


//查询巡检项目
export function getPatrolContent(params: any) {
  return request({
    url: '/maintenance-service/patrol-content',
    method: 'get',
    params
  })
}

//添加巡检项目
export function addContent(data: any) {
  return request({
    url: '/maintenance-service/patrol-content',
    method: 'post',
    data,
  })
}

//编辑巡检项目

export function editPatrolContent(data: any) {
  return request({
    url: '/maintenance-service/patrol-content',
    method: 'put',
    data
  })
}

//删除巡检项目
export function deletePatrolContent(params: any) {
  return request({
    url: '/maintenance-service/patrol-content',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


//----------巡检点------------------------------

//查询巡检点
export function getPatrolPoint(params: any) {
  return request({
    url: '/acs-service/nova/point',
    method: 'get',
    params
  })
}

//添加巡检点
export function addPoint(data: any) {
  return request({
    url: '/maintenance-service/patrol-point',
    method: 'post',
    data,
  })
}

//编辑巡检点

export function editPatrolPoint(data: any) {
  return request({
    url: '/maintenance-service/patrol-point',
    method: 'put',
    data
  })
}

//删除巡检点
export function deletePatrolPoint(params: any) {
  return request({
    url: '/maintenance-service/patrol-point',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


//-----------巡检线路----------------------
//查询巡检线路
export function getPatrolLine(params: any) {
  return request({
    url: '/acs-service/nova/line',
    method: 'get',
    params
  })
}

//添加巡检线路
export function addLine(data: any) {
  return request({
    url: '/maintenance-service/patrol-line',
    method: 'post',
    data,
  })
}

//编辑巡检线路

export function editPatrolLine(data: any) {
  return request({
    url: '/maintenance-service/patrol-line',
    method: 'put',
    data
  })
}

//删除巡检线路
export function deletePatrolLine(params: any) {
  return request({
    url: '/maintenance-service/patrol-line',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}





//-----------巡检计划----------------------
//查询巡检计划
export function getPatrolPlan(params: any) {
  return request({
    url: '/acs-service/nova/plan',
    method: 'get',
    params
  })
}

//添加巡检计划
export function addPlan(data: any) {
  return request({
    url: '/maintenance-service/patrol-plan',
    method: 'post',
    data,
  })
}

//编辑巡检计划

export function editPatrolPlan(data: any) {
  return request({
    url: '/maintenance-service/patrol-plan',
    method: 'put',
    data
  })
}

//删除巡检计划
export function deletePatrolPlan(params: any) {
  return request({
    url: '/maintenance-service/patrol-plan',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}






//-----------巡检计划事件----------------------


//添加巡检计划事件
export function addPlanEvent(data: any) {
  return request({
    url: '/maintenance-service/plan-event',
    method: 'post',
    data,
  })
}

//编辑巡检计划事件

export function editPatrolPlanEvent(data: any) {
  return request({
    url: '/maintenance-service/plan-event',
    method: 'put',
    data
  })
}

//删除巡检计划
export function deletePatrolPlanEvent(params: any) {
  return request({
    url: '/maintenance-service/plan-event',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}





// ------------------------巡检记录--------------------------
export function getPatrolRecord(params: any) {
  return request({
    url: '/maintenance-service/patrol-record',
    method: 'get',
    params
  })
}



export function getPatrolTask(params: any) {
  return request({
    url: '/acs-service/nova/task',
    method: 'get',
    params,
    // paramsSerializer: (params) => {
    //   return qs.stringify(params, { arrayFormat: 'repeat' });
    // }
  })
}

export function getPatrolProcess(params: any) {
  return request({
    url: '/maintenance-service/patrol-task/process',
    method: 'get',
    params,

  })
}



export function processTask(data: any) {
  return request({
    url: '/maintenance-service/deploy',
    method: 'put',
    data
  })
}



//----------巡检标准-----------------------
export function getPatrolEvent(params:any) {
  return request({
    url: '/maintenance-service/patrol-event',
    method: 'get',
    params
  })
}

//添加巡检线路
export function addPatrolEvent(data:any) {
  return request({
    url: '/maintenance-service/patrol-event',
    method: 'post',
    data,
  })
}

//编辑巡检线路

export function editPatrolEvent(data:any) {
  return request({
    url: '/maintenance-service/patrol-event',
    method: 'put',
    data
  })
}

//删除巡检线路
export function deletePatrolEvent(params:any) {
  return request({
    url: '/maintenance-service/patrol-event',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


//-----------安保巡检计划事件----------------------


//添加巡检计划事件
export function addSecurityPlanEvent(data:any) {
  return request({
    url: '/maintenance-service/security-plan-event',
    method: 'post',
    data,
  })
}

//编辑巡检计划事件

export function editSecurityPlanEvent(data:any) {
  return request({
    url: '/maintenance-service/security-plan-event',
    method: 'put',
    data
  })
}

//删除巡检计划
export function deleteSecurityPlanEvent(params:any) {
  return request({
    url: '/maintenance-service/security-plan-event',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}





//-----------安保巡检计划----------------------
//查询巡检计划
export function getSecurityPlan(params:any) {
  return request({
    url: '/acs-service/nova/plan',
    method: 'get',
    params
  })
}

//添加巡检计划
export function addSecurityPlan(data:any) {
  return request({
    url: '/maintenance-service/security-patrol-plan',
    method: 'post',
    data,
  })
}

//编辑巡检计划

export function editSecurityPlan(data:any) {
  return request({
    url: '/maintenance-service/security-patrol-plan',
    method: 'put',
    data
  })
}

//删除巡检计划
export function deleteSecurityPlan(params:any) {
  return request({
    url: '/maintenance-service/security-patrol-plan',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


export function getSecurityDetailRecordDown(params:any) {
  return request({
    url: '/maintenance-service/patrol-record/security-detail-down',
    method: 'post',
    params,
    responseType: 'blob'
  })
}

export function getSecurityDetailRecord(params:any) {
  return request({
    url: '/maintenance-service/patrol-record/security-detail',
    method: 'get',
    params
  })
}

export function getDeviceSecurityDetailRecord(params:any) {
  return request({
    url: '/maintenance-service/patrol-record/security-detail-device',
    method: 'get',
    params
  })
}

export function getSecurityRecord(params:any) {
  return request({
    url: '/maintenance-service/patrol-record/security',
    method: 'get',
    params
  })
}



export function getSecurityStatics(params:any) {
  return request({
    url: '/maintenance-service/patrol-record/security-statics',
    method: 'get',
    params
  })
}
