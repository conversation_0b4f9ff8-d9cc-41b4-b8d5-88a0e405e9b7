import request from '@/utils/request'
//危化品系统


export function GetsProductLibrary(params: any) {
    return request({
      url: '/acs-service/beans/GetsProductLibrary',
      method: 'get',
      params
    })
  }




export function GetsReagentCabinet(params: any) {
    return request({
      url: '/acs-service/beans/GetsReagentCabinet',
      method: 'get',
      params
    })
  }
  export function GetsCabinetWarningRecords(params: any) {
    return request({
      url: '/acs-service/beans/GetsCabinetWarningRecords',
      method: 'get',
      params
    })
  }
  
  export function GetsMaterials(params: any) {
    return request({
      url: '/acs-service/beans/GetsMaterials',
      method: 'get',
      params
    })
  }


  export function GetsMaterialRecord(params: any) {
    return request({
      url: '/acs-service/beans/GetsMaterialRecord',
      method: 'get',
      params
    })
  }


  export function GetMaterialOutInRecord(params: any) {
    return request({
      url: '/acs-service/beans/GetMaterialOutInRecord',
      method: 'get',
      params
    })
  }
  


  export function getAIList(params: any) {
    return request({
      url: '/acs-service/ai/event/list',
      method: 'get',
      params
    })
  }


  export function getAICategory(params: any) {
    return request({
      url: '/acs-service/ai/category',
      method: 'get',
      params
    })
  }

  export function getAIfb(params: any) {
    return request({
      url: '/acs-service/ai/event/fb',
      method: 'get',
      params
    })
  }



  export function getAItrend(params: any) {
    return request({
      url: '/acs-service/ai/event/trend',
      method: 'get',
      params
    })
  }



  export function getAIWeek(params: any) {
    return request({
      url: '/acs-service/ai/week',
      method: 'get',
      params
    })
  }
  
  export function getMeeting(params: any) {
    return request({
      url: '/acs-service/df/getRoomList',
      method: 'get',
      params
    })
  }
  