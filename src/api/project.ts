import request from '@/utils/request'
import qs from 'qs'
export function getProject(params: any) {
  return request({
    url: '/basicinfo-service/project',
    method: 'get',
    params
  })
}


export function getProjectById(id: any) {
  return request({
    url: `/basicinfo-service/project/${id}`,
    method: 'get',
  })
}



export function addProject(data: any) {
  return request({
    url: '/basicinfo-service/project',
    method: 'post',
    data,

  })
}

export function editProject(data: any) {
  return request({
    url: '/basicinfo-service/project',
    method: 'put',
    data
  })
}



export function deleteProject(params: any) {
  return request({
    url: '/basicinfo-service/project',
    method: 'delete',
    params,
    paramsSerializer: (params: any) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}
// 查询自己加入的项目
export function getInviteProject(params: any) {
  return request({
    url: '/basicinfo-service/project/invite',
    method: 'get',
    params
  })
}

// 查询自己创建的项目
export function getSelfProject(params: any) {
  return request({
    url: '/basicinfo-service/project/self',
    method: 'get',
    params
  })
}
// 退出项目
export function removeProject(params: any) {
  return request({
    url: '/basicinfo-service/project/remove',
    method: 'delete',
    params
  })
}

export function getDefaultProject() {
  return request({
    url: '/basicinfo-service/project/default',
    method: 'get'
  })
}






