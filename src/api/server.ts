import request from '@/utils/request'
import qs from 'qs'


export function getServer() {
  return request({
    url: '/rtdb-service/api/io',
    method: 'get',
  })
}

export function getChannal(io: any) {
  return request({
    url: `/rtdb-service/api/${io}/channal`,
    method: 'get',
  })
}


export function getController(io: any, channal: any) {
  return request({
    url: `/rtdb-service/api/${io}/${channal}/controller`,
    method: 'get',
  })
}

export function getVariable(io: any, channal: any, controller: any, variable: any) {
  return request({
    url: `/rtdb-service/api/${io}/${channal}/${controller}/${variable}`,
    method: 'get',
  })
}



export function getHistroyData(params: any) {
  return request({
    url: `/rtdb-service/alarm-history`,
    method: 'get',
    params
  })
}


export function getVariableData(server: any, chal: any, control: any, variable: any) {
  return request({
    url: `/rtdb-service/api/${server}/${chal}/${control}/${variable}`,
    method: 'get',
  })
}
