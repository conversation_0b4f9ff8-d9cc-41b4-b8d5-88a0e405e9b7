import request from '@/utils/request'
import qs from 'qs'


//设备基础信息  设备指标  设备扩展属性
export function getDevices(params: any) {
        return request({
                url: '/resource-service/device',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                        return qs.stringify(params, { arrayFormat: 'repeat' });
                }
        })
}

export function getBasicDevices(params: any) {
        return request({
                url: '/resource-service/device/basic',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                        return qs.stringify(params, { arrayFormat: 'repeat' });
                }
        })
}

//左侧设备列表，查询设备指标参数
export function getDevicesStd(params: any) {
        return request({
                url: '/resource-service/device/standard-page',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                        return qs.stringify(params, { arrayFormat: 'repeat' });
                }
        })
}

export function getDevicesStdById(params: any) {
        return request({
                url: '/resource-service/device/standard',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                        return qs.stringify(params, { arrayFormat: 'repeat' });
                }
        })
}

//设备基础信息
export function getDeviceInfo(params: any) {
        return request({
                url: '/resource-service/device/info',
                method: 'get',
                params
        })
}
export function getDevicesOfType(params: any) {
        return request({
                url: '/resource-service/deviceType/device',
                method: 'get',
                params,
        })
}

export function getDeviceStandard(params: any) {
        return request({
                url: '/resource-service/device-standard-param/device',
                method: 'get',
                params
        })
}

// 获取设备类型树
export function getDeviceTypeTree(params: any) {
        return request({
                url: '/resource-service/deviceType/tree',
                method: 'get',
                params
        })
}


export function getProjectDeviceType(params: any) {
        return request({
                url: '/resource-service/deviceType',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                        return qs.stringify(params, { arrayFormat: 'repeat' });
                }
        })
}



export function getDeviceHealthData(params: any) {
        return request({
                url: '/resource-service/device/device-statistics',
                method: 'get',
                params
        })
}

// 左侧查询设备只带指标
export function getDeviceStandardList(params: any) {
        return request({
                url: '/resource-service/device/standard',
                method: 'get',
                params
        })
}

export function getDeviceTypeStatus(params: any) {
        return request({
                url: '/resource-service/report/all-status',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                        return qs.stringify(params, { arrayFormat: 'repeat' });
                }
        })
}


export function getMaintenanceDevice(params: any) {
        return request({
                url: '/resource-service/device/maintenance',
                method: 'get',
                params
        })
}

export function getDeviceCount1(params: any) {
        return request({
                url: '/resource-service/report/device-count',
                method: 'get',
                params
        })
    }

    export function getDeviceLog(params:any) {
        return request({
          url: '/rtdb-service/runLog/all',
          method: 'get',
          params,
         
        })
      }
