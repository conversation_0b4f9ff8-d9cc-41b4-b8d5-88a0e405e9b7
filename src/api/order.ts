import request from '@/utils/request'
export function getOrder(data: any) {
    return request({
        url: '/maintenance-service/order',
        method: 'post',
        data,
    })
}

export function getOrderProcess(params: any) {
    return request({
        url: '/maintenance-service/order/process',
        method: 'get',
        params
    })
}


export function getProcessLog(params: any) {
    return request({
        url: '/maintenance-service/process-log',
        method: 'get',
        params
    })
}

export function orderStatistic(params: any) {
    return request({
        url: '/maintenance-service/order/statistics',
        method: 'get',
        params
    })
}

export function inspectionStatustic(params: any) {
    return request({
        url: '/maintenance-service/patrol-task/statistics',
        method: 'get',
        params
    })
}
export function addOrder(data: any){
    return request({
        url:'/maintenance-service/order/order',
        method: 'post',
        data
    })
}