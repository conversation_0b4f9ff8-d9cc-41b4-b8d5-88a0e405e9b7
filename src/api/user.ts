import request from '@/utils/request'
import qs from 'qs'

export function login(data: { username: any; password: any; params: any;code?:any }) {
  return request({
    url: '/auth-service/login',
    method: 'post',
    data
  })
}

export function refreshToken(params: { refreshTokenValue: any }) {
  return request({
    url: '/auth-service/refresh',
    method: 'post',
    params,
    headers: {}
  })
}

export function logout(params: any) {
  return request({
    url: '/user-service/user/loginOut',
    method: 'get',
    params
  })
}


export function getUser(params: any) {
  return request({
    url: '/user-service/user',
    method: 'get',
    params,
    paramsSerializer: (params: any) => {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  })
}



export function getUserInfo(params:any) {
  return request({
    url: `/user-service/user/one`,
    params,
    method: 'get',
  })
}

export function register(data: any) {
  return request({
    url: '/user-service/user/register',
    method: 'post',
    data
  })
}

export function getInvited(data: any) {
  return request({
    url: '/user-service/invite/info',
    method: 'get',
    data
  })
}

export function agreeInvite(params: any) {
  return request({
    url: '/user-service/invite/agree',
    method: 'get',
    params
  })
}

export function resetPass(params: any) {
  return request({
    url: '/user-service/user/reset',
    method: 'get',
    params
  })
}


export function updateUser(data: any) {
  return request({
    url: '/user-service/user',
    method: 'put',
    data
  })
}

// 退出登录
export function loginOut(params: any) {
  return request({
    url: '/user-service/user/loginOut',
    method: 'get',
    params
  })
}
// 同意邀请
export function agreeIvite(params: any) {
  return request({
    url: 'user-service/invite/agree',
    method: 'get',
    params
  })
}
// 邀请码验证
export function verificationCode(data: string) {
  return request({
    url: '/user-service/user/check-invite?code=' + data,
    method: 'get',
  })
}
