import request from '@/utils/request'
import qs from 'qs'


//查询维保项目
export function getContent(params: any) {
  return request({
    url: '/maintenance-service/maintenance-content',
    method: 'get',
    params
  })
}

//添加维保项目
export function addMaintenanceContent(data: any) {
  return request({
    url: '/maintenance-service/maintenance-content',
    method: 'post',
    data,
  })
}

//编辑维保内容
export function editMaintenanceContent(data: any) {
  return request({
    url: '/maintenance-service/maintenance-content',
    method: 'put',
    data
  })
}

//删除维保内容
export function deleteContent(params: any) {
  return request({
    url: '/maintenance-service/maintenance-content',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}



//-----------维保计划----------------------

//查询维保计划
export function getMainPlan(params: any) {
  return request({
    url: '/maintenance-service/plan',
    method: 'get',
    params
  })
}

//添加维保计划
export function addMainPlan(data: any) {
  return request({
    url: '/maintenance-service/plan',
    method: 'post',
    data,
  })
}

//编辑维保计划
export function editMainPlan(data: any) {
  return request({
    url: '/maintenance-service/plan',
    method: 'put',
    data
  })
}

//删除维保计划
export function deletePlan(params: any) {
  return request({
    url: '/maintenance-service/plan',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}

export function createOrderByAlarm(params: any) {
  return request({
    url: '/maintenance-service/order/alarm',
    method: 'get',
    params,
  })
}


//获取维保次数
export function getMaintenanceTime(params: any) {
  return request({
    url: '/maintenance-service/statistics/maintenance-time',
    method: 'get',
    params,
  })
}


//获取巡检数量
export function getPointCount(params: any) {
  return request({
    url: '/maintenance-service/statistics/count',
    method: 'get',
    params,
  })
}

//获取巡检任务统计
export function getTaskCount(params: any) {
  return request({
    url: '/acs-service/nova/task/count',
    method: 'get',
    params,
  })
}

export function getPlanCount(params: any) {
  return request({
    url: '/acs-service/nova/plan/count',
    method: 'get',
    params,
  })
}


//获取概述中巡检记录
export function getOPatrolRecord(params: any) {
  return request({
    url: '/maintenance-service/statistics/patrol-record',
    method: 'get',
    params,
  })
}

//获取维保计划数量
export function getMCount(params: any) {
  return request({
    url: '/maintenance-service/statistics/maintenance-count',
    method: 'get',
    params,
  })
}

//获取维保任务统计
export function getMtask(params: any) {
  return request({
    url: '/maintenance-service/statistics/maintenance-task',
    method: 'get',
    params,
  })
}

//获取预期未维保设备
export function getMrecord(params: any) {
  return request({
    url: '/maintenance-service/statistics/maintenance-expire',
    method: 'get',
    params,
  })
}

//获取报修数量
export function getRepairCount(params: any) {
  return request({
    url: '/maintenance-service/statistics/repair-count',
    method: 'get',
    params,
  })
}

export function getRepairStatus(params: any) {
  return request({
    url: '/maintenance-service/statistics/repair-status',
    method: 'get',
    params,
  })
}

export function getRepairReal(params: any) {
  return request({
    url: '/maintenance-service/statistics/repair-real',
    method: 'get',
    params,
  })
}

export function getOrderToday(params: any) {
  return request({
    url: '/maintenance-service/statistics/order-today',
    method: 'get',
    params,
  })
}

export function getOrderTrend(params: any) {
  return request({
    url: '/maintenance-service/statistics/order-trend',
    method: 'get',
    params,
  })
}


export function getOrderWork(params: any) {
  return request({
    url: '/maintenance-service/statistics/order-work',
    method: 'get',
    params,
  })
}

export function getOrderHour(params: any) {
  return request({
    url: '/maintenance-service/statistics/order-hour',
    method: 'get',
    params,
  })
}

export function getOrderPend(params: any) {
  return request({
    url: '/maintenance-service/statistics/order-pending',
    method: 'get',
    params,
  })
}




export function getDeviceFault(params: any) {
  return request({
    url: '/maintenance-service/statistics/device-fault',
    method: 'get',
    params,
  })
}


export function getLeader(params: any) {
  return request({
    url: '/maintenance-service/staff/leader',
    method: 'get',
    params,
  })
}

//本周内待维保设备
export function getUnCompleted(params: any) {
  return request({
    url: '/maintenance-service/plan/uncompleted',
    method: 'get',
    params,
  })
}

//本周维保统计
export function getMainWeekCount(params: any) {
  return request({
    url: '/maintenance-service/plan/week',
    method: 'get',
    params,
  })
}



//本周维保统计
export function getFcRecord(params: any) {
  return request({
    url: '/maintenance-service/fc-record',
    method: 'get',
    params,
  })
}

export function getFcWeekCount(params: any) {
  return request({
    url: '/maintenance-service/fc-record/week',
    method: 'get',
    params,
  })
}


export function getOrderTypeCount(params: any) {
  return request({
    url: '/maintenance-service/statistics/order-type',
    method: 'get',
    params,
  })
}

