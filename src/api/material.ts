import request from '@/utils/request'
export function getMaterial(params: any) {
        return request({
                url: '/contingency-service/material',
                method: 'get',
                params
        })
}

// 物质分类统计,排名
export function getMaterialCount(params: any) {
        return request({
                url: '/contingency-service/material/count',
                method: 'get',
                params
        })
}


export function getMaterialType(params: any) {
        return request({
                url: '/contingency-service/material/type',
                method: 'get',
                params
        })
}

// 获取过期物资
export function getMaterialExpiration(params: any) {
        return request({
                url: '/contingency-service/material/expiration',
                method: 'get',
                params
        })
}

export function getAssetType(params: any) {
        return request({
                url: '/asset-service/info/type',
                method: 'get',
                params
        })
}

export function getAssetState(params: any) {
        return request({
                url: '/asset-service/info/state',
                method: 'get',
                params
        })
}


export function getAssetUse(params: any) {
        return request({
                url: '/asset-service/info/use',
                method: 'get',
                params
        })
}