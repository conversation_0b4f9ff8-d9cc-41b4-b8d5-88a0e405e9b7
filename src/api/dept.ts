import request from '@/utils/request'
import qs from 'qs'
export function getDepts(params: any) {
  return request({
    url: '/basicinfo-service/area/tree',
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}



export function getDept(params:any) {
    return request({
      url: '/basicinfo-service/dept',
      method: 'get',
      params,
      paramsSerializer:(params)=>{
         return qs.stringify(params,{arrayFormat:'repeat'});
      }
    })
  }

