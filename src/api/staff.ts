import request from '@/utils/request'
import qs from 'qs'
export function getProjectStaff(params: any) {
  return request({
    url: '/basicinfo-service/staff',
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}


export function addStaff(data: any) {
  return request({
    url: '/basicinfo-service/staff',
    method: 'post',
    data,

  })
}


export function editStaff(data: any) {
  return request({
    url: '/basicinfo-service/staff',
    method: 'put',
    data
  })
}


export function deleteStaff(params: any) {
  return request({
    url: '/basicinfo-service/staff',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}