import request from '@/utils/request'

// 查询报警主机
export function getAlarmDevice(params: any) {
  return request({
    url: '/hk-service/alarm/device',
    method: 'get',
    params
  })
}


// 查询子系统
export function getAlarmSub(params: any) {
  return request({
    url: '/hk-service/alarm/sub',
    method: 'get',
    params
  })
}

// 查询子系统状态
export function getAlarmSubStatus(params: any) {
  return request({
    url: '/hk-service/alarm/sub-status',
    method: 'get',
    params
  })
}

// 查询子系统防区
export function getDefence(params: any) {
  return request({
    url: '/hk-service/alarm/defence',
    method: 'get',
    params
  })
}

// 查询子系统防区
export function cmd(data: any) {
  return request({
    url: '/hk-service/alarm/defence',
    method: 'post',
    data
  })
}


export function getHKDoor(params: any) {
  return request({
    url: '/acs-service/hk/door',
    method: 'get',
    params
  })
}
export function getHKController(params: any) {
  return request({
    url: '/acs-service/hk/device',
    method: 'get',
    params
  })
}

export function getHKDoorEvent(params: any) {
  return request({
    url: '/acs-service/hk/event',
    method: 'get',
    params
  })
}


export function getHkTerminal(params: any) {
  return request({
    url: '/acs-service/hk/terminal',
    method: 'get',
    params
  })
}

export function hkTerminalCmd(data: any) {
  return request({
    url: '/acs-service/hk/cmd',
    method: 'post',
    data
  })
}


export function getHkProgram(params: any) {
  return request({
    url: '/acs-service/hk/program',
    method: 'get',
    params
  })
}
export function crossRecords(params: any) {
  return request({
    url: '/acs-service/hk/crossRecords',
    method: 'get',
    params
  })
}


export function vehicleList(params: any) {
  return request({
    url: '/acs-service/hk/vehicleList',
    method: 'get',
    params
  })
}












