import request from '@/utils/request'


//场景管理-----------------------------------
export function getSceneVar(params: any) {
    return request({
        url: '/scene-service/scene/var',
        method: 'get',
        params,
    })
}


export function writeGroup(data: any) {
    return request({
        url: '/ws-service/v1/group/write',
        method: 'post',
        data,
    })
}

export function getGroup(params: any) {
    return request({
        url: '/scene-service/group',
        method: 'get',
        params,
    })
}