import request from '@/utils/request'
import qs from 'qs'
export function getFileType(params: any) {
  return request({
    url: '/fileCenter-service/fileType/tree',
    method: 'get',
    params
  })
}

export function addFileType(data: any) {
  return request({
    url: '/fileCenter-service/fileType',
    method: 'post',
    data,

  })
}

export function editFileType(data: any) {
  return request({
    url: '/fileCenter-service/fileType',
    method: 'put',
    data
  })
}

export function deleteFileType(params: any) {
  return request({
    url: '/fileCenter-service/fileType',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}





export function getFile(params: any) {
  return request({
    url: '/bim-service/file',
    method: 'get',
    params
  })
}


export function deleteFile(params: any) {
  return request({
    url: '/bim-service/deleteFile',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}

export function addConfig(data: any) {
  return request({
    url: '/bim-service/config',
    method: 'post',
    data,
  })
}


export function editConfig(data: any) {
  return request({
    url: '/bim-service/config',
    method: 'put',
    data,
  })
}


export function getBIMConfig(params: any) {
  return request({
    url: '/bim-service/config',
    method: 'get',
    params
  })
}

export function getModelConfig(params: any) {
  return request({
    url: '/bim-service/model-config',
    method: 'get',
    params
  })
}

export function getModelCondition(params: any) {
  return request({
    url: '/bim-service/model-condition',
    method: 'get',
    params
  })
}


export function getLinkConfig(params: any) {
  return request({
    url: '/bim-service/config/link',
    method: 'get',
    params
  })
}




export function getDeviceConfig(params: any) {
  return request({
    url: '/bim-service/config/device',
    method: 'get',
    params
  })
}


export function getFileConfig(params: any) {
  return request({
    url: '/bim-service/file/config',
    method: 'get',
    params
  })

}

export function getObjectId(params: any) {
  return request({
    url: '/bim-service/config/objectId',
    method: 'get',
    params
  })
}

export function getCamObjectId(params: any) {
  return request({
    url: '/bim-service/config/cam',
    method: 'get',
    params
  })
}


