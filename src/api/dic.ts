import request from '@/utils/request'
import qs from 'qs'


export function getProjectDic(params: any) {
  return request({
    url: '/basicinfo-service/dic',
    method: 'get',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}

export function addDic(data: any) {
  return request({
    url: '/basicinfo-service/dic',
    method: 'post',
    data,

  })
}

export function editDic(data: any) {
  return request({
    url: '/basicinfo-service/dic',
    method: 'put',
    data
  })
}

export function deleteDic(params: any) {
  return request({
    url: '/basicinfo-service/dic',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}



export function getDicItem(params: any) {
  return request({
    url: '/basicinfo-service/dicItem',
    method: 'get',
    params
  })
}

export function addDicItem(data: any) {
  return request({
    url: '/basicinfo-service/dicItem',
    method: 'post',
    data,

  })
}


export function editDicItem(data: any) {
  return request({
    url: '/basicinfo-service/dicItem',
    method: 'put',
    data
  })
}

export function deleteDicItem(params: any) {
  return request({
    url: '/basicinfo-service/dicItem',
    method: 'delete',
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    }
  })
}





export function getDicType(params: any) {
  return request({
    url: '/basicinfo-service/dicType',
    method: 'get',
    params
  })
}

export function addDicType(data: any) {
  return request({
    url: '/basicinfo-service/dicType',
    method: 'post',
    data,

  })
}


export function editDicType(data: any) {
  return request({
    url: '/basicinfo-service/dicType',
    method: 'put',
    data
  })
}




export function getDicUtil(params: any) {
  return request({
    url: '/basicinfo-service/dicItem',
    method: 'get',
    params
  })
}

export function getDicDataUtil(params: any) {
  return request({
    url: '/basicinfo-service/dicItem',
    method: 'get',
    params
  });
}



export function getDicUtilR(params:any, prop:any, _this:any) {
  return  request({
     url: '/basicinfo-service/dicItem',
     method: 'get',
     params
   })
 }
 
