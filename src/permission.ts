import router from "./router";
import { ElMessage } from "element-plus";

import { getCookie,setCookie } from "@/utils/cookie"; // get token from cookie
import getPageTitle from "@/utils/get-page-title";
import { useAppStore } from "./stores/app";

const whiteList = ["/login", "/register", "/panel/door"]; // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  const store = useAppStore();
  document.title = getPageTitle(to.meta.title);

  const hasToken = getCookie("gh_token");
  const projectId = getCookie('gh_projectId');


  if (hasToken) {
    if (to.path === "/login") {
      if (projectId) {
        next({ path: "/home" });
      } else {
        next();
      }
    } else {
      const hasGetUserInfo = store.name;
      if (hasGetUserInfo) {
        next();
      } else {
        try {
          next();
        } catch (error) {
          await store.RESET_STATE()
          ElMessage.error(error || "Has Error");
          next(`/login?redirect=${to.path}`);
        }
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next();
    } else if (to.path.startsWith("/img")) {
      next();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`);
    }
  }
});
