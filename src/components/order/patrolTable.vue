<template>
  <div class="table h100">
    <el-table :height="tableHeight" :data="list"  fit table-layout="auto">
      <template #empty>
        <no-data />
      </template>
      <el-table-column prop="startTime" label="创建时间" align="center">
      </el-table-column>
      <el-table-column prop="name" label="名称" align="center">
      </el-table-column>
      <el-table-column prop="level" label="工单等级" align="center">
        <template #default="scope">
          <span v-if="scope.row.level == 1">一般</span>
          <span v-if="scope.row.level == 2">紧急</span>
          <span v-if="scope.row.level == 3">严重</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <span style="color: #13d4d9">{{ getStatus(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="staffType" label="所属专业" align="center">
        <template #default="scope">
          <span>{{ getStaffTypeName(scope.row.staffType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" class="editBtn" @click="edit(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="center page">
      <el-pagination :total="total" :page="page" :size="size" @pagination="handleCurrentChange" />
    </div>
    <order-dialog v-if="dialogData.visible" :dialogData="dialogData" />
  </div>
</template>

<script>
import orderDialog from '@/views/patrol/components/orderDialog.vue'
import {
  getCookie
} from '@/utils/cookie'
import {
  computed,
  
  onMounted,
  reactive,
  toRefs,
  watch
} from 'vue'
import {
  useRouter
} from 'vue-router'
import { useAppStore } from '@/stores/app'


export default {
  name: 'order-table',
  props: ['status'],
  components: {
    orderDialog
  },
  setup (props) {
    const { proxy } = getCurrentInstance()
    const router = useRouter()
    const store = useAppStore()
    const state = reactive({
      tableHeight: window.innerHeight * 0.60,
      page: 1,
      size: 10,
      total: 0,
      list: [],
      types: [],
      state: [{
        value: -1,
        name: '待派单',
      },
      {
        value: 1,
        name: '待接单',
      },
      {
        value: 2,
        name: '挂起',
      },
      {
        value: 3,
        name: '正常关闭',
      },
      {
        value: 4,
        name: '异常关闭',
      },
      {
        value: 5,
        name: '退回',
      },
      {
        value: 6,
        name: '完成',
      },
      {
        value: 8,
        name: '恢复工单',
      },
      {
        value: 9,
        name: '处理中',
      },
      ],
      dialogData: {
        visible: false,
        title: '详情',
        order: {}
      }
    })

    onMounted(() => {
      getDicUtilList()
      getPatrolTaskList()
    })
    const projectId = computed(() => {
      return store.projectId || getCookie('gh_projectId')
    })
    watch(projectId, (val) => {
      if (val) {
        getDicUtilList()
        getPatrolTaskList()
      }
    })
    watch(props, (status, preStatus) => {
      if (status) getPatrolTaskList()
    })
    const getDicUtilList = () => {
      api.getDicUtil({
        dicCode: 'profession_type',
        projectId: getCookie("gh_projectId"),
      }).then((res) => {
        state.types = res.data
      })
    }
    const handleCurrentChange = (page) => {
      state.page = page
      getPatrolTaskList()
    }
    const getPatrolTaskList = () => {
      api.getPatrolTask({
        page: state.page,
        size: state.size,
        projectId: getCookie("gh_projectId"),
        status: props.status,
      }).then((res) => {
        state.list = res.data
        state.total = res.total
      })
    }
    const getStaffTypeName = (type) => {
      let name = ''
      state.types.forEach((t) => {
        if (t.tagValue == type) {
          name = t.tagName
        }
      })
      return name
    }
    const getStatus = (val) => {
      let name = ''
      state.state.forEach((t) => {
        if (t.value == val) {
          name = t.name
        }
      })
      return name
    }
    const edit = (row) => {
      sessionStorage.setItem('order', JSON.stringify(row));
      router.push({
        path: '/patrol/order/detail',
      })
    }
    return {
      ...toRefs(state),
      projectId,
      getStatus,
      edit,
      getStaffTypeName,
      getPatrolTaskList,
      handleCurrentChange,
      getDicUtilList,
    }
  },
}
</script>

<style></style>
