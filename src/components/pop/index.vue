<template>
<Transition name="fade" mode="out-in" appear>
    <div class="diagram" v-if="show">
        <div class="content">
            <slot></slot>
        </div>
    </div>
</Transition>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: ['show','title'],

    setup(props, {
        emit
    }) {
        const state = reactive({

        })
        const close = () => {
            emit('update:show', false)
        }
        return {
            ...toRefs(state),
            close,

        }
    },
})
</script>

<style lang="scss" scoped>
div {
    box-sizing: border-box;
}

.diagram {
    height: calc(100% - 130px - 60px);
    width: 1100px;
    background: url("/src/assets/images/dialog/panel_bg.png") no-repeat;
    background-size: 100% 100%;
    border: 1px solid rgba(61, 233, 250, 0.3);
    border-radius: 15px;
    margin-top: 125px;
    padding: 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);

    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 500;
    overflow: hidden;

    .content {
        padding: 10px;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
        z-index: 1;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
            width: 8px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(16, 52, 87, 0.4);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.7) 0%, rgba(17, 150, 252, 0.5) 100%);
            border-radius: 4px;
            transition: background 0.3s ease;

            &:hover {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%);
            }
        }

        &::-webkit-scrollbar-corner {
            background: transparent;
        }
    }
}

/* 过渡动画优化 */
.fade-enter-active,
.fade-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.fade-enter-from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
}

.fade-leave-to {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
}
</style>
