<template>
  <div class="zt">
    <img :src="noData" alt />
    <p class="msg">暂无电子地图～</p>
  </div>
</template>
<script>
export default {
  data () {
    return {
      noData: new URL('/src/assets/images/zt.png', import.meta.url).href
    }
  },
}
</script>
<style lang="scss" scoped>
.zt {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 8;
  .msg {
    font-size: 14px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 400;
    color: #778897;
  }
}
</style>
