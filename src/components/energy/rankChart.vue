
<template>
  <div class="rank_chart" ref="rankChart"></div>
</template>

<script>
import { onMounted, ref, inject, watch } from 'vue'
export default {
  props: {
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    yData: {
      type: Array,
      default: () => {
        return []
      }
    },
    color: {
      type: String,
      default: ''
    },
    barWidth: {
      type: Number,
      default: 16
    }
  },
  setup (props) {
    const rankChart = ref(null)
    let echarts = inject('ec') //引入


    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    const initChart = () => {
      const myChart = echarts.init(rankChart.value)
      let option = {
        tooltip: {
          show: true,
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            color: '#fff'
          },
          formatter: "{b}:{c} kwh"
        },
        grid: {
          left: '2%',
          top: '0',
          right: '6%',
          bottom: '6%',
          containLabel: true
        },
        barWidth: props.barWidth,
        xAxis: {
          type: 'value',
          show: false,
          position: 'top',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff',
            }
          },
          splitLine: {
            show: false
          },
        },
        yAxis: [{
          type: 'category',
          axisTick: {
            show: false,
            alignWithLabel: false,
            length: 5,

          },
          "splitLine": { //网格线
            "show": false
          },
          inverse: 'true', //排序
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff',
            }
          },
          data: props.yData
        }],
        series: [{
          name: '能耗值',
          type: 'bar',
          label: {
            normal: {
              show: true,
              position: 'right',
              formatter: '{c} kwh',
              textStyle: {
                color: 'white' //color of value
              }
            }
          },
          itemStyle: {
            normal: {
              show: true,
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
                offset: 0,
                color: 'rgba(66, 83, 229, 0)'
              }, {
                offset: 1,
                color: 'rgba(66, 83, 229, 1)'
              }]),
              barBorderRadius: 50,
              borderWidth: 0,
              borderColor: '#333',
            }
          },
          barGap: '0%',
          barCategoryGap: '50%',
          data: props.xData

        }
        ]
      };

      myChart.setOption(option); //展示
      myChart.resize(); //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize(); //刷新画布
      })
    }
    onMounted(() => {
      initChart()
    })
    return {
      rankChart,
      initChart
    }
  }
}
</script>

<style lang="scss" scoped>
.rank_chart {
  width: 100%;
  height: 100%;
}
</style>