<template>
<div class="ripple-chart" ref="rtppleChart"></div>
</template>

<script>
import 'echarts-liquidfill/src/liquidFill.js';
import {
    ref,
    onMounted,
    inject,
    watch,
    nextTick
} from 'vue';
export default {
    props: ["Num", "Name"],
    setup(props) {
        const rtppleChart = ref(null)
        let echarts = inject('ec') //引入

        onMounted(() => {
            nextTick(() => {
                initChart()
            })

        })
        watch(props, (newVal, oldVal) => {
            if (newVal)
                initChart()
        })
        const initChart = () => {
            let option = {
                backgroundColor: 'transparent',
                title: {
                    text: props.Num,
                    textStyle: {
                        fontSize: 18,
                        fontFamily: 'Microsoft Yahei',
                        fontWeight: 'normal',
                        color: '#bcb8fb',
                        rich: {
                            a: {
                                fontSize: 16,
                            }
                        }
                    },
                    x: 'center',
                    y: '45%'
                },
                graphic: [{
                    type: 'group',
                    left: 'center',
                    top: '60%',
                    children: [{
                        type: 'text',
                        z: 100,
                        left: '10',
                        top: 'middle',
                        style: {
                            fill: '#aab2fa',
                            text: props.Name,
                            font: '16px Microsoft YaHei'
                        }
                    }]
                }],
                outline: {
                    borderDistance: 0,
                    itemStyle: {
                        borderWidth: 10,
                        borderColor: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: 'rgba(69, 73, 240, 0)'
                            }, {
                                offset: 0.5,
                                color: 'rgba(69, 73, 240, .2)'
                            }, {
                                offset: 1,
                                color: 'rgba(69, 73, 240, 1)'
                            }],
                            globalCoord: false
                        },
                        shadowBlur: 5,
                        shadowColor: '#000',
                    }
                },
                series: [{
                    type: 'liquidFill',
                    radius: '85%',
                    center: ['50%', '50%'],
                    //  shape: 'roundRect',
                    data: [{
                        value: "0.5",
                        direction: 'left',
                        itemStyle: {
                            normal: {
                                color: "rgba(62, 131, 212, 0.4)" // 设置水波的颜色
                            }
                        }
                    }, {
                        value: "0.35",
                        direction: 'left',
                        itemStyle: {
                            normal: {
                                color: "rgba(62, 131, 212, 0.6)" // 设置水波的颜色
                            }
                        }
                    }, {
                        value: "0.5",
                        direction: 'left',
                        itemStyle: {
                            normal: {
                                color: "rgba(62, 131, 212, 0.8)" // 设置水波的颜色
                            }
                        }
                    }],
                    backgroundStyle: {
                        color: {
                            type: 'linear',
                            x: 1,
                            y: 0,
                            x2: 0.5,
                            y2: 1,
                            colorStops: [{
                                offset: 1,
                                color: 'rgba(68, 145, 253,0.4)'
                            }, {
                                offset: 0.5,
                                color: 'rgba(68, 145, 253, 0.6)'
                            }, {
                                offset: 0,
                                color: 'rgba(68, 145, 253, 1)'
                            }],
                            globalCoord: false
                        },
                    },
                    label: {
                        normal: {
                            formatter: '',
                        }
                    }
                }, ]
            };
            var myChart = echarts.init(rtppleChart.value)
            myChart.setOption(option); //展示
            myChart.resize(); //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize(); //刷新画布
            })
        }
        return {
            rtppleChart,
            initChart
        }
    }
}
</script>

<style>
.ripple-chart {
    width: 100%;
    height: 100%;
}
</style>
