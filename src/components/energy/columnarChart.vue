<template>
  <div class="columnar_chart" ref="columnarChart"></div>
</template>

<script>
import { format } from 'echarts';
import {
  watch,
  inject,
  onMounted,
  ref,
  nextTick
} from 'vue'
export default {
  props: ["chartData"],
  setup (props) {
    const columnarChart = ref(null)
    let echarts = inject('ec')

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      let option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(47, 54, 60, 0.8)',
          borderColor: '#2F363C',
          textStyle: {
            fontSize: 12,
            lineHeight: 24,
            color: '#fff'
          },
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            let res = params[0].name + '<br/>';
            for (let i = 0, l = params.length; i < l; i++) {
              res += params[i].seriesName + ' : ' + params[i].value+' kwh' + '<br/>';
            }
            return res;
          }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '15%',
          top: '14%',
          containLabel: true
        },
        legend: {
          right: '2%',
          top: '1%',
          textStyle: {
            color: "#fff"
          },
          itemWidth: 14,
          itemHeight: 4
        },
        xAxis: {
          type: 'category',
          data: props.chartData.xData,
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          },
          axisLabel: {
            textStyle: {
              fontFamily: 'Microsoft YaHei'
            }
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#fff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.3)'
            }
          },
          axisLabel: {}
        },
        "dataZoom": [{
          "show": true,
          "height": 12,
          "xAxisIndex": [0],
          bottom: '8%',
          "start": 10,
          "end": 90,
          handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
          handleSize: '110%',
          handleStyle: {
            color: "#d3dee5",
          },
          textStyle: {
            color: "#fff"
          },
          borderColor: "#90979c"
        }, {
          "type": "inside",
          "show": true,
          "height": 15,
          "start": 1,
          "end": 35
        }],
        series: [{
          name: '本期',
          type: 'bar',
          barWidth: '10%',
          itemStyle: {
            normal: {
              color: "#27EDBB"
            },
          },
          data: props.chartData.yData1
        },
        {
          name: '上期',
          type: 'bar',
          barWidth: '10%',
          itemStyle: {
            normal: {
              color: "#4253E5"
            }
          },
          data: props.chartData.yData2
        }
        ]
      };
      const myChart = echarts.init(columnarChart.value)
      myChart.setOption(option); //展示
      myChart.resize(); //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize(); //刷新画布
      })
    }
    return {
      columnarChart,
      initChart
    }
  }
}
</script>

<style>
.columnar_chart {
  width: 100%;
  height: 100%;
}
</style>
