<template>
  <div class="list">
    <div
      class="item"
      v-for="(item, i) in typeData"
      :key="i"
      :class="{ active: checkType == item.id }"
      @click="handleCheck(item.id)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script>
export default {
  props: ['typeData'],
  data () {
    return {
      checkType: this.typeData[0].id,
    }
  },
  methods: {
    handleCheck (data) {
      this.checkType = data
      this.$emit('clickDeviceType', data)
    },
  },
}
</script>

<style lang="scss" scoped>
.list {
  width: auto;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;

  .item {
    cursor: pointer;
    display: inline-block;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    background: rgba(97, 109, 148, 0.2);
    border-radius: 2px;
    border: 1px solid #616d94;
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.5);
    margin: 0 10px;

    &.active {
      background: rgba(61, 233, 250, 0.5);
      border: 1px solid #3de9fa;
      color: #fff;
    }
  }
}
</style>
