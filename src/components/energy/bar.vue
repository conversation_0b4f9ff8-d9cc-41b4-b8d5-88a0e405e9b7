<template>
<div ref="horizontal" class="horizontal-histogram"></div>
</template>

<script>
import {
    onMounted,
    ref,
    inject,
    nextTick,
    watch
} from 'vue'
export default {
    props: ["chartData"],
    setup(props) {
        const horizontal = ref(null)
        const echarts = inject('ec')

        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })
        watch(props, (newVal, oldVal) => {
            if (newVal) initChart()
        })
        const initChart = () => {
            var myChart = echarts.init(horizontal.value)
            let option = {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(23, 25, 30, 0.8)',
                    borderColor: '#2F363C',
                    textStyle: {
                        color: '#fff'
                    },
                    axisPointer: { // 坐标轴指示器，坐标轴触发有效
                        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        backgroundColor: '#17191E'
                    },
                },
                grid: {
                    top: "10%",
                    left: '0',
                    right: '0',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: "#009cff",
                            width: 0.3
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#778897'
                        }
                    },
                    data: props.chartData.xAxis
                }],
                yAxis: [{
                    type: 'value',
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: false,
                        formatter: '{value}',

                    },
                    splitLine: {
                        show: false,
                    }
                }],
                series: [{
                    type: 'bar',
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            color: '#fff',
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(39, 237, 187, 1)' // 0% 处的颜色
                            }, {
                                offset: 1,
                                color: 'rgba(39, 119, 237, 0.3)' // 100% 处的颜色
                            }], false),
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c}',
                                fontSize: 14
                            },
                            shadowBlur: 10,
                            shadowColor: 'rgba(40, 40, 40, 0.3)',
                        }
                    },
                    data: props.chartData.data
                }]
            };

            myChart.setOption(option); //展示
            myChart.resize(); //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize(); //刷新画布
            })
        }
        return {
            horizontal,
            initChart
        }
    }
}
</script>

<style scoped>
.horizontal-histogram {
    width: 100%;
    height: 100%;
}
</style>
