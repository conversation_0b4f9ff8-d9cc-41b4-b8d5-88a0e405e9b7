<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import { formatter } from 'element-plus';
import {
    ref,
    inject,
    onMounted,
    reactive,
    nextTick,
    toRefs,
    watch,
} from 'vue'
export default {
    props: ['chartData'],
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        const state = reactive({
            myChart: null,
        })
        onMounted(() => {
            nextTick(() => {
                initChart()
                getChartData()
            })
        })
        watch(props.chartData, (newVal, oldVal) => {
            if (newVal) {
                getChartData()
            }
        })

        function initChart() {
            let option = {
                backgroundColor: 'transparent',
                tooltip: {
                    show: false,
                    backgroundColor: 'rgba(47, 54, 60, 0.8)',
                    borderColor: '#2F363C',
                    textStyle: {
                        color: '#fff'
                    },
                 
                },
                grid: {
                    top: '1%',
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,
                },
                barWidth: 15,
                xAxis: {
                    type: 'value',
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        //  改变x轴颜色
                        show: false,
                        lineStyle: {
                            color: '#CDCDCD',
                        },
                    },
                    axisLabel: {
                        show: false,
                        //  改变x轴字体颜色和大小
                        textStyle: {
                            color: 'rgba(250,250,250,0.6)',
                            fontSize: 14,
                        },
                    },
                },
                yAxis: {
                    type: 'category',
                    data: ['照明', '空调插座', '动力', '特殊用电'],
                    splitLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        show: false,
                        //  改变y轴颜色
                        lineStyle: {
                            color: '#CDCDCD',
                        },
                    },
                    axisLabel: {
                        show: true,
                        //  改变y轴字体颜色和大小
                        //formatter: '{value} m³ ', //  给y轴添加单位
                        textStyle: {
                            color: 'rgba(250,250,250,0.6)',
                            fontSize: 14,
                        },
                    },
                },
                series: [
                    // {
                    //     type: 'bar',
                    //     name: '昨日',
                    //     itemStyle: {
                    //         normal: {
                    //             color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                    //                     offset: 0,
                    //                     color: 'rgba(169, 66, 229, 0)',
                    //                 },
                    //                 {
                    //                     offset: 1,
                    //                     color: 'rgba(169, 66, 229, 1)',
                    //                 },
                    //             ]),
                    //             label: {
                    //                 show: true, //开启显示
                    //                 position: 'right', //在上方显示
                    //                 textStyle: {
                    //                     //数值样式
                    //                     color: '#fff',
                    //                     fontSize: 14,
                    //                     fontWeight: 600,
                    //                 },
                    //             },
                    //         },
                    //     },
                    //     data: [19, 29, 39, 81],
                    // },
                    {
                        type: 'bar',
                        name: '今日',
                        itemStyle: {
                            normal: {
                                label: {
                                    show: true, //开启显示
                                    position: 'right', //在上方显示
                                    textStyle: {
                                        //数值样式
                                        color: '#fff',
                                        fontSize: 14,
                                        fontWeight: 400,
                                    },
                                    formatter: function (params) {
                                        return params.value + ' kWh';
                                    },
                                },
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                        offset: 0,
                                        color: 'rgba(66, 83, 229, 0)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(66, 83, 229, 1)',
                                    },
                                ]),
                            },
                        },
                        data: [12, 23, 35, 100],
                    },
                ],
            }
            state.myChart = echarts.init(myEcharts.value)
            state.myChart.showLoading() //等待的显示
            state.myChart.setOption(option)
            window.addEventListener('resize', () => {
                state.myChart.resize() //刷新画布
            })
        }

        function getChartData() {
            state.myChart.setOption({
                yAxis: {
                    data: props.chartData.textData,
                },
                series: [
                    // {
                    //     name: props.chartData.firstHalf.name,
                    //     data: props.chartData.firstHalf.data,
                    // },
                    {
                        name: props.chartData.thisPeriod.name,
                        data: props.chartData.thisPeriod.data,
                    },
                ],
            })
            state.myChart.hideLoading()
            state.myChart.resize() //刷新画布
        }

        return {
            ...toRefs(state),
            myEcharts,
            initChart,
            getChartData
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
