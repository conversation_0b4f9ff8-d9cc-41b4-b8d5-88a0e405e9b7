<template>
  <div ref="meterChart" class="meter-chart" />
</template>

<script>
import {
  ref,
  inject,
  reactive,
  toRefs,
  watch,
  onMounted,
  nextTick
} from 'vue';
export default {
  props: {
    meterChartValue: {
      type: Number,
      default: 0
    },
  },
  setup (props) {
    const meterChart = ref(null)
    let echarts = inject('ec')

    const state = reactive({
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
        offset: 0,
        color: "#5CF9FE" // 0% 处的颜色
      },
      {
        offset: 0.17,
        color: "#468EFD" // 100% 处的颜色
      },
      {
        offset: 0.9,
        color: "#24d4d6" // 100% 处的颜色
      },
      {
        offset: 1,
        color: "#24d4d6" // 100% 处的颜色
      }
      ])
    })

    watch(props, (newVal, oldVal) => {
      if (newVal) {
        initChart()
      }
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    const initChart = () => {
      let option = {
        series: [{
          name: '刻度',
          type: 'gauge',
          radius: '95%',
          min: 0, //最小刻度
          max: 120, //最大刻度
          center: ["50%", "60%"],
          splitNumber: 8, //刻度数量
          startAngle: 200,
          endAngle: -20,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: [
                [1, 'rgba(0,0,0,0)']
              ]
            }
          }, //仪表盘轴线
          axisLabel: {
            show: true,
            color: '#979797',
            distance: -40
          }, //刻度标签。
          axisTick: {
            show: true,
            splitNumber: 7,
            lineStyle: {
              width: 1,
            },
            length: -8
          }, //刻度样式
          splitLine: {
            show: false
          }, //分隔线样式
          detail: {
            show: false
          },
          pointer: {
            show: false
          }
        },
        {
          type: 'gauge',
          radius: '80%',
          center: ["50%", "60%"],
          splitNumber: 0, //刻度数量
          startAngle: 200,
          endAngle: -20,
          axisLine: {
            show: true,
            lineStyle: {
              width: 15,
              color: [
                [
                  0.9, new echarts.graphic.LinearGradient(
                    0, 0, 1, 0, [{
                      offset: 1,
                      color: '#27A6ED'
                    },
                    {
                      offset: 1,
                      color: '#0071B1'
                    }
                  ]
                  )
                ],
                [
                  1, '#413e54'
                ]
              ]
            }
          },
          //分隔线样式。
          splitLine: {
            show: false,
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          },
          pointer: {
            show: false
          },
          //仪表盘详情，用于显示数据。
          detail: {
            show: true,
            offsetCenter: [0, '-10%'],
            color: '#3DE9FA',
            formatter: function (params) {
              return params
            },
            textStyle: {
              fontSize: 32,
              fontFamily: 'PingFangSC-Medium',
              fontWeight: 500
            }
          },
          data: [{
            name: "",
            value: props.meterChartValue
          }]
        }
        ]
      };
      var myChart = echarts.init(meterChart.value)
      myChart.setOption(option); // 展示
      myChart.resize(); // 刷新画布
      window.addEventListener("resize", () => {
        myChart.resize(); // 刷新画布
      });
    }

    return {
      ...toRefs(state),
      meterChart,
      initChart
    }
  }
};
</script>

<style scoped>
.meter-chart {
  width: 100%;
  height: 100%;
}
</style>
