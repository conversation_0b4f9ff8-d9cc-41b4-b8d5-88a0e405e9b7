<template>
  <div class="rank_chart" ref="rankChart"></div>
</template>

<script>
import {
  onMounted,
  ref,
  inject,
  watch
} from 'vue'
export default {
  props: {
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    yData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  setup (props) {
    const rankChart = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    const initChart = () => {
      var myChart = echarts.init(rankChart.value)
      let option = {
        grid: {
          top: '5%',
          bottom: 2,
          right: 0,
          left: 0,
          containLabel: true
        },
        xAxis: {
          show: false,
        },
        yAxis: [{
          inverse: true,
          data: props.yData,
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: '#fff',
            align: 'left',
            verticalAlign: 'bottom',
            padding: [0, 0, 15, 15],
            fontSize: 13
          },
        },
        {
          triggerEvent: true,
          show: true,
          inverse: true,
          data: props.xData,
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            color: ['#fff'],
            align: 'right',
            verticalAlign: 'bottom',
            padding: [0, 10, 15, 0],
            fontSize: 14,
          }
        }
        ],
        series: [{
          name: '值',
          type: 'bar',
          showBackground: true,
          yAxisIndex: 0,
          data: props.xData,
          barWidth: 16,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                1,
                0,
                [{
                  offset: 0,
                  color: '#4253E5'
                },
                {
                  offset: 1,
                  color: '#4253E5'
                }
                ],
                false
              ),
            }
          },
          z: 2
        },
        {
          name: '外框',
          type: 'bar',
          xAxisIndex: 0, //使用的 x 轴的 index，在单个图表实例中存在多个 x 轴的时候有用。
          yAxisIndex: 0, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用。
          barGap: '-100%',
          data: [1000, 1000, 1000],
          barWidth: 15,
          itemStyle: {
            normal: {
              color: 'rgba(66, 83, 229, 0.3)',
            }
          },
          z: 0
        }
        ]
      };

      myChart.setOption(option); //展示
      myChart.resize(); //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize(); //刷新画布
      })
    }
    onMounted(() => {
      initChart()
    })
    return {
      rankChart,
      initChart
    }
  }
}
</script>

<style lang="scss" scoped>
.rank_chart {
  width: 100%;
  height: 100%;
}
</style>
