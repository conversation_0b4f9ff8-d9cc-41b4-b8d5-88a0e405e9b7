<template>
  <div class="card">
    <div class="card-header">
      <div class="header_left">
        <div class="Lheight"></div>
        <div class="card-header-title">{{ title }}</div>
      </div>
      <div class="hea_right">
        <slot name="date" />
      </div>
    </div>
    <slot />
  </div>
</template>

<script>
export default {
  props: ["title"]
}
</script>

<style lang="scss" scope>
.card {
  width: 100%;
  height: 100%;
  .card-header {
    width: 100%;
    height: 28px;
    display: flex;
    justify-content: space-between;
    background: transparent;
    .header_left {
      height: 100%;
      display: flex;
      align-items: center;
      .card-header-title {
        margin-left: 4px;
        height: 100%;
        min-width: 260px;
        line-height: 28px;
        padding: 0 12px;
        background: linear-gradient(
          to right,
          rgba(62, 131, 212, 0.6) 0%,
          rgba(62, 131, 212, 0) 100%
        );
      }
      .Lheight {
        width: 3px;
        height: 100%;
        background: #4dffff;
      }
    }
  }
}
</style>