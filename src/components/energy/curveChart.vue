<template>
    <div class="pie_chart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch,
    reactive
} from 'vue'
export default {
    props: ['chartData'],

    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        var chart = null;
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })
        watch(props, (newVal, oldVal) => {
            if (newVal) {
                initChart()
            }
        })

        const getChartData = () => {
            if (props.chartData.xData.length > 0) {
                chart.setOption({
                    xAxis: {
                        data: props.chartData.xData
                    }
                });
            }
            if (props.chartData.yData.length > 0) {
                chart.setOption({
                    series: {
                        data: props.chartData.yData
                    }
                })
            }

            chart.resize(); //刷新画布
        };

        const initChart = () => {
            let option = {
                grid: {
                    top: 20,
                    bottom: 35,
                    x: 80,
                    x2: 10,
                    y2: 80,
                },
                tooltip: {
                    padding: [10, 20, 10, 8],
                    textStyle: {
                        fontSize: 12,
                        lineHeight: 24,
                        color: '#fff'
                    },
                    trigger: 'axis',
                    backgroundColor: 'rgba(47, 54, 60, 0.8)',
                    borderColor: '#2F363C',
                    axisPointer: {
                        type: 'line',
                        lineStyle: {
                            type: 'dashed',
                            color: '#0c325b',
                        },
                    },
                    formatter: function (params) {
                        let res = params[0].name + '<br/>';
                        for (let i = 0, l = params.length; i < l; i++) {
                            res += params[i].marker + params[i].seriesName + ' : ' + Number(params[i].value).toFixed(2) +'kwh'+ '<br/>';
                        }
                        return res;
                    },
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    // 改变x轴颜色
                    axisLine: {
                        lineStyle: {
                            color: '#009cff',
                            width: 0.3,
                        },
                    },
                    data: props.chartData.xData,
                    // 轴刻度
                    axisTick: {
                        show: false,
                    },
                    // 轴网格
                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        show: true,
                        interval: 0, //横轴信息全部显示
                        textStyle: {
                            color: '#CDCDCD',
                            fontSize: 12,
                        },
                        interval: 0,
                        rotate: 20,
                    },
                },
                yAxis: {
                    axisTick: {
                        show: false, //轴刻度不显示
                    },
                    // max: 100,
                    min: 0,
                    // 改变y轴颜色
                    axisLine: {
                        lineStyle: {
                            color: '#009cff',
                            width: 0.3,
                        },
                    },
                    // 轴网格
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#009cff',
                            width: 0.3,
                        },
                    },
                    //坐标轴文字样式
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#CDCDCD',
                            fontSize: 14,
                        },
                        formatter: function (value) {
                            if (10000 < value && value < 100000000) {
                                value = value / 10000 + '万'
                            } else if (value >= 100000000) {
                                value = value / 100000000 + '千万'
                            }
                            return value;
                        }
                    },
                },
                series: [{
                    name: '用量',
                    type: 'line',
                    areaStyle: {
                        color: '#0c325b',
                    },
                    symbol: 'none', // 折线无拐点
                    lineStyle: {
                        normal: {
                            width: 0, //折线宽度
                        },
                    },
                    smooth: true,
                    data: props.chartData.yData

                }],
            }
            chart = echarts.init(myEcharts.value) //定义
            chart.setOption(option, true) //展示
            chart.resize() //刷新画布
            window.addEventListener('resize', () => {
                chart.resize() //刷新画布
            })
        }

        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.pie_chart {
    width: 100%;
    height: 100%;
}
</style>
