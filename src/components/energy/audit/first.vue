<template>
    <div class="audit_container">
        <div class="audit_container_header">
            <card title="峰平谷分布"/>
        </div>
        <div class="audit_container_body">
            <div class="body_content">
                <div class="content_text">
                    <div class="Light"></div>
                    <div class="explana">峰平谷时段能耗占比分别为：20%，60%，20%</div>
                </div>
                <div class="content_result">
                    <div><i class="iconfont iconxiaolian"></i></div>
                    <div class="result_btn">占比合理，继续保持！</div>
                </div>
            </div>
            <div class="body_content">
                <div class="content_text chart_title">
                    <div class="Light"></div>
                    <div class="explana">能耗时段分布</div>
                </div>
                <div class="body_content_chart">
                    <pie-chart :yData="pieData"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import card from "@/components/energy/card.vue" 
import pieChart from "@/components/energy/pieChart.vue"
export default {
    components:{
        card,
        pieChart
    },
    data(){
        return{
            pieData:[
                {
                    name:'峰时段',
                    value:'4000'
                },
                {
                    name:'平时段',
                    value:'3487'
                },
                {
                    name:'谷时段',
                    value:'2548'
                }
            ],
        }
    }
}
</script>

<style lang="scss" scoped>
.audit_container{
    width: 100%;
    height: 100%;
    &_header{
        width: 100%;
        height: 40px;
    }
    &_body{
        width: 100%;
        height: calc(100% - 40px);
        display: flex;
        .body_content{
            width: 500%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            padding: 40px 0;
            .content_text{
                width: 50%;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                margin-bottom: 20px;
                .Light{
                    width: 10px;
                    height: 10px;
                    border-radius: 5px;
                    background: #3E83D4;
                    margin-top: 5px;
                    flex-shrink:0
                }
                .explana{
                    word-wrap:break-word;
                    margin-left: 10px;
                }
            }
            .chart_title{
                
                justify-content: flex-start;
            }
            .content_result{
                width: 50%;
                height: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                .result_btn{
                    margin-top: 20px;
                    border: solid 1px #32EC7C;
                    padding: 4px 30px;
                    color: #32EC7C;
                }
                i{
                    font-size: 120px;
                    color: #32EC7C;
                }
            }
            &_chart{
                width: 100%;
                height: calc(100% - 160px);
            }
        }
    }
}
    
</style>