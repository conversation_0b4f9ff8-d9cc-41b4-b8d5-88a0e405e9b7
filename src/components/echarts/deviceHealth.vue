<template>
  <div class="history_chart" ref="myEcharts"></div>
</template>

<script setup>
import {
  onMounted,
  ref,
  inject,
  nextTick,
  watch
} from 'vue'

// 定义props
const props = defineProps(['xAxisData', 'seriesData'])

const myEcharts = ref(null)
let echarts = inject('ec') //引入

const initChart = () => {
  var myChart = echarts.init(myEcharts.value)
  let option = {
    tooltip: {
      trigger: "axis",
      backgroundColor: 'rgba(47, 54, 60, 0.8)',
      borderColor: '#2F363C',
      textStyle: {
        color: '#fff'
      },
    },
    grid: {
      left: "4%",
      right: "4%",
      bottom: "10%",
      top: "15%",
      containLabel: true
    },
    xAxis: {
      data: props.xAxisData,
      triggerEvent: true,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        show: true,
        rotate: 0,
        interval: 0,
        textStyle: {
          padding: [14, 0, 0, 0],
          fontSize: 12,
          color: "#778897"
        }
      }
    },
    yAxis: {
      triggerEvent: true,
      nameTextStyle: {
        color: "#fff",
        fontSize: 12,
        padding: [0, 0, 10, -20]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,.1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: 'rgba(255,255,255,.1)'
        }
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: "#778897",
          fontSize: 12
        }
      }
    },
    series: [{
      name: "数量",
      barMinHeight: 10,
      type: "pictorialBar",
      barCategoryGap: "60%",
      // symbol: 'path://M0,10 L10,10 L5,0 L0,10 z',
      symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
      itemStyle: {
        normal: {
          //渐变色
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: "rgb(21, 255, 255)"
          },
          {
            offset: 0.3,
            color: "rgba(0, 163, 255, 0.8)"
          },
          {
            offset: 1,
            color: "rgba(0, 163, 255, 0.3)"
          }
          ])
        }
      },
      label: {
        normal: {
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
            fontSize: 16
          }
        }
      },
      data: props.seriesData,
      z: 10
    }]
  }
  myChart.setOption(option) //展示
  myChart.resize() //刷新画布
  window.addEventListener('resize', () => {
    myChart.resize() //刷新画布
  })
}

// 监听props变化
watch(props, (newVal, oldVal) => {
  if (newVal) {
    initChart()
  }
})

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss">
.history_chart {
  width: 100%;
  height: 100%;
}
</style>
