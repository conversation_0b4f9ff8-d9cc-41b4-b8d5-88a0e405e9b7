<template>
  <div ref="radarChart" class="radar-chart"></div>
</template>

<script setup>
import { onMounted, watch, ref, nextTick } from 'vue';
import * as echarts from 'echarts/core';
import { RadarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';

echarts.use([TitleComponent, TooltipComponent, LegendComponent, RadarChart, CanvasRenderer]);

// 定义props
const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
})

const radarChart = ref(null);
let chart = null;

const initChart = () => {
  if (!radarChart.value) return;
  
  chart = echarts.init(radarChart.value);
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '20%',
      right: '10%',
      bottom: '10%',
      containLabel: true
    },
    radar: {
      indicator: props.chartData.indicator,
      shape: 'polygon',
      radius: '50%',
      center: ['50%', '50%'],
      splitNumber: 4,
      axisName: {
        color: '#C3D2E0',
        fontSize: 12,
        padding: [0, 0],
        formatter: function(value) {
          if (value.length > 4) {
            return value.split('').join('\n');
          }
          return value;
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['rgba(47, 229, 231, 0.05)', 'rgba(47, 229, 231, 0.1)']
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      }
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: props.chartData.value,
            name: '碳排放分布',
            symbol: 'none',
            lineStyle: {
              width: 2,
              color: '#4B91FF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(75, 145, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(75, 145, 255, 0.1)'
                }
              ])
            }
          }
        ]
      }
    ]
  };
  
  chart.setOption(option);
};

const resizeChart = () => {
  if (chart) {
    chart.resize();
  }
};

// 监听props变化
watch(() => props.chartData, () => {
  nextTick(() => {
    if (chart) {
      chart.dispose();
    }
    initChart();
  });
}, { deep: true });

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart();
    window.addEventListener('resize', resizeChart);
  });
});
</script>

<style scoped>
.radar-chart {
  width: 100%;
  height: 100%;
}
</style> 