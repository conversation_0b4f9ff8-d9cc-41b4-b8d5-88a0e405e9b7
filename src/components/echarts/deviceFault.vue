<template>
  <div class="echart" ref="myEcharts"></div>
</template>

<script setup>
import {
  ref,
  inject,
  onMounted,
  watch,
  nextTick
} from 'vue'

// 定义props
const props = defineProps({
  xAxisData: {
    type: Array,
    default: () => {
      return []
    },
  },
  echartData: {
    type: Array,
    default: () => {
      return []
    },
  },
})

const myEcharts = ref(null)
let echarts = inject('ec') //引入

const initChart = () => {
  var myChart = echarts.init(myEcharts.value)
  myChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      backgroundColor: 'rgba(47, 54, 60, 0.8)',
      borderColor: '#2F363C',
      textStyle: {
        color: '#fff'
      },
    },
    dataZoom: [{
      type: 'slider',
      show: true,
      height: 20,
      left: '3%',
      right: '1%',
      bottom: '4%',
      start: 50,
      end: 100,
      textStyle: {
        color: '#d4ffff',
        fontSize: 11,
      },
    }, {
      type: 'inside'
    }

    ],
    grid: {
      top: '10%',
      left: '3%',
      right: '1%',
      bottom: '20%',
    },
    xAxis: [{
      type: 'category',
      data: props.xAxisData,
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#778897',
        width: 100
      },
      splitLine: {
        show: false
      },
      boundaryGap: false,
    }],
    yAxis: [{
      type: 'value',
      min: 0,
      splitNumber: 4,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.1
        }
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        margin: 20,
        textStyle: {
          color: '#778897',

        },
      },
      axisTick: {
        show: false,
      },
    }],
    series: [{
      name: '故障数量',
      data: props.echartData,
      type: 'line',
      showAllSymbol: true,
      symbol: 'circle',
      symbolSize: 5,
      lineStyle: {
        normal: {
          color: "#27EDBB",
        },
      },
      label: {
        show: true,
        position: 'top',
        textStyle: {
          color: '#27EDBB',
        },
      },
      itemStyle: {
        color: "#fff",
        borderColor: "#27EDBB",
        borderWidth: 2,
      },
    }],
  })
  myChart.resize() //刷新画布
  window.addEventListener('resize', () => {
    myChart.resize() //刷新画布
  })
}

// 监听props变化
watch(props, (newVal, oldVal) => {
  if (newVal) initChart()
})

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>
