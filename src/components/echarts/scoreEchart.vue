<template>
<div class="history_chart" ref="myEcharts"></div>
</template>

<script setup>
import {
    onMounted,
    ref,
    inject,
    nextTick,
    watch
} from 'vue'

// 定义props
const props = defineProps(['seriesData', 'radarData'])

const myEcharts = ref(null)
let echarts = inject('ec') //引入

const initChart = () => {
    var myChart = echarts.init(myEcharts.value)
    let option = {
        series: [{
                center: ['50%', '80%'], //仪表的位置
                type: 'gauge', //统计图类型为仪表
                radius: '80%', //统计图的半径大小
                min: 0, //最小刻度
                max: 100, //最大刻度
                splitNumber: 10, //刻度数量
                startAngle: 0, //开始刻度的角度
                endAngle: 180, //结束刻度的角度
                axisLine: {
                    //设置默认刻度盘上的刻度不显示，重新定义刻度盘
                    show: false
                }, //仪表盘轴线
                axisTick: {
                    show: false
                }, //刻度样式
                splitLine: {
                    //文字和刻度的偏移量
                    show: false
                },
                axisLabel: {
                    show: false,
                },
            },
            {
                radius: '90%',
                center: ['50%', '65%'],
                min: 0,
                max: 100,
                startAngle: 180,
                endAngle: 0,
                type: 'gauge',
                axisLabel: {
                    show: false,
                },
                detail: {
                    show: true,
                    formatter: ['{value}', '{name|健康度评分}'].join('\n'),
                    rich: {
                        name: {
                            fontSize: 16,
                            lineHeight: 30,
                            color: '#fff',
                        },
                    },
                    color: '#13D4D9',
                    fontSize: 36,
                    fontWeight: 'bolder',
                    offsetCenter: [-0, -10],
                },
                axisLine: {
                    lineStyle: {
                        width: 20,
                        color: [
                            [
                                80 / 100, '#13D4D9',
                            ],
                            [1, '#2F363C'],
                        ],
                    },
                },
                pointer: {
                    show: false,
                },
                itemStyle: {
                    color: '#DCDCDC',
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                },
                data: [{
                    value: 80,
                }, ],
            },
        ],

    }
    myChart.setOption(option) //展示
    myChart.resize() //刷新画布
    window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
    })
}

// 监听props变化
watch(props, (newVal, oldVal) => {
    if (newVal) {
        initChart()
    }
})

// 组件挂载后初始化图表
onMounted(() => {
    nextTick(() => {
        initChart()
    })
})
</script>

<style lang="scss">
.history_chart {
    width: 100%;
    height: 100%;
}
</style>
