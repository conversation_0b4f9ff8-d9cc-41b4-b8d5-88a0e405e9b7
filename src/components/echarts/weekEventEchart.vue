<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script setup>
import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick
} from 'vue'

// 定义props
const props = defineProps({
    xAxisData: {
        type: Array,
        default: () => {
            return []
        },
    },
    echartData: {
        type: Array,
        default: () => {
            return []
        },
    },
})

const myEcharts = ref(null)
const myChart = ref({})
let echarts = inject('ec') //引入

const initChart = () => {
    const myChart = echarts.init(myEcharts.value)
    let option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                lineStyle: {
                    color: '#57617B'
                }
            }
        },

        grid: {
            top: '10px',
            left: '15px',
            right: '0',
            bottom: '10px',
            containLabel: true
        },
        xAxis: [{
            type: 'category',
            boundaryGap: false,
            axisLine: {
                lineStyle: {
                    color: '#466582'
                }
            },
            axisLabel: {

                textStyle: {

                    color: 'white'
                }
            },
            data: props.xAxisData|| ['13:00', '13:05', '13:10', '13:15', '13:20', '13:25', '13:30', '13:35']
        }],
        yAxis: [{
            type: 'value',
            axisTick: {
                show: false
            },
            axisLine: {
                lineStyle: {
                    color: '#466582'
                },

            },
            axisLabel: {
                margin: 10,
                textStyle: {
                    fontSize: 14,
                    color: 'white'
                }
            },
            splitLine: {
                lineStyle: {
                    color: '#466582'
                },
                show: false
            }
        }],
        series: [{
            name: '数量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
                normal: {
                    width: 3
                }
            },
            areaStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: 'rgba(0,249,250,0.3)'
                    }, {
                        offset: 0.8,
                        color: 'rgba(0,249,250,0.05)'
                    }], false),
                    shadowColor: 'rgba(0, 0, 0, 0.1)',
                    shadowBlur: 10
                },
            },
            itemStyle: {
                normal: {

                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                        offset: 0,
                        color: '#00F9FA'
                    }, {
                        offset: 1,
                        color: '#00F9FA'
                    }])
                },
                emphasis: {
                    color: 'rgb(99,250,235)',
                    borderColor: 'rgba(99,250,235,0.2)',
                    extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
                    borderWidth: 10
                }
            },
            data:props.echartData|| [120, 110, 125, 145, 122, 165, 122, 220]
        }]
    };
    myChart.setOption(option)
    myChart.resize() //刷新画布
    window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
    })

}

// 监听props变化
watch(props, (newVal, oldVal) => {
    if (newVal) initChart()
})

// 组件挂载后初始化图表
onMounted(() => {
    nextTick(() => {
        initChart()
    })
})
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
