<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script setup>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

// 定义props
const props = defineProps({
    yAxisData: {
        type: Array,
        default: () => {
            return []
        },
    },
    echartData: {
        type: Array,
        default: () => {
            return []
        },
    },
})

const myEcharts = ref(null)
let echarts = inject('ec') //引入

const initChart = () => {
    var myChart = echarts.init(myEcharts.value)
    // 绘制图表
    myChart.setOption({
        backgroundColor: 'transparent',
        legend: {
            data: [],
            top: '0',
            bottom: 0,
            right: '10',
            textStyle: {
                color: 'rgba(250,250,250,0.6)',
                fontSize: 14,
            },
        },
        grid: {
            left: '2%',
            top: '0',
            right: '5%',
            bottom: '2%',
            containLabel: true,
        },
        barWidth: 8,
        xAxis: {
            type: 'value',
            axisLine: {
                show: false,
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                show: false,
            },
            splitLine: {
                show: false,
            },
        },
        yAxis: {
            type: 'category',
            data: props.yAxisData,
            splitLine: {
                show: false,
            },
            axisLine: {
                show: false,
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                textStyle: {
                    color: 'rgba(119, 136, 151, 1)',
                    fontSize: 12,
                },
            },
        },
        series: [{
            type: 'bar',
            name: '能耗',
            itemStyle: {
                normal: {
                    barBorderRadius: [4, 4, 4, 4],
                    label: {
                        show: true, //开启显示
                        position: 'right', //在上方显示
                        textStyle: {
                            //数值样式
                            color: '#fff',
                            fontSize: 14,
                        },
                    },
                    color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
                            offset: 0,
                            color: 'rgb(102, 225, 223)',
                        },
                        {
                            offset: 1,
                            color: 'rgb(0, 89, 84)',
                        },
                    ]),
                },
            },
            data: props.echartData,
        }, ],
    })

    myChart.resize() //刷新画布
    window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
    })
}

// 监听props变化
watch(props, () => {
    initChart();
});

// 组件挂载后初始化图表
onMounted(() => {
    nextTick(() => {
        initChart()
    })
})
</script>

<style lang="scss" scoped>
.echart {
    height: 100%;
    width: 100%;
}
</style>
