<template>
  <div class="real_chart" ref="realChart"></div>
</template>

<script setup>

// 定义props
const props = defineProps({
  realData: Object,
  chartParams: Object
})

const realChart = ref(null)
const echarts = inject('ec')

const initChart = () => {
  var myChart = echarts.init(realChart.value)
  let option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(47, 54, 60, 0.8)',
      borderColor: '#2F363C',
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(255, 255, 255, 0)'
            }, {
              offset: 0.5,
              color: 'rgba(255, 255, 255,1)',
            }, {
              offset: 1,
              color: 'rgba(255, 255, 255, 0)'
            }],
            global: false
          }
        }
      }
    },
    grid: {
      top: '10%',
      left: '2%',
      right: '2%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#1B2125'
        }
      },
      axisLabel: {
        color: "#778897"
      },
      data: props.realData.time
    }],
    yAxis: [{
      type: 'value',
      name: '',
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#1B2125'
        }
      },
      axisLabel: {
        margin: 10,
        textStyle: {
          color: '#778897',
          fontSize: 14
        }
      },
      splitLine: {
        lineStyle: {
          color: '#2B3C46'
        }
      }
    }],
    series: [{
      name: props.chartParams.title,
      type: 'line',
      symbol: 'circle',
      symbolSize: 10,
      lineStyle: {
        color: "#4253E5"
      },
      itemStyle: {
        normal: {
          color: '#fff',
          borderColor: '#4253E5',
          borderWidth: 3
        }
      },
      data: props.realData.data
    }]
  }
  myChart.setOption(option); //展示
  myChart.resize(); //刷新画布
  window.onresize = function () { //自适应大小
    myChart.resize();
  };
}

// 监听props变化
watch(() => [props.realData, props.chartParams], (newVal, oldVal) => {
  if (newVal[0] && newVal[1]) {
    nextTick(() => {
      initChart()
    })
  }
}, { deep: true })

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style>
.real_chart {
  width: 100%;
  height: 100%;
}
</style>
