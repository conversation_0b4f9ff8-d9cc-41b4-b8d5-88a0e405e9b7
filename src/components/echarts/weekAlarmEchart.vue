<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script setup>
import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick
} from 'vue'

// 定义props
const props = defineProps({
    xAxisData: {
        type: Array,
        default: () => {
            return []
        },
    },
    echartData: {
        type: Array,
        default: () => {
            return []
        },
    },
})

const myEcharts = ref(null)
const myChart = ref({})
let echarts = inject('ec') //引入

const initChart = () => {
    const myChart = echarts.init(myEcharts.value)
    var data = {
        city: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        num: ["40", "60", "22", "85", "50", "40","30"]
    }
    let option = {
        grid: {
            top: '30',
            left: '10%',
            right: '10%',
            bottom: '50',
            containLabel: true
        },
        xAxis: [{
            type: 'category',
            boundaryGap: false,
            axisLine: { //坐标轴轴线相关设置。数学上的x轴
                show: true,
                lineStyle: {
                    color: 'color:"#092b5d"'
                },
            },
            axisLabel: { //坐标轴刻度标签的相关设置
                textStyle: {
                    color: '#fff',
                    margin: 15,
                },
            },
            axisTick: {
                show: false,
            },
            data: data.city
        }],
        yAxis: [{
            min: 0,
            // max: 1000,
            splitLine: {
                show: false,
                lineStyle: {
                    color: '#092b5d'
                },
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: "#092b5d"
                }

            },
            axisLabel: {
                show: true,
                textStyle: {
                    color: '#fff',

                },
                // formatter: function (value) {
                //     if (value === 0) {
                //         return value
                //     }
                //     return value + '%'
                // }
            },
            axisTick: {
                show: false,
            },
        }],
        series: [{
            name: '注册总量',
            type: 'line',
            symbol: 'circle', // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbolSize: 8,
            lineStyle: {
                normal: {
                    color: "#318FF7", // 线条颜色
                },
                borderColor: '#318FF7',
            },
            itemStyle: {
                color: "#318FF7",
                borderColor: "#318FF7",
                borderWidth: 1

            },
            label: {
                normal: {
                    show: true,
                    position: "top",
                    formatter: [
                        ' {a|{c}}',
                    ].join(','),
                    rich: {
                        a: {
                            color: '#fff',
                            align: 'center',
                        },
                    }
                }
            },
            tooltip: {
                show: true
            },
            areaStyle: { //区域填充样式
                normal: {
                    //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是'true'，则该四个值是绝对像素位置。
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: "rgba(124, 128, 244,.3)"

                        },
                        {
                            offset: 1,
                            color: "rgba(124, 128, 244, 0)"
                        }
                    ], false),
                    shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
                    shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
                }
            },
            data:props.echartData|| data.num
        }]
    };
    myChart.setOption(option)
    myChart.resize() //刷新画布
    window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
    })

}

// 监听props变化
watch(props, (newVal, oldVal) => {
    if (newVal) initChart()
})

// 组件挂载后初始化图表
onMounted(() => {
    nextTick(() => {
        initChart()
    })
})
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
