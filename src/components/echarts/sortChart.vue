<template>
  <div class="history_chart" ref="myEcharts"></div>
</template>

<script setup>
import {
  onMounted,
  ref,
  inject,
  nextTick,
  watch
} from 'vue'

// 定义props
const props = defineProps(['seriesData', 'legendData'])

const myEcharts = ref(null)
let echarts = inject('ec') //引入

const initChart = () => {
  var myChart = echarts.init(myEcharts.value)
  let option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(47, 54, 60, 0.8)',
      borderColor: '#2F363C',
      textStyle: {
        color: '#fff'
      },
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '10%',
      top: 'center',
      itemGap: 25,
      selectedMode: false,
      icon: 'circle',
      data: ['灭火器','沙包','排水泵','水带'],
      textStyle: {
        color: '#77899c',
        rich: {
          uname: {
            width: 100,
          }
        },
      },
      formatter (name) {
        return `{uname|${name}}`;
      },
    },
    color: ['#FFEB6D ', '#81D9FF', '#427EDF', '#C7DFFF'],
    series: [{
      name: '分类统计',
      type: 'pie',
      radius: [50, 70],
      center: ['40%', '50%'],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      itemStyle: {
        borderWidth: 0,
        borderColor: '#fff',
      },
      data: props.seriesData,
    },],
  }
  myChart.setOption(option) //展示
  myChart.resize() //刷新画布
  window.addEventListener('resize', () => {
    myChart.resize() //刷新画布
  })
}

// 监听props变化
watch(props, (newVal, oldVal) => {
  if (newVal) {
    initChart()
  }
})

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss">
.history_chart {
  width: 100%;
  height: 100%;
}
</style>
