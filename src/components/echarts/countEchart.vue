<template>
  <div class="echart" ref="myEcharts"></div>
</template>
<script setup>
import { ref, inject, onMounted, nextTick, watch } from 'vue'

// 定义props
const props = defineProps({
  xAxisData: {
    type: Array,
    default: () => {
      return []
    },
  },
  echartData: {
    type: Array,
    default: () => {
      return []
    },
  },
})

const myEcharts = ref(null)
let echarts = inject('ec') //引入

const initChart = () => {
  var myChart = echarts.init(myEcharts.value)
  myChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(47, 54, 60, 0.8)',
      borderColor: '#2F363C',
      textStyle: {
        fontSize: 12,
        lineHeight: 24,
        color: '#fff'
      },
    },
    grid: {
      left: '2%',
      top: '10',
      right: '2%',
      bottom: '5%',
      containLabel: true,
    },
    barWidth: 10,
    color: 'rgba(27, 192, 237, 1)',
    xAxis: {
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#778897'
        }
      },
      axisLabel: {
        margin: 10,
        color: '#778897',
        textStyle: {
          fontSize: 12
        },
      },
      axisTick: {
        show: false,
      },
      data: props.xAxisData,
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        data: props.echartData,
        type: 'bar',
        barWidth: '12px',
      },
    ],
  })
  myChart.resize() //刷新画布
  window.addEventListener('resize', () => {
    myChart.resize() //刷新画布
  })
}

// 监听props变化
watch(props, (newVal, oldVal) => {
  if (newVal) {
    initChart()
  }
})

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>
<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>
