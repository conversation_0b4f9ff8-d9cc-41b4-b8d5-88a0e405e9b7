<template>
  <div class="pie-chart-container" ref="chartContainer"></div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

// 定义props
const props = defineProps({
  chartData: {
    type: Array,
    required: true
  },
  totalValue: {
    type: [Number, String],
    required: true
  }
})

const chartContainer = ref(null);
let chart = null;

const initChart = () => {
  if (!chartContainer.value) return;
  
  chart = echarts.init(chartContainer.value);
  updateChart();
  
  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
};

const updateChart = () => {
  if (!chart) return;
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    title: {
      text: props.totalValue + '\nkgCO₂e',
      left: 'center',
      top: 'center',
      textStyle: {
        fontSize: 16,
        fontFamily: 'BEBAS, sans-serif',
        color: '#F3F3F3',
        lineHeight: 20,
        fontWeight: 'bold',
        textShadow: '0 0 10px rgba(47, 229, 231, 0.5)'
      }
    },
    series: [
      {
        name: '碳排放分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: 'rgba(16, 42, 67, 0.5)',
          borderWidth: 2
        },
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
            fontSize: '12',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(47, 229, 231, 0.5)'
          }
        },
        data: props.chartData.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ],
    backgroundColor: 'transparent',
    textStyle: {
      color: '#C3D2E0'
    }
  };

  chart.setOption(option);
};

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

// 监听props变化
watch(() => props.chartData, () => {
  nextTick(() => {
    updateChart();
  });
}, { deep: true });

watch(() => props.totalValue, () => {
  nextTick(() => {
    updateChart();
  });
});
</script>

<style scoped>
.pie-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style> 