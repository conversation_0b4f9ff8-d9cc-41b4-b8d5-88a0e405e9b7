<template>
  <div class="echart" ref="myEcharts"></div>
</template>

<script>

import {
  ref,
  inject,
  onMounted,
  watch,
  nextTick,
  onBeforeUnmount
} from 'vue'
export default {
  props: ['data'],
  setup(props) {
    const myEcharts = ref(null)
    const myChart = ref({})
    const interval = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })

    })
    const fontSize=(res) =>{
      let docEl = document.documentElement,
        clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      let fontSize = 100 * (clientWidth / 1920);
      return res * fontSize;

    }

    const initChart = () => {
      const myChart = echarts.init(myEcharts.value)

      let option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '15%',
          right: '3%',
          left: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data:props.data.map(d=>d.name) ,
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.12)'
            }
          },
          axisLabel: {
            margin: 10,
            color: '#e2e9ff',
            textStyle: {
              fontSize: fontSize(0.12)
            },
          },
        }],
        yAxis: [{
          axisLabel: {
            formatter: '{value}',
            color: '#e2e9ff',
            textStyle: {
              fontSize: fontSize(0.12)
            }
          },
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.12)'
            }
          }
        }],
        series: [{
          type: 'bar',
          data: props.data.map(d=>d.value),
          barWidth: '20px',
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(0,244,255,1)' // 0% 处的颜色
              }, {
                offset: 1,
                color: 'rgba(0,77,167,1)' // 100% 处的颜色
              }], false),
              barBorderRadius: [30, 30, 30, 30],
              shadowColor: 'rgba(0,160,221,1)',
              shadowBlur: 4,
            }
          },
          label: {
            normal: {
              show: true,
              lineHeight: 10,
              width: 80,
              height: 10,
              backgroundColor: 'rgba(0,160,221,0.1)',
              borderRadius: 200,
              position: ['-10', '-20'],
              distance: 1,
              formatter: [
                '    {d|●}',
                ' {a|{c}}     \n',
                '    {b|}'
              ].join(','),
              rich: {
                d: {
                  color: '#3CDDCF',
                },
                a: {
                  color: '#fff',
                  align: 'center',
                  fontSize: fontSize(0.12),
                },
                b: {
                  width: 1,
                  height: 10,
                  borderWidth: 1,
                  borderColor: '#234e6c',
                  align: 'left'
                },
              }
            }
          }
        }]
      };




      myChart.setOption(option)
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })

    }






    return {
      myEcharts,
      myChart,
      initChart
    }
  },
}
</script>

<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>