<template>
    <div class="echart" ref="myEcharts"></div>
</template>

<script>

import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick
} from 'vue'
export default {
    props: ['data','count'],
    setup(props) {
        const myEcharts = ref(null)
        const myChart = ref({})
        let echarts = inject('ec') //引入
        var echartData =props.data|| [
            {
                value: 2,
                name: '1F'
            }, {
                value: 3,
                name: '2F'
            }, {
                value: 5,
                name: '3F'
            }, {
                value: 2,
                name: '4F'
            }, {
                value: 3,
                name: '5F'
            }
        ]

        watch(props, (newVal, oldVal) => {
            if (newVal) {
                echartData = props.data
                initChart()
            }
        })
        onMounted(() => {
            nextTick(() => {

                initChart()
            })

        })
        const fontSize = (res) => {
            let docEl = document.documentElement,
                clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
            if (!clientWidth) return;
            let fontSize = 100 * (clientWidth / 1920);
            return res * fontSize;

        }
        const initChart = () => {
            const myChart = echarts.init(myEcharts.value)

            var scale = 1;

            var rich = {
                yellow: {
                    color: "#ffc72b",
                    fontSize: fontSize(0.12),
                    padding: [5, 4],
                    align: 'center'
                },
                total: {
                    color: "#ffc72b",
                    fontSize: fontSize(0.12),
                    align: 'center'
                },
                white: {
                    color: "#fff",
                    align: 'center',
                    fontSize: fontSize(0.14),
                    padding: [0, 0]
                },
                blue: {
                    color: '#49dff0',
                    fontSize: fontSize(0.12),
                    align: 'center'
                },
                hr: {
                    borderColor: '#0b5263',
                    width: '100%',
                    borderWidth: 1,
                    height: 0,
                }
            }
            let option = {
                title: {
                    text: '总数\n' +props.count,
                    left: 'center',
                    top: 'center',
                    padding: [24, 0],
                    textStyle: {
                        color: '#fff',
                        fontSize: fontSize(0.16),
                        align: 'center'
                    }
                },
                // legend: {
                //     selectedMode: false,
                //     formatter: function (name) {
                //         var total = 0; //各科正确率总和
                //         var averagePercent; //综合正确率
                //         echartData.forEach(function (value, index, array) {
                //             total += value.value;
                //         });
                //         return '{total|' + total + '}';
                //     },
                //     data: [echartData[0].name],
                //     // data: ['高等教育学'],
                //     // itemGap: 50,
                //     left: 'center',
                //     top: 'center',
                //     icon: 'none',
                //     align: 'center',
                //     textStyle: {
                //         color: "#fff",
                //         fontSize: 12 * scale,
                //         rich: rich
                //     },
                // },
                series: [{
                    name: '总考生数量',
                    type: 'pie',
                    radius: ['62%', '80%'],
                    hoverAnimation: false,
                    color: ['#c487ee', '#deb140', '#49dff0', '#034079', '#6f81da', '#00ffb4'],
                    label: {
                        normal: {
                            formatter: function (params, ticket, callback) {
                                var total = 0; //考生总数量
                                var percent = 0; //考生占比
                                echartData.forEach(function (value, index, array) {
                                    total += value.value;
                                });
                                percent = ((params.value / total) * 100).toFixed(1);
                                return '{white|' + params.name + '}\n{hr|}\n{yellow|' + params.value + '}\n{blue|' + percent + '%}';
                            },
                            rich: rich
                        },
                        textStyle: {
                            fontSize: fontSize(0.16),
                        },
                        fontSize: fontSize(0.16),
                    },
                    labelLine: {
                        normal: {
                            length: 15,
                            length2: 15,
                            lineStyle: {
                                color: '#0b5263'
                            }
                        }
                    },
                    data: echartData
                }]
            };




            myChart.setOption(option)
            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })

        }
        return {
            myEcharts,
            myChart,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>