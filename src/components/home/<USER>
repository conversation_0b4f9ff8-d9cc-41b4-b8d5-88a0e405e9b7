<template>
  <div class="echart" ref="myEcharts"></div>
</template>

<script>

import {
  ref,
  inject,
  onMounted,
  watch,
  nextTick,
  onBeforeUnmount
} from 'vue'
export default {
  props: ['data'],
  setup(props) {
    const myEcharts = ref(null)
    const myChart = ref({})
    const interval = ref(null)
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })

    })
    let echartData = [
      {
        name: "传感1",
        value1: 160,

      },
      {
        name: "传感2",
        value1: 138,

      },
      {
        name: "传感3",
        value1: 150,

      },
      {
        name: "传感4",
        value1: 173,

      },
      {
        name: "传感5",
        value1: 180,
      },
    ];

    const initChart = () => {
      const myChart = echarts.init(myEcharts.value)
      let color = [
        "#0090FF",
        "#36CE9E",
        "#FFC005",
        "#FF515A",
        "#8B5CFF",
        "#00CA69"
      ];

      echartData = props.data || echartData


      let xAxisData = echartData.map(v => v.name);

      let yAxisData1 = echartData.map(v => v.value1);

      const hexToRgba = (hex, opacity) => {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
          )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
        }
        return rgbaColor;
      }

      const fontSize = (res) => {
        let docEl = document.documentElement,
          clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
        if (!clientWidth) return;
        let fontSize = 100 * (clientWidth / 1920);
        return res * fontSize;

      }

      let option = {
        color: color,
        title: {
          text: "单位:µg/m³",
          left: "left",
          top: 0,
          textStyle: {
            color: "#d1e6eb",
            fontSize: fontSize(0.12)
          }
        },
        grid: {
          top: 30,
          left: 0,
          right: 30,
          bottom: 10,
          containLabel: true
        },
        xAxis: [{
          type: "category",
          boundaryGap: false,
          axisLabel: {
            textStyle: {
              color: "#d1e6eb",
              fontSize: fontSize(0.16)
            },
            formatter: function (value, index) {
              return index === 0 ? '' : value; // 第一个标签不显示
            },
            interval: 1,
          },
          axisTick: { show: false },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#d1e6eb"
            }
          },
          data: xAxisData
        }],
        yAxis: [{
          show: false,
          type: "value",
          name: '单位:µg/m³',
          axisLabel: {
            textStyle: {
              color: "#d1e6eb",
              fontSize: fontSize(0.16)
            }
          },
          nameTextStyle: {
            color: "#d1e6eb",
            fontSize: 12,
            lineHeight: 40
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        }],
        series: [{
          name: "tvoc",
          type: "line",
          smooth: true,
          // showSymbol: false,/
          symbolSize: 8,
          zlevel: 3,
          lineStyle: {
            normal: {
              color: color[0],
              shadowBlur: 0,
            }
          },
          label: {
            show: true,
            color: "#d1e6eb",
            position: 'top',
            formatter: '{c}',

            fontSize: fontSize(0.16)

          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [{
                  offset: 0,
                  color: hexToRgba(color[0], 0.3)
                },
                {
                  offset: 1,
                  color: hexToRgba(color[0], 0.1)
                }
                ],
                false
              ),
              shadowColor: hexToRgba(color[0], 0.1),
              shadowBlur: 10
            }
          },
          data: yAxisData1
        }]
      };

      myChart.setOption(option)
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })

    }


  



    return {
      myEcharts,
      myChart,
      initChart
    }
  },
}
</script>

<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>