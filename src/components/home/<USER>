<template>
  <div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
  ref,
  inject,
  onMounted,
  watch,
  nextTick
} from 'vue'
export default {
  props: ['xData', 'yData', 'yData1'],
  setup(props) {
    const myEcharts = ref(null)
    const myChart = ref({})
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })

    })

    const fontSize = (res) => {
      let docEl = document.documentElement,
        clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      let fontSize = 100 * (clientWidth / 1920);
      return res * fontSize;

    }
    const initChart = () => {
      const myChart = echarts.init(myEcharts.value)
      let option = {
        tooltip: {},
        grid: {
          top: '20%',
          left: '0',
          right: '0',
          bottom: '0',
          containLabel: true,
        },
        legend: {
          itemGap: 20,
          right: 'center',
          top: '0',
          data: ['温度', '湿度'],
          textStyle: {
            color: '#f9f9f9',
            borderColor: '#fff'
          },
        },
        xAxis: [{
          type: 'category',
          boundaryGap: true,
          axisLine: { //坐标轴轴线相关设置。数学上的x轴
            show: true,
            lineStyle: {
              color: '#d1e6eb'
            },
          },
          axisLabel: { //坐标轴刻度标签的相关设置
            // showMaxLabel: true,
            textStyle: {
              color: '#d1e6eb',
              margin: 15,
              fontSize: fontSize(0.16)
            },
            formatter: function (value, index) {
              return index === 0 ? '' : value; // 第一个标签不显示
            },
            interval: 1,
          },
          axisTick: {
            show: false,
          },
          data: props.xData || ['传感1', '传感2', '传感3', '传感4', '传感5'],
        }],
        yAxis: [{
          type: 'value',
          min: 0,
          splitNumber: 7,
          splitLine: {
            show: true,
            lineStyle: {
              color: '#0a3256'
            }
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            margin: 20,
            textStyle: {
              color: '#d1e6eb',
              fontSize: fontSize(0.16)

            },

          },
          axisTick: {
            show: false,
          },
        }],
        series: [{
          name: '温度',
          type: 'line',
          showAllSymbol: true,
          symbol: 'emptyCircle',
          symbolSize: 6,
          lineStyle: {
            normal: {
              color: "#28ffb3", // 线条颜色
            },
            borderColor: '#f0f'
          },
          label: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#d1e6eb',
              fontSize: fontSize(0.12)
            },
            formatter: '{c}℃'
          },
          itemStyle: {
            normal: {
              color: "#28ffb3",

            }
          },
          tooltip: {
            show: false
          },
          areaStyle: { //区域填充样式
            normal: {
              //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(0,154,120,1)'
              },
              {
                offset: 1,
                color: 'rgba(0,0,0, 0)'
              }
              ], false),
              shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
              shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
            }
          },
          data: props.yData || [28, 29, 27, 29, 26]
        }, {
          name: '湿度',
          type: 'bar',
          barWidth: 20,
          tooltip: {
            show: false
          },
          label: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#d1e6eb',
              fontSize: fontSize(0.12)
            },
            formatter: '{c}%'
          },
          itemStyle: {
            normal: {
              barBorderRadius: 5,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(0,244,255,1)' // 0% 处的颜色
              }, {
                offset: 1,
                color: 'rgba(0,77,167,1)' // 100% 处的颜色
              }], false),
            }
          },
          data: props.yData1 || [60, 61, 59, 80, 70]
        }]
      };

      myChart.setOption(option)
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })

    }
    return {
      myEcharts,
      myChart,
      initChart
    }
  },
}
</script>

<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>