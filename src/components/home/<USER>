<template>
  <div class="echart" ref="myEcharts"></div>
</template>

<script>

import {
  ref,
  inject,
  onMounted,
  watch,
  nextTick
} from 'vue'
export default {
  props: ['data'],
  setup(props) {
    const myEcharts = ref(null)
    const myChart = ref({})
    let echarts = inject('ec') //引入

    watch(props, (newVal, oldVal) => {
      if (newVal) initChart()
    })
    onMounted(() => {
      nextTick(() => {
        initChart()
      })

    })
    const fontSize=(res) =>{
      let docEl = document.documentElement,
        clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      let fontSize = 100 * (clientWidth / 1920);
      return res * fontSize;

    }
    const initChart = () => {
      const myChart = echarts.init(myEcharts.value)

      var category = [{
        name: "传感1",
        value: 510
      },
      {
        name: "传感2",
        value: 509
      },
      {
        name: "传感3",
        value: 520
      },
      {
        name: "传感4",
        value: 509
      },
      {
        name: "传感5",
        value: 510
      }
      ]; // 类别

      category=props.data||category
      var datas = [];
      category.forEach(value => {
        datas.push(value.value);
      });
      let option = {
        xAxis: {
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false,
            textStyle: {
              fontSize:fontSize(0.16),
            }
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: '25%',
          top: 0, // 设置条形图的边距
          right: 50,
          bottom: 0,
          containLabel: true
        },
        yAxis: [{
          type: "category",
          inverse: false,
          data: category,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false,
            textStyle: {
              fontSize: fontSize(0.12),
            }
          }
        }],
        series: [
          {
          // 内
          type: "bar",
          barWidth: fontSize(0.12),

          legendHoverLink: false,
          silent: true,
          itemStyle: {
            normal: {
              barBorderRadius: [4, 4, 4, 4],
              color: function (params) {
                var color;
                if (params.dataIndex == 19) {
                  color = {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0,
                      color: "#66b1ff" // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#409eff" // 100% 处的颜色
                    }
                    ]
                  }
                } else if (params.dataIndex == 18) {
                  color = {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0,
                      color: "#66b1ff" // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#409eff" // 100% 处的颜色
                    }
                    ]
                  }
                } else if (params.dataIndex == 17) {
                  color = {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0,
                      color: "#66b1ff" // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#409eff" // 100% 处的颜色
                    }
                    ]
                  }
                } else {
                  color = {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [{
                      offset: 0,
                      color: "#66b1ff" // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#409eff" // 100% 处的颜色
                    }
                    ]
                  }
                }
                return color;
              },
            }
          },
          label: {
            normal: {
              show: true,
              position: "left",
              formatter: "{b}",
              textStyle: {
                color: "#d1e6eb",
                fontSize: fontSize(0.14),
                
              }
            },
          },
          data: category,
          z: 1,
          animationEasing: "elasticOut"
        },
        {
          // 分隔
          type: "pictorialBar",
          itemStyle: {
            normal: {
              color: "#333"
            }
          },
          symbolRepeat: "fixed",
          symbolMargin: 1.5,
          symbol: "rect",
          symbolClip: true,
          symbolSize: [2, 12],
          symbolPosition: "start",
          data: category,
          z: 2,
          animationEasing: "elasticOut"
        },
        {
          // 外边框
          type: "pictorialBar",
          symbol: "rect",
          itemStyle: {
            normal: {
              color: "none"
            }
          },
          label: {
            normal: {
              formatter: (params) => {
                var text;
                if (params.dataIndex == 1) {
                  text = '{f|  ' + params.data + '%}';
                } else if (params.dataIndex == 2) {
                  text = '{f|  ' + params.data + '%}';
                } else if (params.dataIndex == 3) {
                  text = '{f|  ' + params.data + '%}';
                } else {
                  text = '{f|  ' + params.data + '%}';
                }
                return text;
              },
              rich: {
                f: {
                  color: "#d1e6eb",
                  fontSize: fontSize(0.16),
                },

              },
              position: 'right',
              show: true,
              
            },
          },
          data: datas,
          z: 0,
          animationEasing: "elasticOut"
        }
        ]
      };




      myChart.setOption(option)
      myChart.resize() //刷新画布
      window.addEventListener('resize', () => {
        myChart.resize() //刷新画布
      })

    }
    return {
      myEcharts,
      myChart,
      initChart
    }
  },
}
</script>

<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>