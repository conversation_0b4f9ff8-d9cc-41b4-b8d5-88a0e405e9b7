<template>
<div class="sub_title">
    <div class="item">
        <span class="msg1">{{ title1 }}</span>
        <span class="msg">{{ title }}</span>
    </div>
    <slot />
</div>
</template>

<script lang="ts">
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: {
        title: String,
        title1: String,
    },
    setup() {
        const state = reactive({
           
        });
        return {
            ...toRefs(state),
        };
    },
});
</script>

<style lang="scss" scoped>
.sub_title {
    display: flex;
    height: 40px;
    line-height: 40px;
    align-items: center;
    justify-content: space-between;
    .item {
        // flex: 1;
        width: 430px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 40px;
        background: url("@/assets/images/head2.png") no-repeat bottom/100%;
        background-size: 100%;

        .msg {
            font-size: 16px;
            font-family: "DOUYU";
            font-weight: normal;
            color: transparent;
            margin-right: 38px;
            background: linear-gradient(0deg, #3797eb, #fefeff 85%, #fff);
            -webkit-background-clip: text;
        }
        .msg1 {
            color: transparent;
            margin-right: 15px;
            background: linear-gradient(0deg, #3797eb, #fefeff 85%, #fff);
            -webkit-background-clip: text;
        }

        img {
            width: 22px;
            height: 25px;
            vertical-align: middle;
        }

        .icon {
            display: inline-block;
            background-size: contain;
            background-repeat: no-repeat;
            width: 22px;
            height: 25px;
            vertical-align: middle;
        }
    }
}
</style>
