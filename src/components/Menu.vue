<template>
    <div v-if="menuList.length" class="menu">
        <div v-for="item in menuList" :key="item.id" class="item" @click="clickMenu(item)">
            <div class="img">
                <img :src="getImgUrl(item.imgName)">
            </div>
            <div
                class="name alibaba-bold"
                :class="[activeName === item.name ? 'active' : '']"
                :title="item.name"
            >
                {{ item.name }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
    getCookie,
    removeToken,
    setCookie
} from '@/utils/cookie'
import { useAppStore } from '@/stores/app'
import { useMitt } from '@/hooks/mitt'

const api = inject('$api')
const router = useRouter()
const store = useAppStore()

const menuList = ref([])
const list = ref([])
const activeName = ref('综合态势')
const secondName = ref('')


const emitter = useMitt()
emitter.miitOn('Switch_System', (data) => {
    console.log(data)
    if (window.cmd != data.selectedFloorId) {
        return;
    }
    if (data.command.toLowerCase() == 'access') {
        data = "door";
    }
    menuList.value.forEach(item => {
        if (item.cmd == data.command) {
            clickMenu(item)
        }
    })
})

onMounted(() => {

    let firstMenuName = sessionStorage.getItem("firstMenuName")
    if (firstMenuName) {
        activeName.value = firstMenuName;
    }

    let secondMenuName = sessionStorage.getItem("secondMenuName")
    if (secondMenuName) {
        secondName.value = secondMenuName;
    }

    getProjectMenu()

});

const getProjectMenu = () => {
    if (getCookie("gh_projectId") != 0) {
        api.getMenuList({
            configFlag: false,
            projectId: getCookie("gh_projectId"),
            menuType: [1, 2],
            isPcMenu: true,
            enable: true,
        }).then((res) => {
            menuList.value = res.data;
            if (!getCookie("funMenus")) {
                clickMenu(res.data[0]);
            }
        })
    }
}

const clickMenu = (item) => {
    if (item) {
        if (item.component && item.component.startsWith("dialog_")) {
            store.SET_DIALOG( {
                name: item.name,
                component: item.component.split('_')[1]
            })
            return;
        }

        //重置二级菜单
        if (item.menuType == 1) {
            list.value = [];
            sessionStorage.removeItem("second")
            emitter.miitEmit('second', {
                list: []
            })
            //一级菜单名称
            sessionStorage.setItem("firstMenuName", item.name)
        }
        //点击的是一级菜单
        if (item.menuType == 1 && item.children && item.children.length > 0) {
            list.value = item.children;

            sessionStorage.setItem("second", JSON.stringify(item.children))
            //记录二级菜单,刷新使用
            emitter.miitEmit('second', {
                list: item.children
            })
        }

        if (item.menuType == 1 && activeName.value !== item.name) {
            activeName.value = item.name
        }


        //一级菜单没有绑定组件，默认显示子集的第一个
        if (item.menuType == 1 && !item.component && item.children.length > 0) {
            item = item.children[0];

            secondName.value = item.name;
            sessionStorage.setItem("secondMenuName", item.name)
            emitter.miitEmit('secondMenuName', {
                name: item.name
            })

        }

        if (item.component) {
            if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 2) { //主要是各个子系统第一个
                item.secondComponent = item.component.split("_")[1];
                item.component = item.component.split("_")[0];
            } else if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 3) //子系统弹窗
            {
                let components = item.component.split("_");
                item.component = components[0]
                item.secondComponent = components[1]
                item.popName = components[2]
            }
            removeToken("funMenus")
            setCookie('funMenus', JSON.stringify({
                id: item.id,
                name: item.name,
                component: item.component,
                secondComponent: item.secondComponent,
                code: item.code, //菜单编码
                diagramId: item.diagramId,
                popName: item.popName,
                model: item.model,
                showFloor: item.showFloor,
            }))

            store.SET_FUN_MENU(item)
        }

        if (item.cmd) {
            emitter.miitEmit('ue', {
                type: 'menu',
                value: item.cmd.trim()
            })
        }

        router.push({
            path: '/overview'
        })

        emitter.miitEmit('goHome')


    }

}
const getImgUrl = (name) => {
    return new URL(`/src/assets/images/menu/${name}.png`, import.meta.url).href
}




</script>

<style lang="scss" scoped>
.menu {
    position: fixed;
    width: 1000px;
    left: 50%;
    bottom: 5px;
    transform: translateX(-50%);
    z-index: 10;
    min-height: 60px; // 改为最小高度，允许根据内容自适应
    max-height: 80px; // 设置最大高度
    // 保留原有背景图片
    background: url('@/assets/images/menu/底部导航.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    padding: 8px 0; // 增加上下内边距

    // 增加毛玻璃效果和边框，与 .left .right 面板协调
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(61, 233, 250, 0.2);

    // 整体光效背景叠加
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.05) 0%, transparent 50%);
        opacity: 1;
        pointer-events: none;
        border-radius: 12px;
    }

    .item {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;

        // 悬停效果
        &:hover {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.15) 0%, rgba(24, 64, 104, 0.2) 100%);
            backdrop-filter: blur(6px);
            box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2);
            transform: translateY(-2px);

            .img img {
                filter: drop-shadow(0 0 8px rgba(61, 233, 250, 0.6));
                transform: scale(1.1);
            }
        }

        .img {
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: 24px;
                height: 24px;
                transition: all 0.3s ease;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
            }
        }

        .name {
            min-height: 16px;
            max-width: 80px; // 设置最大宽度，避免菜单项过宽
            font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
            font-weight: bold;
            font-size: 14px;
            line-height: 1.2;
            letter-spacing: 1px; // 稍微减少字间距以容纳更多文字
            text-align: center;
            font-style: normal;
            text-transform: none;
            transition: all 0.3s ease;

            // 文本换行设置 - 根据用户偏好换行而不是截断
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: visible;
            display: -webkit-box;
            -webkit-line-clamp: 2; // 最多显示2行
            line-clamp: 2; // 标准属性
            -webkit-box-orient: vertical;
            max-height: 2.4em; // 2行的高度

            // 增强文字阴影效果
            background: linear-gradient(to bottom, #FFFFFF, #7EABD4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
        }

        .active {
            min-height: 16px;
            max-width: 80px; // 设置最大宽度，避免菜单项过宽
            font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
            font-weight: bold;
            font-size: 16px;
            line-height: 1.2;
            letter-spacing: 1px; // 稍微减少字间距以容纳更多文字
            text-align: center;
            font-style: normal;
            text-transform: none;
            transition: all 0.3s ease;

            // 文本换行设置 - 根据用户偏好换行而不是截断
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: visible;
            display: -webkit-box;
            -webkit-line-clamp: 2; // 最多显示2行
            line-clamp: 2; // 标准属性
            -webkit-box-orient: vertical;
            max-height: 2.4em; // 2行的高度

            // 活跃状态增强效果，与主题色协调
            background: linear-gradient(to bottom, #FFFFFF, #3de9fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 6px rgba(61, 233, 250, 0.4);
            filter: drop-shadow(0 0 8px rgba(61, 233, 250, 0.6));

            // 活跃状态发光效果
            position: relative;

            &::after {
                content: '';
                position: absolute;
                top: -2px;
                left: -4px;
                right: -4px;
                bottom: -2px;
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, transparent 100%);
                border-radius: 6px;
                z-index: -1;
                opacity: 0.8;
            }
        }

        // 活跃项目的整体效果
        &:has(.active) {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(24, 64, 104, 0.3) 100%);
            border: 1px solid rgba(61, 233, 250, 0.4);
            box-shadow: 0 4px 20px rgba(61, 233, 250, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
    }
}
</style>
