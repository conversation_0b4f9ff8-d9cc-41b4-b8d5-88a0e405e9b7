<template>
    <div v-if="menuList.length" class="menu">
        <div v-for="item in menuList" :key="item.id"
             class="item"
             :class="[activeName === item.name ? 'active-item' : '']"
             @click="clickMenu(item)">
            <div class="img">
                <img :src="getImgUrl(item.imgName)">
            </div>
            <div class="name alibaba-bold" :class="[activeName === item.name ? 'active' : '']">
                {{ item.name }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
    getCookie,
    removeToken,
    setCookie
} from '@/utils/cookie'
import { useAppStore } from '@/stores/app'
import { useMitt } from '@/hooks/mitt'

const api = inject('$api')
const router = useRouter()
const store = useAppStore()

const menuList = ref([])
const list = ref([])
const activeName = ref('综合态势')
const secondName = ref('')


const emitter = useMitt()
emitter.miitOn('Switch_System', (data) => {
    console.log(data)
    if (window.cmd != data.selectedFloorId) {
        return;
    }
    if (data.command.toLowerCase() == 'access') {
        data = "door";
    }
    menuList.value.forEach(item => {
        if (item.cmd == data.command) {
            clickMenu(item)
        }
    })
})

onMounted(() => {

    let firstMenuName = sessionStorage.getItem("firstMenuName")
    if (firstMenuName) {
        activeName.value = firstMenuName;
    }

    let secondMenuName = sessionStorage.getItem("secondMenuName")
    if (secondMenuName) {
        secondName.value = secondMenuName;
    }

    getProjectMenu()

});

const getProjectMenu = () => {
    if (getCookie("gh_projectId") != 0) {
        api.getMenuList({
            configFlag: false,
            projectId: getCookie("gh_projectId"),
            menuType: [1, 2],
            isPcMenu: true,
            enable: true,
        }).then((res) => {
            menuList.value = res.data;
            if (!getCookie("funMenus")) {
                clickMenu(res.data[0]);
            }
        })
    }
}

const clickMenu = (item) => {
    if (item) {
        if (item.component && item.component.startsWith("dialog_")) {
            store.SET_DIALOG( {
                name: item.name,
                component: item.component.split('_')[1]
            })
            return;
        }

        //重置二级菜单
        if (item.menuType == 1) {
            list.value = [];
            sessionStorage.removeItem("second")
            emitter.miitEmit('second', {
                list: []
            })
            //一级菜单名称
            sessionStorage.setItem("firstMenuName", item.name)
        }
        //点击的是一级菜单
        if (item.menuType == 1 && item.children && item.children.length > 0) {
            list.value = item.children;

            sessionStorage.setItem("second", JSON.stringify(item.children))
            //记录二级菜单,刷新使用
            emitter.miitEmit('second', {
                list: item.children
            })
        }

        if (item.menuType == 1 && activeName.value !== item.name) {
            activeName.value = item.name
        }


        //一级菜单没有绑定组件，默认显示子集的第一个
        if (item.menuType == 1 && !item.component && item.children.length > 0) {
            item = item.children[0];

            secondName.value = item.name;
            sessionStorage.setItem("secondMenuName", item.name)
            emitter.miitEmit('secondMenuName', {
                name: item.name
            })

        }

        if (item.component) {
            if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 2) { //主要是各个子系统第一个
                item.secondComponent = item.component.split("_")[1];
                item.component = item.component.split("_")[0];
            } else if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 3) //子系统弹窗
            {
                let components = item.component.split("_");
                item.component = components[0]
                item.secondComponent = components[1]
                item.popName = components[2]
            }
            removeToken("funMenus")
            setCookie('funMenus', JSON.stringify({
                id: item.id,
                name: item.name,
                component: item.component,
                secondComponent: item.secondComponent,
                code: item.code, //菜单编码
                diagramId: item.diagramId,
                popName: item.popName,
                model: item.model,
                showFloor: item.showFloor,
            }))

            store.SET_FUN_MENU(item)
        }

        if (item.cmd) {
            emitter.miitEmit('ue', {
                type: 'menu',
                value: item.cmd.trim()
            })
        }

        router.push({
            path: '/overview'
        })

        emitter.miitEmit('goHome')


    }

}
const getImgUrl = (name) => {
    return new URL(`/src/assets/images/menu/${name}.png`, import.meta.url).href
}




</script>

<style lang="scss" scoped>
// 参考 .left 和 .right 面板样式的底部菜单美化
.menu {
    position: fixed;
    width: 1000px;
    left: 50%;
    bottom: 15px;
    transform: translateX(-50%);
    z-index: 100;
    height: 80px;

    // 毛玻璃背景效果，参考 .left 面板样式
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.85) 0%, rgba(24, 64, 104, 0.75) 100%);
    border: 1px solid rgba(61, 233, 250, 0.4);
    border-radius: 20px;
    padding: 12px 20px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.15),
                0 0 20px rgba(61, 233, 250, 0.1);

    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;

    // 整体光效背景
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
        opacity: 1;
        pointer-events: none;
        border-radius: 20px;
    }

    // 顶部光线效果
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 10%;
        right: 10%;
        height: 2px;
        background: linear-gradient(90deg, transparent 0%, rgba(61, 233, 250, 0.6) 50%, transparent 100%);
        border-radius: 1px;
    }

    .item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px 16px;
        border-radius: 12px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        backdrop-filter: blur(4px);
        border: 1px solid transparent;
        min-width: 100px;

        // 悬停效果
        &:hover {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.15) 0%, rgba(17, 150, 252, 0.1) 100%);
            border-color: rgba(61, 233, 250, 0.3);
            box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);

            .img img {
                filter: drop-shadow(0 2px 8px rgba(61, 233, 250, 0.4));
                transform: scale(1.1);
            }

            .name {
                text-shadow: 0 0 8px rgba(61, 233, 250, 0.3);
            }
        }

        .img {
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: 28px;
                height: 28px;
                transition: all 0.3s ease;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
            }
        }

        .name {
            font-family: "Alibaba-PuHuiTi", "DOUYU";
            font-weight: 600;
            font-size: 15px;
            line-height: 1.2;
            letter-spacing: 1px;
            text-align: center;
            transition: all 0.3s ease;
            white-space: nowrap;

            // 默认状态渐变文字
            background: linear-gradient(135deg, #E6F4FF 0%, #B8C5D1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .active {
            font-family: "Alibaba-PuHuiTi", "DOUYU";
            font-weight: 700;
            font-size: 16px;
            line-height: 1.2;
            letter-spacing: 1px;
            text-align: center;

            // 激活状态的高亮渐变
            background: linear-gradient(135deg, #3de9fa 0%, #1196FC 50%, #7ED4BB 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 12px rgba(61, 233, 250, 0.5);
        }

        // 激活状态的整个item样式 - 通过添加active-item类来实现
        &.active-item {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.15) 100%);
            border-color: rgba(61, 233, 250, 0.4);
            box-shadow: 0 4px 16px rgba(61, 233, 250, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);

            .img img {
                filter: drop-shadow(0 2px 8px rgba(61, 233, 250, 0.5));
                transform: scale(1.05);
            }
        }
    }

    // 入场动画
    animation: slideInUp 0.6s ease-out;
}

// 底部菜单入场动画
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(100px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

// 响应式适配
@media (max-width: 1200px) {
    .menu {
        width: 90%;
        max-width: 900px;

        .item {
            min-width: 80px;
            padding: 6px 12px;

            .img img {
                width: 24px;
                height: 24px;
            }

            .name {
                font-size: 14px;
            }

            .active {
                font-size: 15px;
            }
        }
    }
}
</style>
