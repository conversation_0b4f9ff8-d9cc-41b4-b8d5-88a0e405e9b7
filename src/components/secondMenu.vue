<script>

import {
    useRouter
} from 'vue-router'
import { getCookie, setCookie, removeToken } from '@/utils/cookie'
import { computed, defineComponent, inject, onMounted, onBeforeUnmount, reactive, toRefs, watch, ref } from 'vue'
import { useAppStore } from '@/stores/app'
import { useMitt } from '@/hooks/mitt'

export default defineComponent({

  setup() {
    const state = reactive({
      list: [],
      activeName: '围护结构',
      isProcessingMenuChange: false,
    })

    const store = useAppStore();
    const router = useRouter();


    const emitter = useMitt()

    emitter.miitOn('second', (data) => {
      state.list = data.list
    })

    emitter.miitOn('secondMenuName', (data) => {
      state.activeName = data.name
    })



    const clickMenu = (item) => {
      state.activeName = item.name
      sessionStorage.setItem('secondMenuName', item.name)

      if (item.component) {
        if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 2) { //主要是各个子系统第一个
          item.secondComponent = item.component.split("_")[1];
          item.component = item.component.split("_")[0];
        } else if ((item.menuType == 1 || item.menuType == 2) && item.component.includes("_") && item.component.split("_").length == 3) //子系统弹窗
        {
          let components = item.component.split("_");
          item.component = components[0]
          item.secondComponent = components[1]
          item.popName = components[2]
        }
        removeToken("funMenus")
        setCookie('funMenus', JSON.stringify({
          id: item.id,
          name: item.name,
          component: item.component,
          secondComponent: item.secondComponent,
          code: item.code, //菜单编码
          diagramId: item.diagramId,
          popName: item.popName,
          model: item.model,
          showFloor: item.showFloor,
        }))

        store.SET_FUN_MENU(item)
      }

      if (item.cmd) {
        emitter.miitEmit('ue', {
          type: 'menu',
          value: item.cmd.trim()
        })
      }

      router.push({
        path: '/overview'
      })


    }



    onMounted(() => {


      // 初始化数据
      const menu = sessionStorage.getItem('second')
      if (menu) {
        state.list = JSON.parse(menu)
      }

      const menuName = sessionStorage.getItem('secondMenuName')
      if (menuName) {
        state.activeName = menuName
      }




    })



    return {
      ...toRefs(state),
      clickMenu,
    }
  },

})
</script>

<template>
  <div class="menu center">
    <div v-for="(item, index) in list" :key="index" class="item center"
      :class="[item.name === activeName ? 'active' : '']" @click="clickMenu(item)">
      <span class="title" :class="[item.name === activeName ? 'active_title' : '']">{{ item.name }}</span>
      <div v-if="item.name === activeName" class="menu_bottom" />
      <div v-if="item.name === activeName" class="menu_top" />
      <div v-if="item.name === activeName" class="active_bg" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.menu {
  position: absolute;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  width: 1034px;
  height: 53px;
  z-index: 100;

  .item {
    width: 102px;
    height: 26px;
    background: url('@/assets/images/menu/menu_bg.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    position: relative;
  }

  .item:not(:last-child) {
    margin-right: 6px;
  }

  .menu_bottom {
    position: absolute;
    bottom: -11px;
    left: 0;
    width: 105px;
    height: 6px;
    background: url('@/assets/images/menu/active_bottom.png') no-repeat;
    background-size: 100% 100%;
  }

  .menu_top {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 11px;
    background: url('@/assets/images/menu/active_top.png') no-repeat;
    background-size: 100% 100%;
  }

  .active_bg {
    position: absolute;
    bottom: -7px;
    left: 0;
    width: 102px;
    height: 11px;
    background: url('@/assets/images/menu/active_bg.png') no-repeat;
    background-size: 100% 100%;
  }
}

.title {
  color: #E9FDFF;
  text-align: center;
  font-family: "Alibaba-PuHuiTi";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.active {
  background: url('@/assets/images/menu/menu_active.png') no-repeat !important;
  background-size: 100% 100% !important;
}

.active_title {
  text-align: center;
  text-shadow: 0px 1px 0px rgba(0, 0, 0, 0.64);
  font-family: "Alibaba-PuHuiTi";
  font-size: 12px !important;
  font-style: normal;
  font-weight: bolder;
  line-height: normal;
  color: white;

}
</style>
