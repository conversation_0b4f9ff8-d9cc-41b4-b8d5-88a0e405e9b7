<template>
    <el-dialog align-center :append-to-body="true" custom-class="cctv-dialog" draggable :title="title" @close="Close"
        v-model="cvisiable">

        <div style="height: 600px">
            <iframe frameborder="no" border="0" style="width: 100%; height: 100%" :src="url"></iframe>
        </div>

    </el-dialog>
</template>

<script>
import { useMitt } from '@/hooks/mitt';



export default defineComponent({
    name: 'VariableDiglog',

    props: ['url', 'visiable'],

    setup(props, context) {
        const api = inject('$api')
        const fullRef = ref();
        const state = reactive({
            title: '',
            cvisiable: false,
            url: ''
        });

        const emitter = useMitt()
        emitter.miitOn('ba_close', (val) => {
            state.cvisiable = false;
        });
        watch(() => props.visiable, (val) => {
            state.cvisiable = val;
        });
        const Close = () => {
            state.cvisiable = false;
            state.url = '';
            // emitter.emit('ue', {
            //     type: 'flyToFloorView'
            // });

        };
        const opened = async (item) => {
            state.cvisiable = true;
            if (item && item.value) {
                state.url = window.PROD_9008_API+"/#/preview?diagramId=" + item.value;

                api.getDiagramFile({ id: item.value }).then((res) => {
                    state.title = res.data[0].name;
                });
            }
        };


        return {
            ...toRefs(state),
            Close,
            opened,

        }
    }
})
</script>

<style lang="scss" scoped>
.h5video {
    width: 100%;
    height: 480px;
    position: relative;
}

.ptz {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 6008;
    border: 0 !important;

    div {
        border: 0 !important;
    }

    display: flex;
    align-items: center;

    .ptzbtn {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        align-content: space-between;
        align-items: center;
        margin-right: 20px;

        .mid {
            display: flex;
            justify-content: space-between;

            div:nth-of-type(1) {
                margin-right: 20px;
            }
        }
    }
}
</style>
