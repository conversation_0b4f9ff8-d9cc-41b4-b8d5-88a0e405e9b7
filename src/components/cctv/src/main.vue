<template>
    <el-dialog align-center :append-to-body="true" custom-class="cctv-dialog" draggable :title="title" @close="Close"
        v-model="cvisiable" >
        <div>
            <video v-if="type == 1" @dblclick="full" ref="fullRef" class="h5video" :id="token" autoplay></video>
            <div style="height:480px" v-if="type == 2" ref="fullRef" class="h5video" :id="token" autoplay></div>
        </div>
        <div class="ptz" :style="{ display: ptzEnable ? 'flex' : 'none' }">
            <div class="ptzbtn">
                <div class="up">
                    <div>
                        <el-button @mousedown="PtzAction('up')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                            circle>
                            <i class="iconfont iconshangjiantou"></i>
                        </el-button>
                    </div>
                </div>
                <div class="mid">
                    <div>
                        <el-button @mousedown="PtzAction('left')" @mouseup="PtzAction('stop')" type="primary"
                            size="mini" circle>
                            <i class="iconfont iconzuojiantou"></i>
                        </el-button>
                    </div>
                    <div>
                        <el-button @mousedown="PtzAction('right')" @mouseup="PtzAction('stop')" type="primary"
                            size="mini" circle>
                            <i class="iconfont iconyoujiantou"></i>
                        </el-button>
                    </div>
                </div>
                <div class="down">
                    <div>
                        <el-button @mousedown="PtzAction('down')" @mouseup="PtzAction('stop')" type="primary"
                            size="mini" circle>
                            <i class="iconfont iconxiajiantou"></i>
                        </el-button>
                    </div>
                </div>
            </div>
            <div class="ptzzoom">
                <div>
                    <el-button @mousedown="PtzAction('zoomin')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                        circle>
                        <i class="iconfont iconjian"></i>
                    </el-button>
                </div>
                <div>
                    <el-button @mousedown="PtzAction('zoomout')" @mouseup="PtzAction('stop')" type="primary" size="mini"
                        circle>
                        <i class="iconfont icontianjia"></i>
                    </el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { useMitt } from '@/hooks/mitt'
import '@/lib/h5splayer'
import {
    H5sPlayerCreate
} from '@/lib/h5splayerhelper'
import axios from 'axios'
import {
    defineComponent,
    
    reactive,
    watch,
    toRefs,
    nextTick,
    inject
} from 'vue'

export default defineComponent({
    name: 'VariableDiglog',

    props: ['url', 'visiable'],

    setup(props, context) {
        const api = inject('$api')
        const fullRef = ref();
        const state = reactive({
            title: 'cam',
            conf: null,
            cvisiable: false,
            token: null,
            jessibuca: null,
            useWCS: false,
            useMSE: false,
            useOffscreen: false,
            type: 1,
            data: null,
            ptzEnable: false,
            session: null,
            htmlopen: false
        });
        const emitter = useMitt()
        emitter.miitOn('close', (val) => {
            state.cvisiable = false;
            if (state.conf) {
                state.conf.disconnect();
                state.conf = null;
            }
        });
        watch(() => props.visiable, (val) => {
            state.cvisiable = val;
        });
        const Close = () => {
            state.cvisiable = false;
            if (state.conf) {
                state.conf.disconnect();
                state.conf = null;
            }
            if (!state.htmlopen) {

                emitter.miitEmit('ue', {
                    type: 'flyToFloorView'
                });
            }

        };
        const Closed = () => {
            state.cvisiable = false;
            if (state.conf) {
                state.conf.disconnect();
                state.conf = null;
            }
           

        };
        const opened = async (item, htmlopen) => {
            if (state.conf) {
                state.conf.disconnect();
                state.conf = null;
            }
            state.htmlopen = htmlopen;
            state.cvisiable = true;
            if (item.token) {
                await nextTick();
                // if (!state.session) {
                //     await getSession()
                // }
                api.getCamByToken(item.token).then(res => {
                    if (res.data) {
                        state.session = res.data.session;
                        state.token = item.token;
                        state.title = res.data.strName; //摄像机名称
                        let conf = {
                            videoid: state.token,
                            protocol: window.location.protocol, //http: or https:
                            host: res.data.ip + ":" + res.data.port, //localhost:8080
                            rootpath: '/', // '/'
                            token: state.token,
                            hlsver: 'v1', //v1 is for ts, v2 is for fmp4
                            session: res.data.session //session got from login  注意现在h5新平台都要登录
                        };
                        setTimeout(() => {
                            state.conf = H5sPlayerCreate(conf);
                            state.conf.connect();
                        });

                    }
                });
            }
        };

        const PtzAction = (action) => {
            if (state.type == 2) {
                let param = {
                    ip: state.data.ip,
                    username: state.data.username,
                    password: state.data.password,
                    profileToken: state.data.profileToken,
                }
                if (action == "stop") {
                    //onvif协议控制云台停止操作
                    api.stopMovePtz(param);
                } else {
                    api.movePtz(Object.assign({}, param, {
                        position: action
                    }));
                }
            } else {
                if (state.data) {
                    Ptz(state.data.token, action, "http://" + state.data.server + ":" + state.data.port, "");
                }
            }
        };
        const Ptz = (token, action, host, session) => {
            let ptzcmd = "token=" + token + "&action=" + action + "&speed=0.5";
            axios.get(host + "/api/v1/Ptz?" + ptzcmd + "session=" + session).then((result) => {
                console.log(result);
            }).catch((err) => {
                console.log(err);
            });
        };
        const full = () => {
            const element = fullRef.value;
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullScreen();
            }
        }
        const getSession = async (ip, port) => {
            const {
                data
            } = await api.getSession({
                // ip,
                port: 2500
            })
            state.session = data;
        }

        return {
            ...toRefs(state),
            Close,
            opened,
            PtzAction,
            Ptz,
            fullRef,
            full,
            getSession,
            Closed,
        }
    }
})
</script>

<style lang="scss" scoped>
.h5video {
    width: 100%;
    height: 480px;
    position: relative;
}

.ptz {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 6008;
    border: 0 !important;

    div {
        border: 0 !important;
    }

    display: flex;
    align-items: center;

    .ptzbtn {
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        align-content: space-between;
        align-items: center;
        margin-right: 20px;

        .mid {
            display: flex;
            justify-content: space-between;

            div:nth-of-type(1) {
                margin-right: 20px;
            }
        }
    }
}


</style>
