<template>
    <div class="floor">
        <div class="buildinglist">
      <div
        v-for="item in list" :key="item.id" :class="[item.id === areaId ? 'active' : '']"
        class="num center cursor" @click="clickFloor(item, true)"
      >
        <span> {{ item.name }}</span>
      </div>
    </div>

    <div class="building center cursor" @click="clickFloor({ id: -1, name: '' }, true)">
      <div class="pie center">
        <i class="iconfont iconnenghao" />
      </div>
    </div>
    </div>
</template>

<script>

import { useMitt } from '@/hooks/mitt'
import { useAppStore } from '@/stores/app'
import {
    getCookie,
    setCookie
} from '@/utils/cookie'




export default defineComponent({
    setup() {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            list: [],
            areaId: null,
            showQiaoJia: false,
            showFa: false
        })
        const emitter = useMitt()

        //点击首页菜单，楼层要回到整个楼栋
        emitter.miitOn('goHome', () => {
            state.areaId = -1;
            store.SET_NAV_AREA({ name: "", id: -1 })
        });

        emitter.miitOn('changeFloor', (data) => {
            if (parseInt(data.value)>= 0) {
                state.list.forEach(d => {
                    if (d.bimFloor == parseInt(data.value)) {
                        clickFloor(d, false)
                    }
                })
            }
        });

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });


        onMounted(() => {

        });



        onMounted(() => {
            getArea()
        })

        const getArea = () => {
            api.getProjectArea({
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                if (res.data && res.data.length > 0) {
                    if (res.data[0] && res.data[0].children.length > 0) {
                        setCookie('area', res.data[0].children[0].id)
                        state.list = res.data[0].children;
                    }
                }

            })
        }
        emitter.miitOn('Switch_Floor', (data) => {
            console.log(data)
            if (window.cmd != data.selectedFloorId) {
                return
            }
            emitter.miitEmit('ue', {
                type: 'openFloor',
                value: parseInt(data.command.toLowerCase().replace('floor', '').trim()) - 1
            })
        });

        const clickFloor = (item, send) => {
            state.areaId = item.id;
            store.SET_NAV_AREA(item)
            if (item.name && send) {
                emitter.miitEmit('ue', {
                    type: 'openFloor',
                    value: parseInt(item.bimFloor)
                })
            } else if (!item.name) {
                emitter.miitEmit('ue', {
                    type: 'menu',
                    value: activeMenus.value.cmd
                })
            }
        }




        return {
            ...toRefs(state),

            // projectId,
            clickFloor

        }
    },
})
</script>

<style lang="scss" scoped>
.floor {
    position: absolute;
    right: 408px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 101;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
}

.buildinglist {
    display: flex;
    width: 50px;
    height: 233px;
    padding: 10px 8px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 50px;
    border: 2px solid #3A6597;
    background: rgba(10, 32, 58, 0.55);
    box-shadow: 0px 0px 20px 0px rgba(5, 17, 36, 0.16);
    overflow-y: auto;
    
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE和Edge */
    
    /* 针对webkit浏览器(Chrome, Safari等) */
    &::-webkit-scrollbar {
        display: none;
    }

    .num {
        display: flex;
        width: 40px;
        height: 40px;
        padding: 10px 8px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 17px;
        flex-shrink: 0;
        border-radius: 50px;
        border: 2px solid #3A6597;
        background: rgba(10, 32, 58, 0.55);
        box-shadow: 0px 0px 20px 0px rgba(5, 17, 36, 0.16);

        span {
            color: #FFF;
            text-align: center;
            font-family: "Alibaba-PuHuiTi";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            /* 83.333% */

        }
    }

    .active {

        font-size: 18px;
        font-family: "BEBAS";
        font-weight: 400;
        color: rgba(255, 255, 255, 0);

        span {
            background: linear-gradient(0deg, #1D88EC 0%, #2FAAF0 49.755859375%, #2AF3F5 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        border: 2px solid #2D86C3 !important;

        background: #0B4488 !important;

    }

}

.building {
    margin-top: 10px;
    display: flex;
    width: 40px;
    height: 40px;
    padding: 10px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 94px;
    border: 2px solid #415C73;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.43) 0%, rgba(0, 0, 0, 0.43) 100%), #112C48;

    background-blend-mode: overlay, normal;

    .pie {
        display: flex;
        width: 40px;
        height: 40px;
        padding: 7.579px 8px 8.421px 8px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        border-radius: 60px;
        border: 1px solid #F3F9FF;
        background: linear-gradient(161deg, rgba(255, 255, 255, 0.42) 14.27%, rgba(255, 255, 255, 0.00) 72.39%), linear-gradient(180deg, #CEDFEE 0%, #8DADCD 100%);
        box-shadow: 0px 4px 8px 0px #0B2540;

        i {
            font-size: 24px;
            color: 'rgba(17, 44, 72, 1)'
        }
    }
}
</style>
