<template>
<Transition name="fade" mode="out-in" appear>
    <div class="diagram" v-if="show">
        <div class="header">
            <div class="title">
                <img src="@/assets/images/common/head.png">
                <div>{{name}}</div>
            </div>
            <div class="close cursor" @click="close">
            </div>
        </div>
        <div class="content">
            <slot></slot>
        </div>
    </div>
</Transition>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";
export default defineComponent({
    props: ['show','name'],

    setup(props,{emit}) {
        const state = reactive({

        })
        const close=()=>{
            emit('update:show',false)
        }
        return {
            ...toRefs(state),
            close,

        }
    },
})
</script>

<style lang="scss" scoped>
div {
    box-sizing: border-box;
}

.diagram {
    height: calc(100% - 100px - 139px);
    width: 1100px;
    background: url("/src/assets/images/dialog/panel_bg.png") no-repeat;
    background-size: 100% 100%;
    border: 1px solid rgba(61, 233, 250, 0.3);
    border-radius: 15px;
    margin-top: 100px;
    padding: 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);

    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 500;
    overflow: hidden;

    .header {
        background: linear-gradient(90deg, rgba(45, 85, 135, 0.3) 0%, transparent 100%);
        border-bottom: 1px solid rgba(61, 233, 250, 0.2);
        height: 50px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;
        position: relative;
        z-index: 1;

        .title {
            height: 100%;
            display: flex;
            align-items: center;
            font-size: 18px;
            font-family: "DOUYU";
            font-weight: 500;
            color: #E6F4FF;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin-left: 8px;

            img {
                width: 20px;
                height: 20px;
                margin-right: 8px;
                filter: drop-shadow(0 2px 4px rgba(61, 233, 250, 0.3));
            }
        }

        .close {
            height: 32px;
            width: 32px;
            background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.8) 100%);
            border: 1px solid rgba(61, 233, 250, 0.4);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '×';
                font-size: 20px;
                font-weight: bold;
                color: #e6f4ff;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            }

            &:hover {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.3) 0%, rgba(17, 150, 252, 0.4) 100%);
                border-color: rgba(61, 233, 250, 0.6);
                box-shadow: 0 0 12px rgba(61, 233, 250, 0.4);
                transform: scale(1.05);
            }

            &:active {
                transform: scale(0.95);
            }
        }
    }

    .content {
        padding: 20px;
        height: calc(100% - 60px);
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;
        z-index: 1;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
            width: 8px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(16, 52, 87, 0.4);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.7) 0%, rgba(17, 150, 252, 0.5) 100%);
            border-radius: 4px;
            transition: background 0.3s ease;

            &:hover {
                background: linear-gradient(135deg, rgba(61, 233, 250, 0.9) 0%, rgba(17, 150, 252, 0.7) 100%);
            }
        }

        &::-webkit-scrollbar-corner {
            background: transparent;
        }
    }
}

/* 过渡动画优化 */
.fade-enter-active,
.fade-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.fade-enter-from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
}

.fade-leave-to {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
}
</style>
