<template>
  <div class="line_box">
    <div class="line"></div>
    <div class="box"></div>
  </div>
</template>

<style lang="scss" scoped>
.line_box {
  display: flex;
  align-items: center;

  .line {
    height: 49px;
    border-left: 1px solid rgba(255, 255, 255, 0.5);
    margin: 0 15px;
  }

  .box {
    width: 14px;
    height: 58px;
    border-style: solid;
    border-width: 1px;
    border-left-color: rgba(255, 255, 255, 0.5);
    border-top-color: rgba(255, 255, 255, 0.5);
    border-right: none;
    border-bottom: none;
  }
}
</style>
