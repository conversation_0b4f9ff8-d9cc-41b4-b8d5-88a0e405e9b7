<template>
  <div class="wrapper">
    <div class="device_count">
      <panel title="设备统计"></panel>
      <div class="count_echart">
        <subEchart :echartData="echartData" :colors="colors"></subEchart>
      </div>
    </div>
    <div class="alarm_count">
      <panel title="报警统计"></panel>
      <div class="count">
        <div class="box">
          <div class="num">6</div>
          <div class="tit">普通报警</div>
        </div>
        <div class="box">
          <div class="num">0</div>
          <div class="tit">严重报警</div>
        </div>
        <div class="slash">/</div>
        <div class="box">
          <div class="num">60</div>
          <div class="tit">紧急报警</div>
        </div>
      </div>
    </div>
    <div class="log">
      <el-scrollbar>
        <div v-for="item in list" :key="item.id" class="list">
          <div class="icon">
            <i class="iconfont iconcaozuorizhi"></i>
          </div>
          <div class="info">
            <div class="name">
              {{ item.name }}
              <div class="time">{{item.time}}</div>
            </div>
            <div class="content"> {{item.content}}</div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>
<script lang="ts">
import subEchart from "@/components/echarts/subEchart.vue";
import { reactive, toRefs } from "vue";
export default {
  components: {
    subEchart,
  },
  setup() {
    const state = reactive({
      colors: [
        "#0E7CE2",
        "#FF8352",
        "#E271DE",
        "#F8456B",
        "#00FFFF",
        "#4AEAB0",
      ],
      echartData: [
        {
          name: "设备1",
          value: "400",
        },
        {
          name: "设备2",
          value: "364",
        },
        {
          name: "设备3",
          value: "354",
        },
        {
          name: "设备4",
          value: "248",
        },
      ],
      list: [
        {
          name: "新风机1",
          time: "2021-07-29",
          content: "温度 张三 调整 为20",
        },
        {
          name: "新风机",
          time: "2021-07-29",
          content: "温度 张三 调整 为20",
        },
        {
          name: "新风机2",
          time: "2021-07-29",
          content: "温度 张三 调整 为20",
        },
        {
          name: "新风机3",
          time: "2021-07-29",
          content: "温度 张三 调整 为20",
        },
        {
          name: "新风机4",
          time: "2021-07-29",
          content: "温度 张三 调整 为20",
        },
      ],
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>
<style lang="scss" scoped>
.wrapper {
  height: 100%;

  .device_count {
    height: 30%;
    .count_echart {
      height: calc(100% - 49px);
    }
  }
  .alarm_count {
    height: 20%;

    .slash {
      line-height: 73px;
      font-size: 50px;
      font-weight: 100;
      color: rgba(255, 255, 255, 0.3);
    }
    .count {
      display: flex;
      height: calc(100% - 49px);
      align-items: center;
      color: #fff;

      .box {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        height: 60%;

        .tit {
          font-size: 14px;
          font-family: "Alibaba-PuHuiTi";
          font-weight: 400;
          color: #778897;
        }
        .num {
          font-size: 25px;
          font-family: "DINAlternate-Bold";
          font-weight: bold;
          color: #3de9fa;
          margin-bottom: 2px;
        }
      }
    }
  }
  .log {
    height: 50%;

    .list {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 0 5px 0;
      line-height: 25px;
      border-bottom: 1px dashed rgba(96, 105, 109, 0.2);
      .icon {
        width: 40px;
        height: 40px;
        background: #1c2022;
        display: flex;
        align-items: center;
        margin-right: 8px;
        .iconfont {
          color: #1bc0ed;
          font-size: 22px;
          margin: 0 auto;
        }
      }
      .info {
        flex: 1;
        font-family: "PingFangSC-Medium", "PingFang SC";

        .name {
          flex: 1;
          display: flex;
          justify-content: space-between;
          font-size: 16px;
          font-weight: 500;
          color: #ffffff;

          .time {
            font-size: 12px;
            font-weight: 400;
            color: #687287;
          }
        }
        .content {
          font-size: 12px;
          font-family: "Alibaba-PuHuiTi";
          font-weight: 400;
          color: #889cc3;
        }
      }
    }
  }
}
</style>