<template>
  <div class="no_data">
    <div class="icon"> <img :src="noData" alt /></div>
    {{ msg||'暂无数据' }}
  </div>
</template>
<script>
import noDataImg from '@/assets/images/noData.png'

export default {
  props: {
    msg: {
      type: String,
      default: '',
    },
  },
  data () {
    return {
      noData: noDataImg,
      // msg: '暂无数据',
    }
  },
}
</script>
<style lang="scss" scoped>
.no_data {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-family: "Alibaba-PuHuiTi";
  font-weight: 400;
  color: #889cc3;
  height: 100%;
  flex:1;
  .icon {
    width: 41px;
    height: 32px;
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
