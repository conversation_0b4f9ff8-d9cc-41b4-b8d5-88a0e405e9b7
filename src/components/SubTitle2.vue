<template>
    <div class="sub_title">
      <div class="item">
        <!-- <img :src="icon" alt /> -->
        <span class="msg">{{ title }}</span>
      </div>
      <slot />
    </div>
  </template>
  
  <script lang="ts">
  import { defineComponent, reactive, toRefs } from "vue";
  export default defineComponent({
    props: {
      title: String,
    },
    setup() {
      const state = reactive({
      });
      return {
        ...toRefs(state),
      };
    },
  });
  </script>
  
  <style lang="scss" scoped>
  .sub_title {
    display: flex;
    height: 40px;
    line-height: 40px;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(
      360deg,
      rgba(64, 112, 124, 0.38) 0%,
      rgba(199, 223, 255, 0) 100%
    );
    margin-bottom: 9px;
  
    .item {
      flex: 1;
  
      .msg {
        color: #c7dfff;
        font-size: 18px;
        font-family: "PingFangSC-Medium", "PingFang SC";
        font-weight: 500;
        margin-left: 5px;
      }
  
      img {
        width: 22px;
        height: 25px;
        vertical-align: middle;
      }
  
      .icon {
        display: inline-block;
        background-size: contain;
        background-repeat: no-repeat;
        width: 22px;
        height: 25px;
        vertical-align: middle;
      }
    }
  }
  </style>
  