<template>
  <div class="content">
    <el-form :inline="true" class="search_box form_inline" size="small">
      <el-form-item label="操作人">
        <el-select placeholder="请选择" v-model="userId">
          <el-option v-for="item in users" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button class="searchBtn" type="text" @click="search">查询</el-button>
      </el-form-item>
    </el-form>
    <div class="table">
      <el-table :data="list" :height="tableHeight"  fit table-layout="auto">
        <template #empty>
          <no-data />
        </template>
        <el-table-column prop="logTime" label="时间" align="center"></el-table-column>
        <el-table-column prop="standardName" label="设备指标" align="center"></el-table-column>
        <el-table-column prop="content" label="记录内容" align="center">
          <template #default="scope">
            <span>
              数值从{{ scope.row.oldValue }}改变为{{ scope.row.newValue }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="操作者" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="page">
      <el-pagination :total="total" :page="page" :size="size" @pagination="handleCurrentChange" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import {
  getCookie
} from '@/utils/cookie'
import {
  reactive,
  toRefs,
  onMounted,
  
  computed,
  watch
} from 'vue'
import { useAppStore } from '@/stores/app'


export default {

  props: ['deviceId'],
  setup () {
    const api = inject('$api')
    const store = useAppStore()
    const state = reactive({
      tableHeight: window.innerHeight * 0.60,
      list: [],
      page: 1,
      size: 10,
      total: 0,
      userId: '',
      date: [],
      users: [],
    })
    const projectId = computed(() => {
      return store.projectId || getCookie('gh_projectId')
    })
    watch(projectId, (val) => {
      if (val) {
        getUserList()
        getRunManualLogList()
      }
    })
    onMounted(() => {
      state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
      state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
      getUserList()
      getRunManualLogList()
    })
    const getUserList = () => {
      api.getUser({
        projectId: getCookie('gh_projectId'),
        status: 1,
      }).then((res) => {
        state.users = res.data
      })
    }
    const getRunManualLogList = () => {
      api.getRunManualLog({
        projectId: getCookie('gh_projectId'),
        type: 2,
        bt: dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
        et: dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
        tag: 1,
        page: state.page,
        size: 10,
      }).then((res) => {
        state.list = res.data
        state.total = res.total
      })
    }
    const search = () => {
      state.page = 1
      getRunManualLogList()
    }
    const handleCurrentChange = (page) => {
      state.page = page
      getRunManualLogList()
    }
    return {
      ...toRefs(state),
      getUserList,
      getRunManualLogList,
      search,
      handleCurrentChange,
      projectId
    }
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
  height: calc(100% - 56px);
  background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
  border: 1px solid rgba(45, 85, 135, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  .search_box {
    margin-bottom: 16px;
    padding: 16px;
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
    border: 1px solid rgba(45, 85, 135, 0.2);
    border-radius: 6px;

    :deep(.el-form-item) {
      margin-bottom: 0;

      .el-form-item__label {
        color: #e6f4ff;
        font-size: 14px;
        font-weight: 500;
      }
    }

    :deep(.el-select) {
      .el-select__wrapper {
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
        border: 1px solid rgba(45, 85, 135, 0.5);
        border-radius: 6px;
        box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.3);

        &:hover {
          border-color: rgba(45, 135, 230, 0.7);
          box-shadow: 0 0 8px rgba(45, 135, 230, 0.2);
        }

        &.is-focus {
          border-color: #2d87e6;
          box-shadow: 0 0 12px rgba(45, 135, 230, 0.3);
        }

        .el-select__selected-item {
          color: #e6f4ff;
        }

        .el-select__placeholder {
          color: rgba(230, 244, 255, 0.5);
        }
      }
    }

    :deep(.el-date-editor) {
      .el-input__wrapper {
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
        border: 1px solid rgba(45, 85, 135, 0.5);
        border-radius: 6px;
        box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.3);

        &:hover {
          border-color: rgba(45, 135, 230, 0.7);
          box-shadow: 0 0 8px rgba(45, 135, 230, 0.2);
        }

        &.is-focus {
          border-color: #2d87e6;
          box-shadow: 0 0 12px rgba(45, 135, 230, 0.3);
        }
      }

      .el-input__inner {
        color: #e6f4ff;

        &::placeholder {
          color: rgba(230, 244, 255, 0.5);
        }
      }
    }

    .searchBtn {
      background: linear-gradient(135deg, #2d87e6 0%, #1976d2 100%);
      border: 1px solid #2d87e6;
      color: #ffffff;
      padding: 8px 16px;
      border-radius: 4px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #5ba3f5 0%, #2196f3 100%);
        box-shadow: 0 0 10px rgba(45, 135, 230, 0.4);
        transform: translateY(-1px);
      }
    }
  }

 

  .page {
    margin-top: 16px;
    padding: 12px 0;
    border-top: 1px solid rgba(45, 85, 135, 0.4);
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      .el-pager li {
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
        border: 1px solid rgba(45, 85, 135, 0.4);
        color: #e6f4ff;
        border-radius: 4px;
        margin: 0 2px;
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(45, 135, 230, 0.7);
          box-shadow: 0 0 8px rgba(45, 135, 230, 0.3);
        }

        &.is-active {
          background: linear-gradient(135deg, #2d87e6 0%, #1976d2 100%);
          color: #ffffff;
          border-color: #2d87e6;
          box-shadow: 0 0 10px rgba(45, 135, 230, 0.5);
        }
      }

      .btn-prev, .btn-next {
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
        border: 1px solid rgba(45, 85, 135, 0.4);
        color: #e6f4ff;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          border-color: rgba(45, 135, 230, 0.7);
          box-shadow: 0 0 8px rgba(45, 135, 230, 0.3);
        }
      }
    }
  }
}
</style>
