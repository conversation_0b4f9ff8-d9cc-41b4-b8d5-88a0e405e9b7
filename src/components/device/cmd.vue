<template>
    <div class="cmd">
        <!-- 状态显示项 -->
        <div class="list state-item" v-for="item in data.state" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou"></i>
            </div>
            <div class="name" :title="item.name">{{ item.name }}</div>
            <div class="dot"></div>
            <div class="num">
                <div class="value" :style="{ color: getColor(realData[item.id], item.config) }">
                    {{ getName(realData[item.id], item.config) }}
                </div>
                <i :class="item.icon || 'iconfont iconzhuangtai'"></i>
            </div>
        </div>

        <!-- 开关控制项 -->
        <div class="list switch-item" v-for="item in data.num_out" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou"></i>
            </div>
            <div class="name" :title="item.standardName">{{ item.standardName }}</div>
            <div class="dot"></div>
            <div class="num">
                <el-switch
                    @change="changeSwitch($event, item.variable)"
                    :active-value="1"
                    v-model="realData[item.id]"
                    :inactive-value="0"
                    size="default">
                </el-switch>
            </div>
        </div>

        <!-- 数值显示项 -->
        <div class="list value-item" v-for="item in data.string_input" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou"></i>
            </div>
            <div class="name" :title="item.standardName">{{ item.standardName }}</div>
            <div class="dot"></div>
            <div class="num">
                <div class="value">{{ realData[item.id] }}{{ getUnitName(item.unit) }}</div>
                <i :class="item.icon || 'iconfont iconshuzhi'"></i>
            </div>
        </div>

        <!-- 滑块控制项 -->
        <div class="list slider-item" v-for="item in data.string_out" :key="item.id">
            <div class="icon">
                <i class="iconfont iconyoujiantou"></i>
            </div>
            <div class="name" :title="item.standardName">{{ item.standardName }}</div>
            <div class="slider">
                <div class="min-value">{{ item.min }}</div>
                <el-slider
                    @change="changeSlider($event, item)"
                    v-model="realData[item.id]"
                    :max="item.max"
                    :min="item.min"
                    :show-tooltip="true" />
                <div class="max-value">{{ item.max }}</div>
            </div>
        </div>

        <!-- 枚举选择项 -->
        <div class="list enum-item" v-for="(item, i) in data.enum" :key="i">
            <div class="icon">
                <i class="iconfont iconyoujiantou"></i>
            </div>
            <div class="name" :title="item[0].standardName">{{ item[0].standardName }}</div>
            <div class="dot"></div>
            <div class="num">
                <el-select
                    @change="changeEnum($event, d)"
                    v-model="realData[item[0].id]"
                    placeholder="请选择"
                    size="default">
                    <el-option
                        v-for="d in item"
                        :key="d.value"
                        :label="d.name"
                        :value="d.value">
                    </el-option>
                </el-select>
            </div>
        </div>
    </div>
</template>

<script setup>
import socket from "@/utils/socket";
import {
    getCookie
} from "@/utils/cookie";
import calc from '@/utils/eval';
import useSocket from "@/hooks/socket";


const props = defineProps(['deviceId']);
const { socketEmit, socketOn } = useSocket();

const data = ref({
    name: '',
    state: [],
    num_out: [],
    string_input: [],
    string_out: [],
    enum: []
});
const sockets = ref(null);
const realData = ref({});
const unit = ref([]);

const api = inject('$api');


onMounted(() => {
    socketEmit("UnSubscribeBatch",'panel')
    if (props.deviceId) {
        getDeviceList(props.deviceId)
    }
    getUnit()
});

watch(() => props.deviceId, (val) => {
    if (val) {
        getDeviceList(val)
    }
});

const getUnit = () => {
    api.getDicUtil({
        dicCode: 'unit',
        projectId: getCookie("gh_projectId"),
    }).then((res) => {
        unit.value = res.data
    })
};

const getUnitName = (val) => {
    let name = ''
    unit.value.forEach((u) => {
        if (u.id == val) {
            name = u.tagName
        }
    })
    return name
};
//设备标准订阅
const deviceStd = (d) => {
    let ws = [];
    data.value = {
        name: '',
        state: [],
        num_out: [],
        string_input: [],
        string_out: [],
        enum: []
    };

    if (d.deviceStandards) {
        let deviceData = {};
        deviceData.name = d.name;
        deviceData.id = d.id
        deviceData.state = [];
        deviceData.num_out = [];
        deviceData.string_input = [];
        deviceData.string_out = [];
        deviceData.enum = [];
        //设备指标          
        d.deviceStandards.forEach((s) => {
            let id = d.productId + "_" + d.id + '_' + s.identifier;
            let variable = d.productId + ":" + d.id + ":" + s.identifier;
            let subItem = {
                productId: d.productId,
                deviceId: d.id,
                identifier: s.identifier,
            };
            //属性  
            if (s.typeId == 1 && s.dataTypeName == 'bool') {
                deviceData.state.push({
                    name: s.name,
                    id: id,
                    config: s.config ? JSON.parse(s.config) : null,
                    showType: s.showType
                });
                realData.value = Object.assign({}, realData.value, {
                    [id]: 0,
                });
                ws.push(subItem);
            } else if (s.typeId == 2 && s.dataTypeName == "bool" && JSON.parse(s.config).length == 2) {
                let item = {};
                item.standardName = s.name;
                item.value = s.paramValue;
                item.icon = s.icon || 'iconfont iconjidian01';
                item.variable = variable;
                item.id = id;
                item.config = s.config ? JSON.parse(s.config) : null
                realData.value = Object.assign({}, realData.value, {
                    [id]: 0,
                });
                ws.push(subItem);
                deviceData.num_out.push(item);
            }
            else if (s.typeId == 2 && s.dataTypeName == "bool" && JSON.parse(s.config).length > 2) {

                if (s.config) {
                    let enums = JSON.parse(s.config);
                    let e = [];
                    enums.forEach((d) => {
                        e.push({
                            id: id,
                            variable: s.variable,
                            name: d.text,
                            value: Number(d.value),
                            type: "enum",
                            config: enums,
                            icon: s.icon ||
                                'iconfont iconjidian01',
                            standardName: s.name,
                        });
                    });

                    realData.value = Object.assign({}, realData.value, {
                        [id]: 0,
                    });
                    ws.push(subItem);
                    deviceData.enum.push(e);
                }
            }
            else if (s.typeId == 1 && (s.dataTypeName == 'string' || s.dataTypeName == 'int'
                || s.dataTypeName == 'float' || s.dataTypeName == 'double')) {
                let item = {};
                item.standardName = s.name;
                item.value = s.paramValue;
                item.icon = s.icon || 'iconfont iconjidian01';
                item.unit = s.unitId;
                item.id = id;
                item.config = s.config ? JSON.parse(s.config) : {}
                realData.value = Object.assign({}, realData.value, {
                    [id]: 0,
                });
                ws.push(subItem);
                deviceData.string_input.push(item);
            }

            else if (s.typeId == 2 && (s.dataTypeName == 'string' || s.dataTypeName == 'int'
                || s.dataTypeName == 'float' || s.dataTypeName == 'double')) {
                let item = {};
                item.standardName = s.name;
                item.variable = variable;

                item.min = s.min || 0;
                item.max = s.max || 100;

                item.icon = s.icon || 'iconfont iconjidian01';
                item.id = id;
                realData.value = Object.assign({}, realData.value, {
                    [id]: 0,
                });
                ws.push(subItem);
                deviceData.string_out.push(item);
            }
            else if (s.dataTypeName == 'enmu') {
                if (p.paramValue) {
                    let enums = p.paramValue.split(";");
                    enums.forEach((d) => {
                        deviceData.enum.push({
                            id: id,
                            variable: variable,
                            name: d.split("=")[0],
                            value: d.split("=")[1],
                            type: "enum",
                            icon: s.icon || 'iconfont iconjidian01',
                            standardName: s.name,
                        });
                    });

                    realData.value = Object.assign({}, realData.value, {
                        [id]: 0,
                    });
                    ws.push(subItem);
                }
            }

        });
        data.value = deviceData;
    }

    if (ws.length > 0) {
        nextTick(() => {
            socketEmit("Subscribe",JSON.stringify({
                batchDefinitionId: "panel",
                requestItems: null,
                dev: ws
            }))
        });
    }
};


const getDeviceList = async (id) => {
    socketEmit("UnSubscribeBatch",'panel')
    let {
        data
    } = await api.getDevicesStdById({
        deviceId: id,
        projectId: getCookie("gh_projectId")
    })
    if (data && data.length > 0) {
        deviceStd(data[0]);
    }
};

const subscribeData = (res) => {
    if (res) {
        let data = JSON.parse(res);
        if (data.batchDefinitionId == "panel") {
            data.data.forEach((d) => {
                //判断是不是数字
                if (!isNaN(Number(d.value))) {
                    // 如果是数字，转换为Number类型后保存
                    realData.value[d.id] = Number(d.value);
                } else {
                    realData.value[d.id] = d.value;
                }
            });
        }
    }
};
const getName = (value, config) => {
    let name = "";
    if (config && config.length && config.length > 0) {
        config.forEach(c => {
            if (calc(value, c.factor, c.value)) {
                name = c.text;
            }
        })
    }
    return name;
};

const getColor = (value, config) => {
    let color = "";
    if (config && config.length && config.length > 0) {
        config.forEach(c => {
            if (calc(value, c.factor, c.value)) {
                color = c.color;
            }
        })
    }
    return color;
};

const changeSwitch = (value, variable) => {
    socket.writeValue(
        sockets.value,
        variable,
        value,
        "ba",
        sockets.value.id,
        getCookie("gh_projectId"),
        getCookie("gh_id")
    );
};

const changeSlider = (val, data) => {
    socket.writeValue(
        sockets.value,
        data.variable,
        val,
        "ba",
        sockets.value.id,
        getCookie("gh_projectId"),
        getCookie("gh_id")
    );
};

const changeEnum = (val, data) => {
    socket.writeValue(
        sockets.value,
        data.variable,
        val,
        "ba",
        sockets.value.id,
        getCookie("gh_projectId"),
        getCookie("gh_id")
    );
};




onUnmounted(() => {
    socketEmit("UnSubscribeBatch",'panel')
});

socketOn('live', subscribeData);
socketOn('onVarsChangedCallback', subscribeData);

</script>

<style lang="scss" scoped>
// 参考 .left 和 .right 面板样式的设备控制面板
.cmd {
    color: #E6F4FF;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    // 与 .left 面板保持一致的毛玻璃背景
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
    border: 1px solid rgba(61, 233, 250, 0.3);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    // 整体光效背景
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(61, 233, 250, 0.05) 0%, transparent 50%);
        opacity: 1;
        pointer-events: none;
    }

    .list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        // 参考 .left 面板内部项目样式
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.5) 0%, rgba(24, 64, 104, 0.4) 100%);
        backdrop-filter: blur(6px);
        border: 1px solid rgba(61, 233, 250, 0.25);
        border-left: 3px solid #3de9fa;
        border-radius: 8px;
        height: 52px;
        width: 100%;
        padding: 0 16px;
        box-sizing: border-box;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03);

        // 悬停光效
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.08) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        &:hover {
            border-color: rgba(61, 233, 250, 0.4);
            box-shadow: 0 4px 16px rgba(61, 233, 250, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);

            &::before {
                opacity: 1;
            }
        }

        // 图标样式
        .icon {
            color: #3de9fa;
            font-size: 16px;
            margin-right: 12px;
            filter: drop-shadow(0 0 6px rgba(61, 233, 250, 0.4));
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;

            &:hover {
                transform: scale(1.1);
                filter: drop-shadow(0 0 8px rgba(61, 233, 250, 0.6));
            }
        }

        // 名称样式
        .name {
            margin-right: 12px;
            font-size: 14px;
            font-weight: 500;
            color: #E6F4FF;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;
            cursor: help;
            font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
        }

        // 连接线
        .dot {
            height: 1px;
            border-bottom: 1px dashed rgba(61, 233, 250, 0.4);
            flex: 1;
            margin: 0 12px;
            position: relative;
            z-index: 1;
        }

        // 数值显示区域
        .num {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-left: 10px;
            position: relative;
            z-index: 1;
            min-width: 100px;

            .value {
                margin-right: 8px;
                font-size: 14px;
                font-weight: 600;
                color: #E6F4FF;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
                font-family: 'Alibaba-PuHuiTi', 'PingFang SC';
            }

            i {
                color: #3de9fa;
                font-size: 16px;
                filter: drop-shadow(0 0 6px rgba(61, 233, 250, 0.4));
                transition: all 0.3s ease;

                &:hover {
                    transform: scale(1.1);
                    filter: drop-shadow(0 0 8px rgba(61, 233, 250, 0.6));
                }
            }
        }

        // 滑块容器样式
        .slider {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
            max-width: 200px;

            .min-value,
            .max-value {
                font-size: 12px;
                color: rgba(230, 244, 255, 0.8);
                min-width: 32px;
                text-align: center;
                font-weight: 500;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        }
    }

    // 不同类型项目的特殊样式
    .state-item {
        .icon {
            color: #27edbb;
        }

        border-left-color: #27edbb;
    }

    .switch-item {
        .icon {
            color: #3de9fa;
        }

        border-left-color: #3de9fa;
    }

    .value-item {
        .icon {
            color: #1196FC;
        }

        border-left-color: #1196FC;
    }

    .slider-item {
        .icon {
            color: #E3731B;
        }

        border-left-color: #E3731B;
    }

    .enum-item {
        .icon {
            color: #ff4757;
        }

        border-left-color: #ff4757;
    }
}

// Element Plus 组件深度样式 - 与全局样式保持一致
:deep(.el-slider) {
    flex: 1;

    .el-slider__runway {
        background: rgba(16, 52, 87, 0.6);
        border: 1px solid rgba(61, 233, 250, 0.3);
        border-radius: 4px;
        height: 6px;
    }

    .el-slider__bar {
        background: linear-gradient(90deg, #3de9fa 0%, #1196FC 100%);
        box-shadow: 0 0 8px rgba(61, 233, 250, 0.4);
        border-radius: 4px;
    }

    .el-slider__button {
        background: linear-gradient(135deg, #3de9fa 0%, #1196FC 100%);
        border: 2px solid #ffffff;
        box-shadow: 0 0 10px rgba(61, 233, 250, 0.5);
        width: 16px;
        height: 16px;
        transition: all 0.3s ease;

        &:hover {
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(61, 233, 250, 0.7);
        }
    }

    .el-slider__button-wrapper {
        &:hover {
            .el-slider__button {
                transform: scale(1.2);
            }
        }
    }
}

:deep(.el-switch) {
    .el-switch__core {
        background: rgba(16, 52, 87, 0.6);
        border: 1px solid rgba(61, 233, 250, 0.3);
        transition: all 0.3s ease;

        &::after {
            background: #ffffff;
            box-shadow: 0 0 6px rgba(61, 233, 250, 0.3);
            transition: all 0.3s ease;
        }
    }

    &.is-checked .el-switch__core {
        background: linear-gradient(90deg, #3de9fa 0%, #1196FC 100%);
        border-color: #3de9fa;
        box-shadow: 0 0 8px rgba(61, 233, 250, 0.4);

        &::after {
            box-shadow: 0 0 8px rgba(61, 233, 250, 0.5);
        }
    }

    &:hover {
        .el-switch__core {
            border-color: rgba(61, 233, 250, 0.5);
            box-shadow: 0 0 6px rgba(61, 233, 250, 0.2);
        }
    }
}

:deep(.el-select) {
    min-width: 120px;

    .el-select__wrapper {
        background: linear-gradient(135deg, rgba(16, 52, 87, 0.6) 0%, rgba(24, 64, 104, 0.4) 100%);
        border: 1px solid rgba(61, 233, 250, 0.3);
        border-radius: 6px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(61, 233, 250, 0.5);
            box-shadow: 0 0 8px rgba(61, 233, 250, 0.2);
        }

        &.is-focus {
            border-color: #3de9fa;
            box-shadow: 0 0 12px rgba(61, 233, 250, 0.3);
        }

        .el-select__selected-item {
            color: #E6F4FF;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .el-select__placeholder {
            color: rgba(230, 244, 255, 0.5);
        }

        .el-select__suffix {
            color: #3de9fa;
        }
    }
}

// 下拉选项样式
:deep(.el-select-dropdown) {
    background: linear-gradient(135deg, rgba(16, 52, 87, 0.95) 0%, rgba(24, 64, 104, 0.9) 100%);
    border: 1px solid rgba(61, 233, 250, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);

    .el-select-dropdown__item {
        color: #E6F4FF;
        background: transparent;
        transition: all 0.3s ease;

        &:hover {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.15) 0%, rgba(17, 150, 252, 0.1) 100%);
            color: #3de9fa;
        }

        &.is-selected {
            background: linear-gradient(135deg, rgba(61, 233, 250, 0.2) 0%, rgba(17, 150, 252, 0.15) 100%);
            color: #3de9fa;
            font-weight: 600;
        }
    }
}
</style>
