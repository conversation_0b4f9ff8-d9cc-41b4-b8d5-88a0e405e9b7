<template>
  <div class="analysis_content">
    <div class="top">
      <sub-title title="用能统计" />
      <div class="box">
        <div class="item">
          <div class="name day">本日</div>
          <span class="num">215,510</span>
        </div>
        <div class="item">
          <div class="name week">本周</div>
          <span class="num">8,899</span>
        </div>
        <div class="item">
          <div class="name month">本月</div>
          <span class="num">954,705</span>
        </div>
        <div class="item">
          <div class="name year">本年</div>
          <span class="num">18888,899</span>
        </div>
      </div>
    </div>
    <div class="chart">
      <historyChart :historyData="historyData" />
    </div>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs } from "vue";
import historyChart from "@/components/echarts/historyChart.vue";
export default {
  components: {
    historyChart,
  },
  setup() {
    const state = reactive({
      historyData: {
        data: [
          100, 1900, 200, 600, 8700, 100, 900, 200, 600, 870, 8700, 100, 900,
          200, 600, 870, 1200, 700, 300, 500,
        ],
        time: [
          "2012-8-1",
          "2012-8-2",
          "2012-8-3",
          "2012-8-4",
          "2012-8-5",
          "2012-8-6",
          "2012-8-7",
          "2012-8-8",
          "2012-8-9",
          "2012-8-10",
          "2012-8-11",
          "2012-8-12",
          "2012-8-13",
          "2012-8-14",
          "2012-8-15",
          "2012-8-16",
          "2012-8-17",
          "2012-8-18",
          "2012-8-19",
          "2012-8-20",
        ],
        name: "",
      },
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>
<style lang="scss" scoped>
.analysis_content {
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: calc(100% - 56px);
  background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
  border: 1px solid rgba(45, 85, 135, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  .top {
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
    border: 1px solid rgba(45, 85, 135, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    .box {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .item {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
        border: 1px solid rgba(45, 85, 135, 0.4);
        border-left: 3px solid #2d87e6;
        border-radius: 6px;
        padding: 24px 16px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(45, 135, 230, 0.08) 0%, transparent 50%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          border-color: rgba(45, 135, 230, 0.6);
          box-shadow: 0 2px 12px rgba(45, 135, 230, 0.2);
          transform: translateY(-2px);

          &::before {
            opacity: 1;
          }
        }

        .name {
          font-size: 14px;
          font-family: "Alibaba-PuHuiTi";
          font-weight: 500;
          margin-bottom: 16px;
          position: relative;
          z-index: 1;
        }

        .day {
          background: linear-gradient(180deg, #ffffff 0%, #ed7327 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .week {
          background: linear-gradient(180deg, #ffffff 0%, #e5950d 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .month {
          background: linear-gradient(180deg, #ffffff 0%, #27edbb 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .year {
          background: linear-gradient(180deg, #ffffff 0%, #27a6ed 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .num {
          width: 90%;
          height: 45px;
          line-height: 45px;
          background: linear-gradient(135deg, rgba(45, 135, 230, 0.15) 0%, rgba(45, 135, 230, 0.08) 100%);
          border: 1px solid rgba(45, 135, 230, 0.3);
          border-radius: 4px;
          text-align: center;
          font-size: 20px;
          font-family: "DINAlternate-Bold", "DINAlternate";
          font-weight: bold;
          color: #2d87e6;
          text-shadow: 0 0 8px rgba(45, 135, 230, 0.5);
          position: relative;
          z-index: 1;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, rgba(45, 135, 230, 0.25) 0%, rgba(45, 135, 230, 0.15) 100%);
            border-color: rgba(45, 135, 230, 0.5);
            box-shadow: 0 0 10px rgba(45, 135, 230, 0.3);
          }
        }
      }
    }
  }

  .chart {
    flex: 1;
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
    border: 1px solid rgba(45, 85, 135, 0.2);
    border-radius: 8px;
    padding: 16px;
  }
}
</style>