<template>
<div class="panel">
    <div class="title">{{ title }}</div>
    <slot />
</div>
</template>

<script>
import {
    defineComponent
} from 'vue'
export default defineComponent({
    props: {
        title: {
            type: String,
            default: () => {
                return '智慧控制'
            },
        },
    },
})
</script>

<style lang="scss" scoped>
.panel {
    background: linear-gradient(90deg, rgba(45, 85, 135, 0.3) 0%, transparent 100%);
    border-left: 3px solid #2d87e6;
    padding: 10px 12px;
    margin-bottom: 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    width: 100%;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(45, 135, 230, 0.05) 0%, transparent 50%);
        opacity: 1;
    }

    .title {
        padding-left: 18px;
        font-size: 16px;
        font-weight: 500;
        color: #e6f4ff;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        position: relative;
        z-index: 1;

        &::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: #2d87e6;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(45, 135, 230, 0.6);
        }
    }
}
</style>
