<template>
<div class="device">
    <el-row type="flex" :gutter="10" justify="space-between">
        <el-col :xs="15" :sm="15" :md="15" :lg="15" :xl="15">
            <div class="info">
                <panel title="基础信息"></panel>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备名称：</span>
                        <span class="item-value">{{ device.name }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备型号: </span>
                        <span class="item-value">{{ device.model }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备编号: </span>
                        <span class="item-value">{{ device.code }}</span>
                    </el-col>
                </el-row>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">生产厂家: </span>
                        <span class="item-value">{{ device.factory }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">设备类型: </span>
                        <span class="item-value">{{ device.deviceTypeName }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">所属空间: </span>
                        <span class="item-value">{{ device.areaName }}</span>
                    </el-col>
                </el-row>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">所属部门: </span>
                        <span class="item-value">{{ device.departmentName }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">维保周期: </span>
                        <span class="item-value">{{ device.period }}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="position">
                <panel title="安装信息"></panel>
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">安装时间: </span>
                        <span class="item-value">{{ device.installTime }}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">安装位置: </span>
                        <span class="item-value">{{ device.position }}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="position">
                <panel title="BIM信息" />
                <el-row class="item">

                    <span class="item-label">模型名称：</span>
                    <span class="item-value">{{modelName}}</span>

                </el-row>
                <el-row class="item">
                    <span class="item-label"> 构件名称：</span>
                    <span class="item-value">{{name}}</span>
                </el-row>
            </div>
            <div class="position">
                <panel title="GIS信息" />
                <el-row class="item">
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">经度：</span>
                        <span class="item-value">{{points&&points.length>0?points[0].lat:''}}</span>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
                        <span class="item-label">纬度：</span>
                        <span class="item-value">{{points&&points.length>0?points[0].lng:''}}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="position">
                <panel title='安装手册'></panel>
                <div class="book-list">
                    <div class="book-list-item" v-if="device.manualPath">
                        <div>
                            <i class="iconfont iconshuomingshu"></i>
                        </div>
                        <div class="center">
                            说明书
                            <el-link type="primary" :href="device.manualPath">下载</el-link>
                        </div>
                    </div>
                    <div class="book-list-item" v-if="device.cadPath">
                        <div>
                            <i class="iconfont iconicon-book"></i>
                        </div>
                        <div class="center">
                            图纸
                            <el-link type="primary" :href="device.cadPath">下载</el-link>
                        </div>
                    </div>
                </div>
            </div>
        </el-col>
        <el-col :xs="9" :sm="9" :md="9" :lg="9" :xl="9">
            <div class="img_box">
                <img  :src="getImageUrl(device.imgPath)" style="object-fit: contain" />
            </div>
            <div class="qrCode">
                <div ref="qrCodeDiv" id="qrCode"></div>
                <div class="title">设备二维码</div>
            </div>
        </el-col>
    </el-row>
</div>
</template>

<script>
import QRCode from 'qrcode';
import {
    getCookie
} from '@/utils/cookie' // get token from cookie
import {
    defineComponent,
    reactive,
    ref,
    toRefs,
    onMounted,
    watch,
    inject,
    computed
} from 'vue'
import Panel from './Panel.vue';
import { useAppStore } from '@/stores/app';

export default defineComponent({
    components: {
        Panel
    },
    name: 'device',
    props: ['deviceId'],
    setup(props) {
        const api = inject('$api')
        const store = useAppStore();
        const qrCodeDiv = ref(null)
        const state = reactive({
            device: {},
            points: [],
            modelName: '',
            name: ''
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        
        // 先定义 getDevice 函数
        const getDevice = () => {
            api.getDevices({
                projectId: getCookie("gh_projectId"),
                id: props.deviceId,
            }).then((res) => {
                state.device = res.data.length > 0 ? res.data[0] : null
                document.getElementById('qrCode').innerHTML = ''
                
                // 检查设备ID是否有效再生成二维码
                if (state.device && state.device.id) {
                    // 创建canvas并追加到div中
                    QRCode.toCanvas(String(state.device.id), {
                        width: 212,
                        height: 212,
                        color: {
                            dark: "#fff", //二维码颜色
                            light: "#000", //二维码背景色
                        }
                    }, function (error, canvas) {
                        if (error) {
                            console.error('生成二维码失败:', error);
                        } else {
                            // 清空容器并添加新的canvas
                            qrCodeDiv.value.innerHTML = '';
                            qrCodeDiv.value.appendChild(canvas);
                        }
                    });
                } else {
                    console.warn('设备ID无效，无法生成二维码');
                }
            }).catch((error) => {
                console.error('获取设备信息失败:', error);
            })
        }
        
        // 在函数定义后添加监听器
        watch(props, (newVal, oldVal) => {
            if (newVal) {
                getDevice()
            }
        })
        watch(projectId, (val) => {
            if (val) {
                getDevice()
            }
        })
        onMounted(() => {
            if (props.deviceId) {
                getDevice()
               // getGisBim()
            }
        })
        
        const getGisBim = () => {
            api.GisBim({
                projectId: getCookie("gh_projectId"),
                deviceId: props.deviceId,
            }).then(res => {
                const data = res.data
                state.points = eval('(' + data.points[0] + ')');

                if (data.gis.length > 0) {
                    const modelName = [];
                    const name = [];
                    data.gis.forEach(element => {
                        if (element.modelName) {
                            modelName.push(element.modelName)
                            state.modelName = modelName.join('，')
                        }
                        if (element.name) {
                            name.push(element.name)
                            state.name = name.join('，')
                        }
                    });
                }
            })
        }

        // 处理图片路径，支持Vite的静态资源导入
        const getImageUrl = (path) => {
            if (!path) return '';
            
            // 如果是完整的HTTP/HTTPS URL，直接返回
            if (path.startsWith('http://') || path.startsWith('https://')) {
                return path;
            }
            
            // 如果是以/开头的绝对路径，直接返回
            if (path.startsWith('/')) {
                return path;
            }
            
            // 如果是相对路径且指向src目录，使用动态导入
            if (path.startsWith('src/') || path.startsWith('./src/')) {
                try {
                    // 使用new URL + import.meta.url来处理相对路径
                    const cleanPath = path.replace(/^(\.\/)?src\//, './');
                    return new URL(cleanPath, import.meta.url).href;
                } catch (error) {
                    console.warn('图片路径解析失败:', path, error);
                    return '';
                }
            }
            
            // 其他情况直接返回
            return path;
        }

        return {
            ...toRefs(state),
            qrCodeDiv,
            getDevice,
            getGisBim,
            getImageUrl,
            projectId
        }
    },
})
</script>

<style lang="scss" scoped>
.device {
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
    border: 1px solid rgba(45, 85, 135, 0.3);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    .item {
        margin-bottom: 16px;
        padding: 12px 0;
        border-bottom: 1px solid rgba(45, 85, 135, 0.2);

        &:last-child {
            border-bottom: none;
        }

        .item-value {
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 500;
            color: #e6f4ff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
            word-wrap: break-word;
            max-width: 100%;
        }

        .item-label {
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: rgba(230, 244, 255, 0.7);
            margin-bottom: 4px;
        }
    }

    .position {
        margin: 20px 0;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
        border: 1px solid rgba(45, 85, 135, 0.2);
        border-radius: 6px;
        padding: 16px;

        .book-list {
            display: flex;
            padding: 10px 0;
            gap: 20px;

            i {
                font-size: 24px;
            }

            .book-list-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 12px;
                background: linear-gradient(135deg, rgba(45, 135, 230, 0.1) 0%, rgba(45, 135, 230, 0.05) 100%);
                border: 1px solid rgba(45, 135, 230, 0.2);
                border-radius: 6px;
                transition: all 0.3s ease;
                cursor: pointer;

                &:hover {
                    background: linear-gradient(135deg, rgba(45, 135, 230, 0.2) 0%, rgba(45, 135, 230, 0.1) 100%);
                    border-color: rgba(45, 135, 230, 0.4);
                    box-shadow: 0 0 10px rgba(45, 135, 230, 0.3);
                    transform: translateY(-2px);
                }

                .iconfont {
                    color: #7bb8f7;
                    filter: drop-shadow(0 0 6px rgba(123, 184, 247, 0.4));
                    margin-bottom: 8px;
                }

                .center {
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    color: #e6f4ff;
                    text-align: center;

                    :deep(.el-link) {
                        color: #2d87e6;
                        font-weight: 500;
                        margin-left: 8px;

                        &:hover {
                            color: #5ba3f5;
                        }
                    }
                }
            }
        }
    }

    .img_box {
        width: 100%;
        max-width: 350px;
        height: 350px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
        border: 1px solid rgba(45, 85, 135, 0.4);
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(45, 135, 230, 0.05) 0%, transparent 50%);
            opacity: 1;
        }

        img {
            height: 100%;
            width: 100%;
            object-fit: contain;
            position: relative;
            z-index: 1;
        }
    }

    .qrCode {
        display: flex;
        flex-direction: column;
        align-items: center;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
        border: 1px solid rgba(45, 85, 135, 0.2);
        border-radius: 8px;
        padding: 20px;

        #qrCode {
            margin-bottom: 12px;
            border-radius: 6px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
        }

        .title {
            text-align: center;
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 500;
            color: #e6f4ff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
        }
    }

    .info {
        margin-top: 0;
    }
}
</style>
