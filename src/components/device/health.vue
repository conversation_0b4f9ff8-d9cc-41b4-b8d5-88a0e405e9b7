<template>
  <div class="health_content">
    <div class="top">
      <scoreEchart />
      <healthDegree :seriesData="seriesData" :radarData="radarData"></healthDegree>
    </div>
    <div class="bottom">
      <panel title="健康数据采集" />
      <div class="info">
        <div v-for="(item, i) in list" :key="i" class="list">
          <div class="label" :style="item.sty">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import healthDegree from "@/components/echarts/healthDegreeEchart.vue";
import scoreEchart from "@/components/echarts/scoreEchart.vue";
import { inject, reactive, toRefs } from "vue";
import {
  defineComponent,
  
  onMounted,
} from "vue";

export default defineComponent({
  components: {
    healthDegree,
    scoreEchart,
  },
  props: ["deviceId"],
  setup(props) {
    const state = reactive({
      seriesData: [70, 90, 80, 85, 70],
      radarData: [
        {
          name: "使用时长",
          max: 100,
          value: 70,
        },
        {
          name: "启停次数",
          max: 100,
          value: 90,
        },
        {
          name: "在线率",
          max: 100,
          value: 80,
        },
        {
          name: "保养次数",
          max: 100,
          value: 85,
        },
        {
          name: "故障次数",
          max: 100,
          value: 70,
        },
      ],
      list: [
        {
          label: "累计运行时长",
          value: 497,
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27EDBB 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计启停次数",
          value: "97",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27A6ED 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计故障次数",
          value: "34",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27EDBB 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计保养次数",
          value: "149",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27A6ED 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
        {
          label: "累计在线时长",
          value: "100",
          sty: "background:linear-gradient(180deg, #FFFFFF 0%, #27EDBB 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent;",
        },
      ],
    });
    const api=inject('$api') as any

    const getMaintenanceTime = async () => {
      try {
        // 获取维保次数
        const result = await api.getMaintenanceTime({deviceId:props.deviceId});
        if (result && typeof result === 'object' && 'data' in result) {
          state.list[3].value = result.data;
        } else {
          console.warn('getMaintenanceTime 返回的数据格式不正确:', result);
          state.list[3].value = 0;
        }
      } catch (error) {
        console.error('获取维保次数失败:', error);
        state.list[3].value = 0;
      }
    };
    const getDeviceHealthData = async () => {
      try {
        const result = await api.getDeviceHealthData({deviceId:props.deviceId});
        if (result && typeof result === 'object' && 'data' in result) {
          const { data } = result;
          state.list[1].value = data.onCount || 0;
          state.list[0].value = data.onTime ? data.onTime / 24 : 0;
          state.list[2].value = data.onFault || 0;
        } else {
          console.warn('getDeviceHealthData 返回的数据格式不正确:', result);
        }
      } catch (error) {
        console.error('获取设备健康数据失败:', error);
      }
    };

    onMounted(() => {
      getMaintenanceTime();
      getDeviceHealthData();
    });

    return {
      ...toRefs(state),
      getMaintenanceTime,
    };
  },
});
</script>

<style lang="scss" scoped>
.health_content {
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100%;
  background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
  border: 1px solid rgba(45, 85, 135, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  .top {
    display: flex;
    height: 60%;
    gap: 20px;
    margin-bottom: 20px;

    > div {
      background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
      border: 1px solid rgba(45, 85, 135, 0.2);
      border-radius: 8px;
      padding: 16px;
    }
  }

  .bottom {
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
    border: 1px solid rgba(45, 85, 135, 0.2);
    border-radius: 8px;
    padding: 16px;

    .info {
      display: flex;
      margin: 20px 0;
      gap: 16px;

      .list {
        flex: 1;
        padding: 24px 16px;
        text-align: center;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
        border: 1px solid rgba(45, 85, 135, 0.4);
        border-left: 3px solid #2d87e6;
        border-radius: 6px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(45, 135, 230, 0.08) 0%, transparent 50%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          border-color: rgba(45, 135, 230, 0.6);
          box-shadow: 0 2px 12px rgba(45, 135, 230, 0.2);
          transform: translateY(-2px);

          &::before {
            opacity: 1;
          }
        }

        .label {
          font-size: 14px;
          font-family: "Alibaba-PuHuiTi";
          font-weight: 500;
          color: #e6f4ff;
          line-height: 20px;
          margin-bottom: 12px;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
          position: relative;
          z-index: 1;
        }

        .value {
          width: 90%;
          height: 45px;
          line-height: 45px;
          margin: 0 auto;
          font-size: 22px;
          font-family: "DINAlternate-Bold", "DINAlternate";
          font-weight: bold;
          background: linear-gradient(135deg, rgba(45, 135, 230, 0.15) 0%, rgba(45, 135, 230, 0.08) 100%);
          border: 1px solid rgba(45, 135, 230, 0.3);
          border-radius: 4px;
          color: #2d87e6;
          text-shadow: 0 0 8px rgba(45, 135, 230, 0.5);
          position: relative;
          z-index: 1;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, rgba(45, 135, 230, 0.25) 0%, rgba(45, 135, 230, 0.15) 100%);
            border-color: rgba(45, 135, 230, 0.5);
            box-shadow: 0 0 10px rgba(45, 135, 230, 0.3);
          }
        }
      }
    }
  }
}
</style>
