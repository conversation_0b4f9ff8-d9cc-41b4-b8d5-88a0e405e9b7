<template>
    <div class="history_content ">
        <div class="box-list">
            <el-scrollbar>
                <div v-for="(item, i) in data" :key="i" class="box-list-item" @click="changeBox(item, i)">
                    <div class="icon" :class="{ active: activeBox == item.standardId }"></div>
                    <div class="header">
                        <div class="box-list-title">{{ item.name }}</div>

                    </div>
                    <div class="item">
                        <div>平均值</div>
                        <div class="box-list-num">
                            {{ values[item.variable] ? values[item.variable].mean : 0 }}
                        </div>
                    </div>
                    <!-- <div class="item">
                    <div>最大值</div>
                    <div class="box-list-num">
                        {{ values[item.variable].max }}
                    </div>
                </div>
                <div class="item">
                    <div>最小值</div>
                    <div class="box-list-num">
                        {{ values[item.variable].min }}
                    </div>
                </div> -->

                    <div class="arrow">
                        <img src="@/components/device/img/arrow.png" />
                    </div>
                    <div class="bar top_bar"></div>
                    <div class="bar bottom_bar"></div>
                </div>

            </el-scrollbar>
        </div>
        <div class="chart">
            <!-- <panel :title="item?.name"> -->
            <el-form :inline="true" class="search_box form_inline" size="small">
                <el-form-item label="时间选择">
                    <el-date-picker :popper-class="search_picker" v-model="startEndDate" type="daterange"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                        @change="changeDate"></el-date-picker>
                </el-form-item>
            </el-form>
            <!-- </panel> -->
            <!-- <el-form :inline="true" class="search_sub" size="small" style="margin-right:15px">
            <el-button type="primary" size="small" @click="down">导出</el-button>
        </el-form> -->

            <historyChart :historyData="historyData" />
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import historyChart from '@/components/echarts/historyChart.vue'
import {
    reactive,
    toRefs,
    onMounted,
     watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import Panel from './Panel.vue'
export default {
    components: {
        historyChart,
        Panel
    },
    props: ['deviceId'],
    setup(props) {
        const api = inject('$api')
        const state = reactive({
            data: [],
            values: {},
            checked: false,
            chartsData: {},
            interval: null,
            chart: null,
            activeBox: '',
            historyData: {
                data: [],
                time: [],
                name: '',
            },
            realParams: {
                title: '',
                unit: '',
            },
            startEndDate: [],
            item: null,
            downData: []
        })
        watch(() => props.deviceId, (val) => {
            if (val) {
                getDeviceParams()
            }
        })
        onMounted(() => {
            state.startEndDate.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.startEndDate.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            if (props.deviceId) {
                getDeviceParams()
            }
        })

        const getDeviceParams = () => {
            api.getDeviceStandard({
                deviceId: props.deviceId,
            }).then((res) => {
                // let data = [];

                // if (res.data && res.data.length > 0) {
                //     res.data.forEach((d) => {
                //         if (d.standardParams && d.standardParams.length > 0) {
                //             for (let i = 0; i < d.standardParams.length; i++) {
                //                 if (d.standardParams[i].dataType == 'string_input') {
                //                     data.push(d)
                //                     break
                //                 }
                //             }
                //         }
                //     })
                // }

                // state.data = data
                state.data = res.data
                analyze(state.data)
                changeBox(res.data[0])
            })
        }
        const analyze = (data) => {
            if (data) {
                let key = []
                data.forEach((d) => {
                    if (d.variable) {
                        key.push(d.variable)
                        state.values = Object.assign({}, state.values, {
                            [d.variable]: {
                                mean: 0,
                                max: 0,
                                min: 0,
                            },
                        })
                    }
                })
                if (key.length > 0) {
                    api.getAnalyze({
                        keyword: key,
                        bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD 00:00:00"),
                        et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD 23:59:59"),
                    }).then((res) => {
                        res.data.forEach((d) => {
                            state.values[d.variable].max = d.max
                            state.values[d.variable].min = d.min
                            state.values[d.variable].mean = d.mean
                        })
                    })
                }
            }
        }
        const changeBox = (item) => {
            if (item) {
                state.item = item
                state.activeBox = item.standardId
                getHistory(item.variable, item.name)
            }

        }
        const getHistory = (variable, name) => {
            api.getHistoryDataNoPage({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                variable: variable,
            }).then((res) => {
                state.downData = res.data.histories;
                state.historyData.time = res.data.map((v, i) => {
                    return v.ts
                })
                state.historyData.data = res.data.map((e, j) => {
                    return e._value
                })
                state.historyData.name = name
            })
        }

        const down = () => {
            if (!state.item) {
                ElMessage.warning("无历史指标数据");
                return;
            }
            const params = {
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD 23:59:59"),
                keyword: state.item.variable,
            }
            api.getHistoryDown(params).then(res => {
                const link = document.createElement('a')
                const blob = new Blob([res], {
                    type: 'application/vnd.ms-excel'
                })
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                link.setAttribute('download', `历史数据.xlsx`)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
        }
        const changeDate = (value) => {
            if (state.item && state.item.variable) {
                getHistory(state.item.variable, state.item.name)
            }
        }

        return {
            ...toRefs(state),
            getDeviceParams,
            analyze,
            changeBox,
            getHistory,
            changeDate,
            down
        }
    },
}
</script>

<style lang="scss" scoped>
.history_content {
    display: flex;
    flex-direction: row;
    gap: 20px;
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
    border: 1px solid rgba(45, 85, 135, 0.3);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    .box-list {
        width: 300px;
        height: calc(100% - 20px);

        :deep(.el-scrollbar) {
            .el-scrollbar__bar {
                &.is-vertical {
                    .el-scrollbar__thumb {
                        background: linear-gradient(135deg, rgba(45, 135, 230, 0.7) 0%, rgba(25, 118, 210, 0.5) 100%);
                        border-radius: 3px;

                        &:hover {
                            background: linear-gradient(135deg, rgba(45, 135, 230, 0.9) 0%, rgba(25, 118, 210, 0.7) 100%);
                        }
                    }
                }
            }

            .el-scrollbar__track {
                background: rgba(5, 26, 48, 0.4);
                border-radius: 3px;
            }
        }

        .box-list-item {
            background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
            border: 1px solid rgba(45, 85, 135, 0.4);
            border-left: 3px solid #2d87e6;
            border-radius: 6px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(45, 135, 230, 0.08) 0%, transparent 50%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:hover {
                border-color: rgba(45, 135, 230, 0.6);
                box-shadow: 0 2px 12px rgba(45, 135, 230, 0.2);
                transform: translateY(-1px);

                &::before {
                    opacity: 1;
                }
            }

            .icon {
                width: 85px;
                height: 92px;
                background: url("@/components/device/img/t.png") no-repeat;
                margin-right: 20px;
                position: relative;
                z-index: 1;
                filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.3));
            }

            .item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-direction: column;
                font-size: 12px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #e6f4ff;
                padding: 6px 8px;
                position: relative;
                z-index: 1;

                .box-list-num {
                    font-size: 16px;
                    font-family: "DINAlternate-Bold";
                    font-weight: bold;
                    color: #2d87e6;
                    text-shadow: 0 0 8px rgba(45, 135, 230, 0.5);
                    margin-top: 4px;
                }
            }

            .header {
                display: flex;
                align-items: center;
                position: relative;
                z-index: 1;

                .box-list-title {
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: #e6f4ff;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
                }
            }

            .bar {
                width: 29px;
                height: 2px;
                background: linear-gradient(90deg, #2d87e6 0%, #1fa6cd 100%);
                box-shadow: 0 0 6px rgba(45, 135, 230, 0.5);
            }

            .bottom_bar {
                position: absolute;
                bottom: 0;
                right: 15px;
            }

            .top_bar {
                position: absolute;
                top: 0;
                left: 0;
            }

            .arrow {
                height: 94px;
                width: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                z-index: 1;

                img {
                    filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.4));
                    transition: transform 0.3s ease;
                }
            }

            &:hover .arrow img {
                transform: translateX(3px);
            }
        }
    }

    .chart {
        flex: 1;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
        border: 1px solid rgba(45, 85, 135, 0.2);
        border-radius: 8px;
        padding: 16px;

        .history_chart {
            height: calc(100% - 49px);
        }

        .search_box {
            text-align: right;
            padding-right: 20px;
            margin-bottom: 16px;

            :deep(.el-form-item__label) {
                color: #e6f4ff;
                font-weight: 500;
            }

            :deep(.el-date-editor) {
                .el-input__wrapper {
                    background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
                    border: 1px solid rgba(45, 85, 135, 0.5);
                    border-radius: 6px;
                    box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.3);

                    &:hover {
                        border-color: rgba(45, 135, 230, 0.7);
                        box-shadow: 0 0 8px rgba(45, 135, 230, 0.2);
                    }

                    &.is-focus {
                        border-color: #2d87e6;
                        box-shadow: 0 0 12px rgba(45, 135, 230, 0.3);
                    }
                }

                .el-input__inner {
                    color: #e6f4ff;

                    &::placeholder {
                        color: rgba(230, 244, 255, 0.5);
                    }
                }
            }
        }

        .search_sub {
            margin-left: 20px;
            padding-bottom: 10px;
        }
    }

    .active {
        background: url("@/components/device/img/t_active.png") no-repeat !important;
        filter: drop-shadow(0 0 10px rgba(45, 135, 230, 0.6)) !important;
    }
}
</style>
