<template>
    <!-- 实时工况 -->
    <div class="real_content">
        <div class="box-list">
            <el-scrollbar>
                <div v-for="(item, i) in data" :key="i">
                    <div class="box-list-item cursor" @click="changeBox(item, i)" v-if="item.typeId == 1 && (item.dataTypeName == 'string' || item.dataTypeName == 'int'
                        || item.dataTypeName == 'float' || item.dataTypeName == 'double')">
                        <div class="icon" :class="[activeBox == item.id ? 'active' : '']"></div>
                        <div class="temperature">
                            <!-- <div class="box-list-title">{{ item.name }}</div> -->
                            <div class="item">
                                <div>{{ item.name }}</div>
                                <div class="box-list-num">
                                    {{ values['n' + i] }}{{ item.unitName }}
                                </div>
                            </div>
                        </div>
                        <div class="arrow">
                            <img src="@/components/device/img/arrow.png" />
                        </div>
                        <div class="bar top_bar"></div>
                        <div class="bar bottom_bar"></div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        <div class="chart_right">
            <panel :title="realParams.title + '-实时数据'" />
            <realChart :realData="realData" :chartParams="realParams" />
        </div>
    </div>
</template>

<script setup>
import {
    getCookie
} from '@/utils/cookie' // get token from cookie

import dayjs from 'dayjs'
import realChart from '@/components/echarts/realChart.vue'



import Panel from './Panel.vue'
import { useAppStore } from '@/stores/app'
import useSocket from '@/hooks/socket'

const { socketOn,socketEmit } = useSocket();

const props = defineProps({
    deviceId: {
        type: [String, Number],
        required: true
    }
})

socketOn('live', (res) => {
    process(res)
})
socketOn('onVarsChangedCallback', (res) => {
    process(res)
})



const api = inject('$api')
const store = useAppStore()

const data = ref([])
const values = ref({})
const checked = ref(false)
const chartsData = ref({})
const interval = ref(null)
const activeBox = ref('')
const realData = ref({
    time: [],
    data: [],
})
const timer = ref(null)
const realParams = ref({
    unit: '',
    title: '',
})
const swiperOption = ref({
    slidesPerView: 8,
    spaceBetween: 0,
    freeMode: true,
    navigation: {
        nextEl: '.next',
        prevEl: '.prev',
    },
})
const sockets = ref(null)
const unit = ref([])

sockets.value = inject('socket')
onUnmounted(() => {
    if (interval.value) {
        clearInterval(interval.value)
    }
    if (timer.value) {
        clearInterval(timer.value)
    }
})
const projectId = computed(() => {
    return store.projectId || getCookie('gh_projectId')
})

watch(() => props.deviceId, (val) => {
    if (val) {
        getDeviceParams()
    }
})
onMounted(() => {
    getDeviceParams()
})

onUnmounted(() => {
    socketEmit("UnSubscribeBatch",'device')
})

const getDeviceParams = () => {
    api.getDevicesStdById({
        deviceId: props.deviceId,
        projectId: getCookie("gh_projectId"),
        page: 1,
        size: 1
    }).then((res) => {
        data.value = res.data[0].deviceStandards;
        activeBox.value = data.value[0].id
        subscribe(data.value)
    })
}
const subscribe = (dataList) => {
    dataList.forEach((d, i) => {
        values.value = Object.assign({}, values.value, {
            ['n' + i]: 0
        })
        values.value = Object.assign({}, values.value, {
            time: getnewDate(),
        })
        chartsData.value[d.name] = 'n' + i
        if (d.variable) {
            let v = d.variable.split(':')
            let item = {
                id: 'n' + i,
                iosvrKey: v[0],
                chlKey: v[1],
                ctrlKey: v[2],
                varKey: v[3],
                realTime: false,
            }
            socketEmit("Subscribe",JSON.stringify({
                batchDefinitionId: 'device',
                requestItems: [item],
                dev: null
            }))
        }
    })
    getrealData(dataList[0], 0)
}
const getrealData = (item, i) => {
    if (timer.value) {
        window.clearInterval(timer.value)
    }
    realParams.value = {
        title: item.name,
        unit: item.unitName,
    }

    if (realData.value.time.length == 10) {
        realData.value.time.shift()
        realData.value.data.shift()
    }
    let dataValue = parseInt(values.value['n' + i])
    realData.value.time.push(getnewDate())
    realData.value.data.push(dataValue)

    timer.value = setInterval(() => {
        if (realData.value.time.length == 10) {
            realData.value.time.shift()
            realData.value.data.shift()

            let dataValue = parseInt(values.value['n' + i])
            realData.value.time.push(getnewDate())
            realData.value.data.push(dataValue)
        } else {
            let dataValue = parseInt(values.value['n' + i])
            realData.value.time.push(getnewDate())
            realData.value.data.push(dataValue)
        }
    }, 5000)
}
const getnewDate = () => {
    let time = dayjs().format('HH:mm:ss')
    return time
}
const process = (res) => {
    if (res) {
        let responseData = JSON.parse(res)
        if (responseData.batchDefinitionId == 'device') {
            responseData.data.forEach((d) => {
                values.value[d.id] = d.value
                values.value.time = getnewDate()
            })
        }
    }
}
// 点击box
const changeBox = (item, i) => {
    activeBox.value = item.id
    realData.value = {
        time: [],
        data: [],
    }
    getrealData(item, i)
}
</script>

<style lang="scss" scoped>
.real_content {
    display: flex;
    flex-direction: row;
    height: calc(100% - 56px);
    gap: 20px;
    background: linear-gradient(135deg, rgba(5, 26, 48, 0.4) 0%, rgba(2, 15, 30, 0.6) 100%);
    border: 1px solid rgba(45, 85, 135, 0.3);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    .box-list {
        width: 300px;
        height: calc(100% - 20px);

        :deep(.el-scrollbar) {
            .el-scrollbar__bar {
                &.is-vertical {
                    .el-scrollbar__thumb {
                        background: linear-gradient(135deg, rgba(45, 135, 230, 0.7) 0%, rgba(25, 118, 210, 0.5) 100%);
                        border-radius: 3px;

                        &:hover {
                            background: linear-gradient(135deg, rgba(45, 135, 230, 0.9) 0%, rgba(25, 118, 210, 0.7) 100%);
                        }
                    }
                }
            }

            .el-scrollbar__track {
                background: rgba(5, 26, 48, 0.4);
                border-radius: 3px;
            }
        }

        .box-list-item {
            background: linear-gradient(135deg, rgba(5, 26, 48, 0.5) 0%, rgba(2, 15, 30, 0.7) 100%);
            border: 1px solid rgba(45, 85, 135, 0.4);
            border-left: 3px solid #2d87e6;
            border-radius: 6px;
            margin-bottom: 12px;
            display: flex;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(45, 135, 230, 0.08) 0%, transparent 50%);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:hover {
                border-color: rgba(45, 135, 230, 0.6);
                box-shadow: 0 2px 12px rgba(45, 135, 230, 0.2);
                transform: translateY(-1px);

                &::before {
                    opacity: 1;
                }
            }

            .icon {
                width: 85px;
                height: 92px;
                background: url("@/components/device/img/t.png") no-repeat;
                margin-right: 20px;
                position: relative;
                z-index: 1;
                filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.3));
            }

            .temperature {
                display: flex;
                flex-direction: column;
                flex: 1;
                position: relative;
                z-index: 1;

                .box-list-title {
                    padding: 14px 8px 0 8px;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 500;
                    color: rgba(230, 244, 255, 0.8);
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
                }

                .item {
                    height: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    color: #e6f4ff;
                    padding: 12px 8px;

                    .box-list-num {
                        font-size: 18px;
                        font-family: "DINAlternate-Bold";
                        font-weight: bold;
                        color: #2d87e6;
                        margin-left: 20px;
                        text-shadow: 0 0 8px rgba(45, 135, 230, 0.5);
                        min-width: 60px;
                        text-align: right;
                    }
                }
            }

            .bar {
                width: 29px;
                height: 2px;
                background: linear-gradient(90deg, #2d87e6 0%, #1fa6cd 100%);
                box-shadow: 0 0 6px rgba(45, 135, 230, 0.5);
            }

            .bottom_bar {
                position: absolute;
                bottom: 0;
                right: 15px;
            }

            .top_bar {
                position: absolute;
                top: 0;
                left: 0;
            }

            .arrow {
                height: 94px;
                width: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                z-index: 1;

                img {
                    filter: drop-shadow(0 0 6px rgba(45, 135, 230, 0.4));
                    transition: transform 0.3s ease;
                }
            }

            &:hover .arrow img {
                transform: translateX(3px);
            }
        }
    }

    .active {
        background: url("@/components/device/img/t_active.png") no-repeat !important;
        filter: drop-shadow(0 0 10px rgba(45, 135, 230, 0.6)) !important;
    }

    .chart_right {
        flex: 1;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
        border: 1px solid rgba(45, 85, 135, 0.2);
        border-radius: 8px;
        padding: 16px;

        .real_chart {
            height: calc(100% - 49px);
        }
    }
}
</style>