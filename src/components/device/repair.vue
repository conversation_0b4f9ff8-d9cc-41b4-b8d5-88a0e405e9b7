<template>
<div class="run">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="处理时间">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button class="searchBtn" type="text" @click="getProcessLogList">
                查询
            </el-button>
        </el-form-item>
    </el-form>
    <el-table :data="list" height="calc(100% - 100px)"  fit table-layout="auto">
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="deviceName" label="维修设备" align="center"></el-table-column>
        <el-table-column prop="createTime" label="维修时间" align="center"></el-table-column>
        <el-table-column prop="description" label="维修人员" align="center"></el-table-column>
        <el-table-column prop="createTime" label="维修内容" align="center"></el-table-column>
    </el-table>
    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import { useAppStore } from '@/stores/app'



export default {

    props: ['deviceId'],
    setup(props) {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            list: [],
            page: 1,
            size: 10,
            total: 0,
            status: '',
            date: [],
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getProcessLogList()
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) getProcessLogList()
        })
        const getProcessLogList = () => {
            api.getProcessLog({
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size,
                deviceId: props.deviceId,
                type: 1,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getProcessLogList()
        }
        return {
            ...toRefs(state),
            getProcessLogList,
            handleCurrentChange,
            projectId
        }
    }
}
</script>

<style lang="scss" scoped>
.run {

    border: 1px solid rgba(45, 85, 135, 0.3);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    .search_box {
        margin-bottom: 16px;
        padding: 16px;
        background: linear-gradient(135deg, rgba(5, 26, 48, 0.3) 0%, rgba(2, 15, 30, 0.5) 100%);
        border: 1px solid rgba(45, 85, 135, 0.2);
        border-radius: 6px;

        :deep(.el-form-item__label) {
            color: #e6f4ff;
            font-weight: 500;
        }

        :deep(.el-date-editor) {
            .el-input__wrapper {
                background: linear-gradient(135deg, rgba(5, 26, 48, 0.7) 0%, rgba(2, 15, 30, 0.9) 100%);
                border: 1px solid rgba(45, 85, 135, 0.5);
                border-radius: 6px;
                box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.3);

                &:hover {
                    border-color: rgba(45, 135, 230, 0.7);
                    box-shadow: 0 0 8px rgba(45, 135, 230, 0.2);
                }

                &.is-focus {
                    border-color: #2d87e6;
                    box-shadow: 0 0 12px rgba(45, 135, 230, 0.3);
                }
            }

            .el-input__inner {
                color: #e6f4ff;

                &::placeholder {
                    color: rgba(230, 244, 255, 0.5);
                }
            }
        }

        .searchBtn {
            background: linear-gradient(135deg, #2d87e6 0%, #1976d2 100%);
            border: 1px solid #2d87e6;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                background: linear-gradient(135deg, #5ba3f5 0%, #2196f3 100%);
                box-shadow: 0 0 10px rgba(45, 135, 230, 0.4);
                transform: translateY(-1px);
            }
        }
    }




}
</style>
