import SocketIO from 'socket.io-client'

const socket = SocketIO(window.apiUrl)

const useSocket = () => {

    const socketOn = (event: string, callback: (data: any) => void) => {
        socket.on(event, callback)
    }
    const socketEmit = (event: string, ...args: any[]) => {
        socket.emit(event, ...args)
    }
    const socketOff = (event: string) => {
        socket.off(event)
    }

    return {socketOn,socketEmit,socketOff,socket}
}

export default useSocket