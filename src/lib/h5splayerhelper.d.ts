// 声明从h5splayer.js导入的类型
declare module './h5splayer.js' {
  export class H5sPlayerWS {
    constructor(conf: any)
    connect(): void
    disconnect(): void
    // 添加其他必要的方法
  }

  export class H5sPlayerHls {
    constructor(conf: any)
    connect(): void
    disconnect(): void
    // 添加其他必要的方法
  }

  export class H5sPlayerRTC {
    constructor(conf: any)
    connect(): void
    disconnect(): void
    // 添加其他必要的方法
  }
}

// 声明h5splayerhelper.js导出的函数
export function H5siOS(): boolean

export function H5sPlayerCreate(conf: any): any

export function H5sPlayerDelete(player: any): void
