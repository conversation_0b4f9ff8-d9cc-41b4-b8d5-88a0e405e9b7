// API响应基础类型
interface ApiResponse<T> {
  data: T
  msg: string
  success: boolean,
  total: number
}

// API请求参数基础类型
interface ApiParams {
  [key: string]: any
}

// API模块类型集合
interface ApiModules {
  getDevicesStd: (params: ApiParams) => Promise<ApiResponse<DeviceStdDataResponse[]>>
  getDevicesStdById: (params: ApiParams) => Promise<ApiResponse<DeviceStdDataResponse[]>>
  getDeviceType: (params: ApiParams) => Promise<ApiResponse<DeviceType>>
  getAreaConfig: (params: ApiParams) => Promise<ApiResponse<AreaConfig>>
  getMenuList: (params: ApiParams) => Promise<ApiResponse<MenuList>>
  getDefaultProject: (params: ApiParams) => Promise<ApiResponse<DefaultProject>>
  getProjectById: (params: ApiParams) => Promise<ApiResponse<ProjectById>>
  login: (data: ApiParams) => Promise<ApiResponse<Login>>
  getCamByToken: (params: ApiParams) => Promise<ApiResponse<any>>
  getGroupList: (params: ApiParams) => Promise<ApiResponse<GroupList[]>>
  getGroupVar: (params: ApiParams) => Promise<ApiResponse<GroupVar[]>>
  getProjectArea: (params: ApiParams) => Promise<ApiResponse<ProjectArea[]>>
  getDeviceRunLog: (params: ApiParams) => Promise<ApiResponse<DeviceRunLog[]>>
  [key: string]: (params: ApiParams) => Promise<ApiResponse<any>>
}

interface DeviceStdDataResponse {
  id: number
  code: string
  areaName: string
  name: string
  productId: number
  icon?: string
  deviceStandards?: DeviceStandard[]
}

interface DeviceStandard {
  id: number
  name: string
  identifier: string
  value: string
  min: number
  max: number
  unitName: string
  variable: string
  config: string
  dataTypeId: number
  dataTypeName: string
  color: string
  icon?: string
  orderNo: number
  showList: boolean
  showType: number
  typeId: number
}

interface GroupList {
  id: number
  name: string,
  status: boolean,
  type: number,
  description: string,
  menuId: number,
  icon?: string
}

interface GroupVar {
  id: number
  varValue: string,
  variable: string,
  productId: number,
  identifier: string,
  deviceId: number,

  
}

interface ProjectArea {
  id: number
  name: string
  parentId: number
  projectId: number
  orderNo: number,
  bimFloor: string,
  fullPath: string,
  fullName: string,
  level: number,
  children?: ProjectArea[]
}

class ProjectArea {
  constructor(
    public id: number,
    public name: string,
    public parentId: number,
    public projectId: number,
    public orderNo: number,
    public bimFloor: string,
    public fullPath: string,
    public fullName: string,
    public level: number,
    public children?: ProjectArea[]
  ) {}
  
}

interface DeviceRunLog {
  id: number
  deviceName1: string
  standardName1:string
  oldValue:string
  newValue:string
}

