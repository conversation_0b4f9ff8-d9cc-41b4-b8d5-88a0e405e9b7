<template>
<div class="">
    <div class="left">
        <div class="header flex-start">
            <img src="@/assets/images/common/head.png">
            <div> 设备列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按设备名称搜索"></el-input>
        </div>
        <div class="device">

            <el-scrollbar>
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                        <span class="iconfont iconxiaofang"></span>
                        <div class="name" @click="showDetail(item)">{{item.name}}</div>
                    </div>
                    <div class="center state">
                        <div :style="{color:getColor(realData[p.id],p.config)}" v-for="(p,j) in item.state" :key="j">{{getName(realData[p.id],p.config)}}</div>
                    </div>
                    <div class="position cursor" @click="zoomToPosition(item)">
                        <img src="@/assets/images/common/position.png" />
                    </div>
                </div>
            </el-scrollbar>

        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>


    <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName"></component>
            </Transition>
    </pop>


    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='设备统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>379</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">烟感数量:</span><span class="num">303</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">温感数量:</span><span class="num">012</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">手报数量:</span><span class="num">017</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#dce417"></div><span class="text">消报数量:</span><span class="num">047</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='报警分析' />
            <div class="item-body kong">
                <Alarm :xAxisData="xAxisData" :echartData="echartData" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='事件列表' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list space-between" v-for="(item,i) in alarms" :key="i">
                        <div class="name">
                            <img v-if="i%2==0" src="@/assets/images/common/on.png" />
                            <img v-else src="@/assets/images/common/off.png" />
                            <div>{{ item.deviceName }}</div>
                        </div>

                        <div class="time">
                            {{ item.createTime }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    inject,
    watch,
    nextTick
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import calc from '@/utils/eval';
import socket from "@/utils/socket";

import pop from '@/components/pop/index.vue'
import Alarm from '@/components/echarts/weekEventEchart.vue'
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "fa11",
    components: {
        pop,
        Alarm
    },
    sockets: {
        live(res) {
            this.subscribeData(res);
        },
        onVarsChangedCallback(res) {
            this.subscribeData(res);
        },
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            sockets: null,
            list: [], //设备列表
            realData: {}, //订阅返回的实时数据
            mode: 0, //0---AR  3 vr  4---2D   1---BIM   2---GIS,
            echartData: [10, 11, 12, 5, 8, 6, 2],
            xAxisData: ['周一', '周二', '周三', '周四', '周五', '周六', '周天'],
            popName: '',
            alarms: [],
        })
        state.sockets = inject("socket");
        const emitter = inject("mitt");
        onMounted(() => {
            let mode = getCookie("mode");
            if (mode) {
                state.mode = mode;
            }

            if (state.mode == 4 || state.mode == 1) {
                getDeviceList();
            }

        });
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        //当前激活的楼层
        const areaId = computed(() => {
            return store.area;
        });

        watch(areaId, (val) => {
            //bim和2d才加载设备
            if (state.mode == 4 || state.mode == 1) {
                getDeviceList();
            }
        });

        watch(activeMenus, (val) => {
            if (state.mode == 4 || state.mode == 1) {
                if (!val.popName) { //中间记录不需要加载设备
                    getDeviceList();
                }
            }
        });
        //解析设备指标
        const deviceStd = (data) => {
            let ws = [];
            state.list = [];
            data.forEach((d, i) => {
                if (d.deviceStandards) {
                    let data = {};
                    data.name = d.name;
                    data.icon = d.icon;
                    data.id = d.id;
                    data.code = d.code;
                    data.state = [];
                    //设备指标          
                    d.deviceStandards.forEach((s, j) => {
                        //当前指标的参数集合
                        if (s.deviceParams) {
                            s.deviceParams.forEach((p, k) => {
                                //只解析当前值
                                if (p.paramKey == "Value") {
                                    //状态输入  
                                    if (p.dataType == "num_input") {
                                        data.state.push({
                                            name: s.name,
                                            id: "s_" + i + '_' + j + '_' + k,
                                            config: p.config ? JSON.parse(p.config) : null
                                        });
                                        let v = s.variable.split(":");
                                        let item = {
                                            id: "s_" + i + '_' + j + '_' + k,
                                            iosvrKey: v[0],
                                            chlKey: v[1],
                                            ctrlKey: v[2],
                                            varKey: v[3],
                                            realTime: false,
                                        };
                                        state.realData = Object.assign({}, state.realData, {
                                            ["s_" + i + '_' + j + '_' + k]: 0,
                                        });
                                        ws.push(item);
                                    }
                                }
                            });
                        }
                    });
                    state.list.push(data);
                }
            });
            if (ws.length > 0) {
                nextTick(() => {
                    socket.subscribe(state.sockets, "real", "ba", ws);
                });
            }
        };

        const getDeviceList = async () => {
            socket.unsubscribe(state.sockets, "ba", "real");
            let {
                data,
                total
            } = await api.getDevicesStd({
                areaId: areaId.value ? areaId.value.id : "",
                menuId: activeMenus.value ? activeMenus.value.id : "",
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            deviceStd(data);
        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const search = () => {
            state.page = 1;
            getDeviceList();
        }

        const getName = (value, config) => {
            let name = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        name = c.text;
                    }
                })
            }
            return name;
        }
        const getColor = (value, config) => {
            let color = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        color = c.color;
                    }
                })
            }
            return color;
        }
        const subscribeData = (res) => {
            if (res) {
                let data = JSON.parse(res);
                if (data.batchDefinitionId == "real" && data.clientId == "ba") {
                    data.data.forEach((d) => {
                        state.realData[d.id] = Number(d.value);
                    });
                }
            }
        };
        const zoomToPosition = async (item) => {
            let {
                data
            } = await api.getObjectId({
                projectId: getCookie("gh_projectId"),
                deviceId: item.id,
                menuId: activeMenus.value.id,
                // fileId: state.id,
            });
            if (data) {
                emitter.emit("zoomToComponents", {
                    objectId: data.objectId
                })
            } else {
                ElMessage.warning("该设备未关联模型构件");
                return;
            }

        };

        const getHistroyRecordPage = () => {
            api.getHistroyRecord({
                projectId: getCookie("gh_projectId"),
                page: 1,
                size: 10,
                alarmSource: 4

            }).then((res) => {
                state.alarms = res.data;
            })
        }
        const getAlarmWeek = () => {
            api.getAlarmWeek({
                projectId: getCookie("gh_projectId"),
                alarmSource: 4

            }).then((res) => {
                // console.log(res.data.map(el => el.count))
                state.echartData = res.data.map(el => el.count);
            })
        }
        getAlarmWeek()

        getHistroyRecordPage();

        return {
            ...toRefs(state),
            activeMenus,
            areaId,
            handleCurrentChange,
            search,
            getName,
            getColor,
            subscribeData,
            zoomToPosition,
        }
    }
});
</script>

<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("@/assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("@/assets/images/common/tab_active.png") no-repeat !important;
    }

}
</style>
