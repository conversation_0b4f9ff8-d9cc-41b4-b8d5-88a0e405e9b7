<template>
<div class="">
    <div class="left">
        <div class="header flex-start">
            <img src="@/assets/images/common/head.png">
            <div> 设备列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按设备名称搜索"></el-input>
        </div>
        <div class="device">

            <el-scrollbar>
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                     
                        <span class="iconfont iconmenjin"></span>
                        <div class="name" @click="showDetail(item)">{{item.name}}</div>
                    </div>
                    <div class="center state">
                        <div :style="{color:getColor(realData[p.id],p.config)}" v-for="(p,j) in item.state" :key="j">{{getName(realData[p.id],p.config)}}</div>
                    </div>
                    <div class="position cursor" @click="zoomToPosition(item)">
                        <img src="@/assets/images/common/position.png" />
                    </div>
                </div>
            </el-scrollbar>

        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>

    <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName"></component>
            </Transition>
    </pop>

    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='设备统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{on+off}}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">在线状态:</span><span class="num">{{on}}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">离线状态:</span><span class="num">{{off}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='门禁分析' />
            <div class="item-body kong">
                <Alarm :value="value" :value1="value1" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='事件列表' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list space-between" v-for="item in events" :key="item.eventId">
                        <div class="name">
                            <div>

                                <span v-if="item.inAndOutType==1" style="color:#0CCA0F" class="iconfont iconmenjin"></span>
                                <span v-else style="color:#F84141" class="iconfont iconmenjin"></span>
                            </div>
                            <div>{{ item.doorName }}</div>
                        </div>

                        <div>
                            <span v-if="item.inAndOutType==1">进</span>
                            <span v-if="item.inAndOutType==0">出</span>
                            <span v-if="item.inAndOutType==-1">未知</span>
                        </div>

                        <div class="time">
                            {{ dayjs(item.eventTime).format("YYYY-MM-DD HH:mm:ss") }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    inject,
    watch,
    nextTick,onUnmounted
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import calc from '@/utils/eval';
import socket from "@/utils/socket";
import diagram from '@/components/diagram/index.vue'
import pop from '@/components/pop/index.vue'
import dayjs from 'dayjs'
import Alarm from './echart/door.vue'
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "door1111",
    components: {
        diagram,
        // device,
        // health,
        // run,
        // work,
        // alarm,
        // repair,
        // real,
        // history,
        // cmd,
        pop,
        Alarm

    },
    sockets: {
        live(res) {
            this.subscribeData(res);
        },
        onVarsChangedCallback(res) {
            this.subscribeData(res);
        },
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            sockets: null,
            showDevice: false,
            list: [], //设备列表
            realData: {}, //订阅返回的实时数据
            mode: 0, //0---AR  3 vr  4---2D   1---BIM   2---GIS,
            tabComponent: "device",

            deviceDetail: null,
            popName: '',
            value: [],
            value1: [],
            on: 0,
            off: 0,
            dayjs,
            events:[]
        })
        state.sockets = inject("socket");
        onMounted(() => {

            getDeviceList();
            getHKDoor();
            getHKDoorEvent();

        });
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        //当前激活的楼层
        const areaId = computed(() => {
            let area = getCookie("area");
            return store.area || area ? JSON.parse(area) : "";
        });

        const emitter=inject("mitt");

        watch(areaId, (val) => {
            //bim和2d才加载设备
            if (state.mode == 4 || state.mode == 1) {
                getDeviceList();
            }
        });

        watch(activeMenus, (val) => {
            if (state.mode == 4 || state.mode == 1) {
                if (!val.popName) { //中间记录不需要加载设备
                    getDeviceList();
                }
            }
        });
        //解析设备指标
        const deviceStd = (data) => {
            let ws = [];
            state.list = [];
            data.forEach((d, i) => {
                if (d.deviceStandards) {
                    let data = {};
                    data.name = d.name;
                    data.icon = d.icon;
                    data.id = d.id;
                    data.code = d.code;
                    data.state = [];
                    //设备指标          
                    d.deviceStandards.forEach((s, j) => {
                        //当前指标的参数集合
                        if (s.deviceParams) {
                            s.deviceParams.forEach((p, k) => {
                                //只解析当前值
                                if (p.paramKey == "Value") {
                                    //状态输入  
                                    if (p.dataType == "num_input") {
                                        data.state.push({
                                            name: s.name,
                                            id: "s_" + i + '_' + j + '_' + k,
                                            config: p.config ? JSON.parse(p.config) : null
                                        });
                                        let v = s.variable.split(":");
                                        let item = {
                                            id: "s_" + i + '_' + j + '_' + k,
                                            iosvrKey: v[0],
                                            chlKey: v[1],
                                            ctrlKey: v[2],
                                            varKey: v[3],
                                            realTime: false,
                                        };
                                        state.realData = Object.assign({}, state.realData, {
                                            ["s_" + i + '_' + j + '_' + k]: 0,
                                        });
                                        ws.push(item);
                                    }
                                }
                            });
                        }
                    });
                    state.list.push(data);
                }
            });
            if (ws.length > 0) {
                nextTick(() => {
                    socket.subscribe(state.sockets, "real", "door", ws);
                });
            }
        };

        const getDeviceList = async () => {
            socket.unsubscribe(state.sockets, "door", "real");
            let {
                data,
                total
            } = await api.getDevicesStd({
                areaId: areaId.value ? areaId.value.id : "",
                menuId: activeMenus.value ? activeMenus.value.id : "",
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            deviceStd(data);
        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const search = () => {
            state.page = 1;
            getDeviceList();
        }

        const getName = (value, config) => {
            let name = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        name = c.text;
                    }
                })
            }
            return name;
        }
        const getColor = (value, config) => {
            let color = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        color = c.color;
                    }
                })
            }
            return color;
        }
        const subscribeData = (res) => {
            if (res) {
                let data = JSON.parse(res);
                if (data.batchDefinitionId == "real" && data.clientId == "door") {
                    data.data.forEach((d) => {
                        state.realData[d.id] = Number(d.value);
                    });
                }
            }
        };

        const showDetail = (item) => {
            state.showDevice = true;
            state.deviceDetail = item;
        }

        const getHKDoor = async () => {
            let {
                data
            } = await api.getHKController({
                page: 1,
                size: 1000
            });
            state.on=0;
            state.off=0;
            if (data.data && data.data.list) {
                data.data.list.forEach(d => {
                    if (d.online == 1) {
                        state.on++;

                    } else {
                        state.off++;
                    }
                })
            }

        }

        const getHKDoorEvent = async () => {
            let {
                data
            } = await api.getHKDoorEvent({
                page: 1,
                size: 1000,
                bt: dayjs().startOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00',
                et: dayjs().endOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00'
            });
            state.events = data.data.list.slice(0, 10);
            let value = new Array(24).fill(0)
            let value1 = new Array(24).fill(0)
            data.data.list.forEach(d => {
                if (d.inAndOutType == 1) {
                    let num = value[dayjs(d.eventTime).hour()]
                    value[dayjs(d.eventTime).hour()] = num + 1
                } else if (d.inAndOutType == 0) {
                    let num = value1[dayjs(d.eventTime).hour()]
                    value1[dayjs(d.eventTime).hour()] = num + 1
                }

            })
            state.value = value;
            state.value1 = value1;

        }

        const zoomToPosition = async (item) => {
            let {
                data
            } = await api.getObjectId({
                projectId: getCookie("gh_projectId"),
                deviceId: item.id,
                menuId: activeMenus.value.id,
                // fileId: state.id,
            });
            if (data) {
                emitter.emit("zoomToComponents", {
                    objectId: data.objectId
                })
            } else {
                ElMessage.warning("该设备未关联模型构件");
                return;
            }

        };
        onUnmounted(()=>{
            socket.unsubscribe(state.sockets, "door", "real");
        });
        return {
            ...toRefs(state),
            activeMenus,
            areaId,
            handleCurrentChange,
            search,
            getName,
            getColor,
            subscribeData,
            showDetail,
            zoomToPosition,
        }
    }
});
</script>

<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("@/assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("@/assets/images/common/tab_active.png") no-repeat !important;
    }

}
</style>
