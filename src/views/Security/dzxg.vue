<template>
<div class="">
    <div class="left">
        <div class="header flex-start">
            <img src="@/assets/images/common/head.png">
            <div> 巡检点列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按巡检点名称搜索"></el-input>
        </div>
        <div class="device">
            <el-scrollbar v-if="list.length>0">
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                        <div>
                            <span class="iconfont icondingwei2"></span>
                        </div>
                        <div class="name">{{item.name}}</div>
                    </div>
                    <div class="center state">
                        <!-- <div v-for="(p,j) in item.state" :key="j">11</div> -->
                    </div>
                    <div class="position cursor">
                        <img src="@/assets/images/common/position.png" />
                    </div>
                </div>
            </el-scrollbar>
            <noData v-else/>
        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>

    <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName"></component>
            </Transition>
    </pop>

    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='运行统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>3741</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">巡检点数:</span><span class="num">20</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">巡检计划:</span><span class="num">10</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">巡检线路:</span><span class="num">12</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='巡检统计' />
            <div class="item-body kong">
                <Alarm :xAxisData="xAxisData" :echartData="echartData" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='实时巡检' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list " v-for="i in 10" :key="i">
                        <div class="name">
                            <div>地点1</div>
                        </div>
                        <div>
                            <div>张三</div>
                        </div>
                        <div class="time">
                            2023-02-27 10:00:00
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
    watch,
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop/index.vue'
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "dzxg",
    components: {
        Alarm,
        pop
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [],
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],

        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        onMounted(() => {
            getDeviceList();
        });
        const getDeviceList = async () => {
            let {
                data,
                total
            } = await api.getPatrolPoint({
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            state.list = data;

        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const search = () => {
            state.page = 1;
            getDeviceList();
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>

</style>
