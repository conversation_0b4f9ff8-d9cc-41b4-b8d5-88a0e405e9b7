<template>
<div class="h100">
    <el-tabs v-model="active" @tab-click="handleClick" class="h100">
        <el-tab-pane label="待派发" name="first" class="h100">
            <order-table :status="status" type="1"></order-table>
        </el-tab-pane>
        <el-tab-pane label="待接单" name="second" class="h100">
            <order-table :status="status" type="1"></order-table>
        </el-tab-pane>
        <el-tab-pane label="处理中" name="third" class="h100">
            <order-table :status="status" type="1"></order-table>
        </el-tab-pane>
        <el-tab-pane label="待审核" name="fourth" class="h100">
            <order-table :status="status" type="1"></order-table>
        </el-tab-pane>
        <el-tab-pane label="已完成" name="five" class="h100">
            <order-table :status="status" type="1"></order-table>
        </el-tab-pane>
    </el-tabs>
</div>
</template>

<script>
import orderTable from '@/components/order/orderTable.vue'
import {
    reactive,
    toRefs
} from 'vue'

export default {
    name: 'repairorder',
    components: {
        orderTable
    },
    setup() {
        const state = reactive({
            active: 'first',
            list: [],
            status: [-1],
        })

        const handleClick = (tab, event) => {
            if (tab.paneName == 'first') {
                state.status = [-1]
            } else if (tab.paneName == 'second') {
                state.status = [1]
            } else if (tab.paneName == 'third') {
                state.status = [2, 8, 5, 9]
            } else if (tab.paneName == 'fourth') {
                state.status = [3, 4]
            } else if (tab.paneName == 'five') {
                state.status = [6]
            }
        }
        return {
            ...toRefs(state),
            handleClick
        }
    }
}
</script>

<style scoped>
</style>
