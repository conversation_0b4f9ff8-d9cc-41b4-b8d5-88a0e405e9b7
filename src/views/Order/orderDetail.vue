<template>
<div class="wrapper">
    <panel title="工单详情"><i class="el-icon-close closeBtn"></i></panel>
    <div class="content card-body">
        <el-timeline class="time">
            <el-timeline-item v-for="(item, index) in points" :key="index" :timestamp="item.start_TIME_" placement="top" v-show="item.act_NAME_">
                <div class="point-item">
                    <h3>{{ item.act_NAME_ }}</h3>
                    <h4>处理人:{{ item.name ? item.name : (index==0?item.createName:'') }}</h4>
                </div>
            </el-timeline-item>
        </el-timeline>
    </div>
    <div class="btn search_box">
        <div class="searchBtn"  v-for="(item, i) in cmd" :key="i" type="primary" @click="process(item.value)">{{ item.name }}</div>
    </div>
    <el-dialog align-center :append-to-body="true" v-model="Visible" draggable title="派单" custom-class="addDiagram border0" width="940px">
        <el-form ref="form1" :model="sendForm" :rules="rule" class="form">
            <el-row type="flex" :gutter="30">
                <el-col :span="12">
                    <el-form-item label="接收人:" prop="userId">
                        <el-select clearable multiple filterable v-model="sendForm.userId">
                            <el-option v-for="item in users" :key="item.userId" :value="item.userId" :label="item.name">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="工单级别：" prop="level">
                        <el-select v-model="sendForm.level" placeholder="请选择工单级别">
                            <el-option label="一般" :value="1"></el-option>
                            <el-option label="紧急" :value="2"></el-option>
                            <el-option label="严重" :value="3"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划完成时间：">
                        <el-date-picker v-model="sendForm.endTime" type="date" placeholder="选择日期" popper-class="date-picker">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" size="small" class="searchBtn" @click="selectUser('form1')">派 单</div>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    ElMessage
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs,
    watch,
    computed,
    
    onMounted
} from 'vue'

import {
    useRouter
} from 'vue-router'
export default {
    name: "orderdetail",
    props: ['order'],
    setup(props, {
        emit
    }) {
        const api = inject('$api')
        const router = useRouter()
        const state = reactive({
            order: {},
            activeNames: '',
            points: [],
            cmd: [],
            Visible: false,
            users: [],
            userId: '',
            order: null,
            send: {
                cmd: -2,
                taskId: '',
                description: '',
                instanceId: '',
                operator: getCookie('gh_id'),
                projectId: getCookie('gh_projectId'),
                staffType: '',
                status: '',
                orderId: '',
                level: '',
                endTime: '',
                userId: getCookie('gh_id'),
            },
            path: '',
            sendForm: {},
            rule: {
                userId: [{
                    required: true,
                    message: '接收人不能空',
                    trigger: 'change'
                }],
                level: [{
                    required: true,
                    message: '级别不能空',
                    trigger: 'change'
                }]
            }
        })
        onMounted(() => {
            state.order = props.order
            getOrderDetail();
        })
        watch(() => props.order, (val) => {
            state.order = props.order
            getOrderDetail();
        })
        const getOrderDetail = () => {
            state.cmd = [];
            if (state.order.instanceId) {
                getOrderProcess(state.order.instanceId)
            }
            if (state.order.cmd) {
                state.order.cmd = state.order.cmd.replace('.', ',')
            }
            if (state.order.cmd && state.order.cmd.includes(',')) {
                let split = state.order.cmd.split(',')
                split.forEach((val) => {
                    state.cmd.push({
                        name: val.split('=')[0],
                        value: val.split('=')[1],
                    })
                })
            } else if (state.order.cmd && state.order.cmd.includes('.')) {
                let split = state.order.cmd.split('.')
                split.forEach((val) => {
                    state.cmd.push({
                        name: val.split('=')[0],
                        value: val.split('=')[1],
                    })
                })
            } else if (state.order.cmd && state.order.cmd.includes('=')) {
                let split = state.order.cmd.split('=')
                state.cmd.push({
                    name: split[0],
                    value: split[1],
                })
            }
            getProjectStaffList(state.order.staffType || '');
        }
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })

        const getOrderProcess = (id) => {
            api.getPatrolProcess({
                instanceId: id,
            }).then((res) => {
                state.points = res.data
            })
        }
        const process = (cmd) => {
            if (cmd == 1) {
                //派单
                getProjectStaffList({
                    staffType: state.order.staffType,
                    projectId: getCookie("gh_projectId"),
                })
                state.Visible = true
            } else {
                let data = {
                    cmd: cmd,
                    taskId: state.order.taskId,
                    description: '',
                    instanceId: state.order.instanceId,
                    operator: getCookie('gh_id'),
                    projectId: getCookie("gh_projectId"),
                    staffType: state.order.staffType,
                    status: state.order.status,
                    orderId: state.order.id,
                    userId: getCookie('gh_id'),
                }
                getProcessTask(data)
            }
        }
        const getProjectStaffList = (type) => {
            api.getProjectStaff({
                // staffType: type,
                projectId:getCookie('gh_projectId')
            }).then((res) => {
                state.users = res.data
            })
        }
        const getProcessTask = (data) => {
            api.processTask(data).then((res) => {
                if (res.success) {
                    ElMessage({
                        type: 'success',
                        message: '处理成功',
                    })
                    state.Visible = false;
                    emit('getOrderPage')
                }
            })
        }
        const selectUser = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    state.send.userId = state.sendForm.userId.toString()
                    state.send.level = state.sendForm.level
                    state.send.endTime = dayjs(state.sendForm.endTime).format('YYYY-MM-DD HH:mm:ss')
                    state.send.cmd = 1
                    state.send.taskId = state.order.taskId
                    state.send.instanceId = state.order.instanceId
                    state.send.staffType = state.order.staffType
                    state.send.status = state.order.status
                    state.send.orderId = state.order.id
                    state.Visible = false
                    getProcessTask(state.send)
                } else {
                    return false
                }
            })

        }
        return {
            ...toRefs(state),
            process,
            getOrderProcess,
            selectUser,
            getProcessTask,
            getProjectStaffList,
            projectId
        }
    }
}
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    flex-direction: column;
    height: 600px;

    .closeBtn {
        position: absolute;
        right: 20px;
        top: 10px;
    }

    .content {
        display: flex;
        height: 100%;
        justify-content: center;
        overflow-y: auto;
        margin-top: 20px;
    }
}

.point-item {
    color: rgb(171, 186, 204);
}

.cmdBtn {
    background: rgba(61, 233, 250, 0.5);
    border: 1px solid #3de9fa;
}

.box-title {
    position: relative;
}

.btn {
    text-align: center;
    display: flex;
    justify-content: center;
}

.searchBtn{
    margin:0 5px;
}
</style>
