<template>
    <div class="">
        <div class="left">
            <div class="header ">
                <div class="center">
                    <img src="@/assets/images/common/head.png">
                    <div> 保修中心</div>
                </div>

                <div class="icon cursor" @click="showRepair">
                    <span class="iconfont iconbianji"></span>
                </div>
            </div>
            <div class="device">
                <el-scrollbar v-if="list.length > 0">
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <div class="name"> {{ item.name }}
                                </div>
                                <div class="time">
                                    <span class="iconfont iconlishi"></span>
                                    {{ dayjs(item.createTime).format("MM-DD HH:mm") }}
                                </div>
                            </div>
                        </div>
                        <div class="center state center">
                            <div style="color:green" class="center">{{ state[item.status] }}</div>
                        </div>
                        <div class="position cursor">
                            <img src="@/assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else />

            </div>

            <div class="header flex-start">
                <div class="center">
                    <img src="@/assets/images/common/head.png">
                    <div> 工单中心</div>
                </div>

            </div>
            <div class="device">
                <el-scrollbar v-if="list1.length > 0">
                    <div class="list space-between" v-for="item in list1" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <div class="name"> {{ item.name }}
                                    <!-- <el-tag class="state" size="small" type="warning">{{getStatus(item.status)}}</el-tag> -->
                                </div>
                                <div class="time">
                                    <span class="iconfont iconlishi"></span>
                                    {{ dayjs(item.createTime).format("MM-DD HH:mm") }}
                                </div>
                            </div>
                        </div>
                        <div class="center state">
                            <div style="color:green" class="center">{{ getStatus(item.status) }}</div>
                        </div>
                        <div class="position cursor">
                            <img src="@/assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>

        </div>

        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

        <div class="right">
            <div class="item" style="flex:1">
                <sub-title title='策略运行' />
                <div class="item-body run">
                    <el-scrollbar class="center">
                        <div class="list" v-for="item in strategies" :key="item.id">
                            <div>
                                <div class="img">
                                    <span class="iconfont iconyonghurenxiang"></span>
                                </div>
                                <div class="h_center">
                                    <div class="name">{{ item.name }}</div>
                                    <div v-if="!item.repeatTag" class="time">{{ item.startTime }}-{{ item.endTime }}</div>
                                    <div v-if="item.repeatTag" class="time">{{ item.startTime }}-{{ item.startTime1 }}</div>
                                    <div v-if="item.repeatTag" class="time">{{ item.endTime }}-{{ item.endTime1 }}</div>
                                </div>
                            </div>
                            <div class="center">
                                <el-switch v-model="item.status" @change="changeEnable($event, item)" />
                            </div>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
            <div class="item" style="flex:1">
                <sub-title title='手动群控' />
                <div class="item-body kong">
                    <el-scrollbar>
                        <div class="list space-between" v-for="item in manual" :key="item.id">
                            <div class="name">
                                <img src="@/assets/images/common/d3.png" />
                                <div>{{ item.name }}</div>
                            </div>
                            <div class="dot"></div>
                            <div class="btn space-between">
                                <div class="center cursor" @click="writeValue(item.startGroup, item.status)">
                                    <div class="center">开</div>
                                </div>

                                <div class="center cursor" @click="writeValue(item.endGroup, item.status)">
                                    <div class="center">关</div>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>

                </div>
            </div>
        </div>
        <repair-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getFaultPage="getFaultPage" />
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    onMounted,
    inject
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop/index.vue'
import subEchart from "./subEchart.vue";
import {
    ElMessageBox
} from 'element-plus';
import dayjs from 'dayjs'
import repairDialog from '../Repair/components/repairDialog.vue'
import socket from "@/utils/socket";
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "run",
    components: {
        Alarm,
        pop,
        subEchart,
        repairDialog
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            list: [], //保修单
            list1: [], //工单
            value1: true,
            dayjs: dayjs,
            state: ['', '待处理', '处理中', '已完成'],
            dialogData: {
                visible: false,
                title: '新增',
                repair: {
                    deviceId: null,
                    description: '',
                    name: '',
                },
                rule: {
                    name: [{
                        required: true,
                        message: '报修名称不能空',
                        trigger: 'blur',
                    },],

                    description: [{
                        required: true,
                        message: '描述不能空',
                        trigger: 'change',
                    },]
                }
            },
            orderState: [{
                value: -1,
                name: '待派单',
            },
            {
                value: 1,
                name: '待接单',
            },
            {
                value: 2,
                name: '挂起',
            },
            {
                value: 3,
                name: '正常关闭',
            },
            {
                value: 4,
                name: '异常关闭',
            },
            {
                value: 5,
                name: '退回',
            },
            {
                value: 6,
                name: '完成',
            },
            {
                value: 8,
                name: '恢复工单',
            },
            {
                value: 9,
                name: '处理中',
            },
            ],
            sockets: null,
            manual: null,
            strategies: null,
        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        onMounted(() => {
            getOrderPage();
            getFaultPage();
        });
        state.sockets = inject("socket");
        const getStatus = (val) => {
            let name = ''
            state.orderState.forEach((t) => {
                if (t.value == val) {
                    name = t.name
                }
            })
            return name
        }
        const getOrderPage = () => {
            api.getOrder({
                bt: dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                et: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.list1 = res.data
            })
        }

        const getFaultPage = () => {
            api.getFault({
                projectId: getCookie('gh_projectId'),
                bt: dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                et: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
            })
        }

        const showRepair = () => {
            state.dialogData.visible = true;
        }

        const getRunConfigPage = (type) => {
            api.getRunConfig({
                projectId: getCookie("gh_projectId"),
                tag: 1,
                type,
            }).then((res) => {
                if (type == "strategy") {
                    state.strategies = res.data.strategies;
                    // getRunModelPage(2);
                }
                if (type == "manual") {
                    state.manual = res.data.manuals;
                    // getRunModelPage(3);
                }
            });
        };
        const changeEnable = async (val, item) => {
            const {
                data
            } = await api.updateStrategyEnable({
                id: item.id,
                status: val
            })
            if (data) {
                getRunConfigPage("strategy")

            }
        };
        getRunConfigPage("strategy")
        getRunConfigPage("manual")
        const writeValue = (group, status) => {
            ElMessageBox.confirm('是否确认改操作？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                if (group && group.length > 0) {
                    group.forEach((g, i) => {
                        g.groupVars.forEach((v, j) => {
                            socket.writeValue(
                                state.sockets,
                                v.variable,
                                v.varValue,
                                "manual",
                                state.sockets.id,
                                getCookie("gh_projectId"),
                                getCookie("gh_id")
                            );
                  
                        });
                    });
                    ElMessage.success('操作成功')
                }
            })


        };
        return {
            ...toRefs(state),
            activeMenus,
            getStatus,
            getFaultPage,
            showRepair,
            changeEnable,
            writeValue
        }
    }
});
</script>

<style lang="scss" scoped>
.left {

    .header {
        font-size: 16px;
        font-family: "DOUYU";
        font-weight: 400;
        color: #E6F4FF;
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
    }

    .icon {
        color: #DEF0FF;
    }

    .device {
        height: calc(50% - 130px/2);

        .list {
            background: rgba(16, 52, 87, 0.25);
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #FFFFFF;
            margin-bottom: 8px;
            border-left: 1px solid #4274A3;
            height: 52px;

            .name,
            .time {
                margin-left: 10px;
            }

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #FFFFFF;

            }

            .time {

                display: flex;
                align-items: center;

                font-size: 16px;
                font-family: "BEBAS";
                font-weight: 400;
                color: #C0D0E0;

                span {
                    margin-right: 5px;
                }

                .iconfont {
                    margin: 0;
                }

            }

            .icon {
                color: #74EFFF;
                font-size: 20px;
                padding: 0 15px;
            }

            .state {
                div {
                    margin-right: 48px;
                    border-radius: 5px;
                    background: rgba(28, 184, 57, 0.1);
                    border: 1px solid #1CB839;
                    padding: 3px;
                    font-size: 10px;
                }

            }
        }

    }
}

.right {

    .run {
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
        color: #E6F0F6;
        flex-direction: column;
        height: calc(100% - 60px);
        justify-content: center;

        .list {
            display: flex;
            justify-content: space-between;
            align-content: center;
            width: 361px;
            height: 80px;
            background: rgba($color: #103457, $alpha: 0.25);
            margin-bottom: 15px;
            padding: 0 20px 0 10px;

            .img {
                background: url("@/assets/images/common/mode.jpg");
                width: 68px;
                height: 55px;
                margin-right: 10px;
                display: flex;
                justify-content: center;

                span {
                    font-size: 28px;

                    background: -webkit-gradient(linear, 0 0, 0 100%, from(#e5f3fe), to(#95ccfd));
                    -webkit-background-clip: text; // 
                    -webkit-text-fill-color: transparent; // 将文字透明，镂空

                }
            }

            .name {

                font-size: 16px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #C7D6E0;

            }

            .time {

                font-size: 20px;
                font-family: "BEBAS";
                font-weight: 400;
                color: #FFFFFF;

            }

            &>div:first-child {
                display: flex;
                align-items: center;
            }
        }
    }

}
</style>
