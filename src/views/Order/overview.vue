<template>

<div class="layout_wrapper overview_container">
    <div class="overview_top">
        <div class="day_order">
            <sub-title2 title="今日工单统计" />
            <div class="order">
                <div v-for="(item, i) in orderList" :key="i" class="list" :style="{ color: item.color }">
                    <div class="icon" :style="{ background: item.color }"></div>
                    {{ item.label }}
                    <span class="value">{{ item.value }}</span>
                </div>
            </div>
            <div class="today_echart">
                <div class="item">
                    <topEcharts :yAxisData="yAxisOrder" :echartData="orderData"></topEcharts>
                </div>
                <div class="item">
                    <subEchart :echartData="echartSub" :colors="colors"></subEchart>
                </div>
            </div>
        </div>
        <div class="year_order">
            <sub-title2 title="本年工单趋势" />
            <orderEchart :xAxisData="xAxisData" :echartData="echartData" />
        </div>
        <div class="month_yxgs">
            <sub-title2 title="本月有效工时" />
            <div class="work_hour">
                <div class="item">
                    <workEchart title="人均接单数" :xAxisData="xAxisWork" :echartData="seriesData" />
                </div>
                <div class="item">
                    <workEchart title="人均有效工时(小时)" :xAxisData="xAxisHour" :echartData="seriesDataHour" />
                </div>
            </div>
        </div>
    </div>
    <div class="overview_bottom">
        <sub-title2 title="未处理工单" />
        <div class="table">
            <el-table :data="tableData" :height="tableHeight" table-layout="auto">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column label="时间" prop="createTime" />
                <el-table-column label="工单编号" prop="code" />
                <el-table-column label="地点" prop="address" />
                <el-table-column label="单号类型" prop="type">
                    <template #default="scope">
                        <span>
                            {{ types[scope.row.type] }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="内容" prop="description" />
            </el-table>
        </div>
    </div>
</div>
</template>

<script lang="ts">
import orderEchart from "@/components/echarts/orderEchart.vue";
import topEcharts from "@/components/echarts/topEchart.vue";
import subEchart from "@/components/echarts/subEchart.vue";
import workEchart from "@/components/echarts/workEchart.vue";
import {
    reactive,
    toRefs
} from "vue";
import {
    computed,
    
    onMounted,
    watch,
} from "vue";
import {
    getCookie
} from "@/utils/cookie";
import { useAppStore } from "@/stores/app";

export default {
    name: 'orderoverview',
    components: {
        orderEchart,
        topEcharts,
        subEchart,
        workEchart,
    },
    setup() {
        const store = useAppStore();
        const state = reactive({
            tableHeight: window.innerHeight * 0.4,
            orderList: [{
                    label: "今日工单数",
                    value: 0,
                    color: "#13D4D9",
                },
                {
                    label: "昨日工单数",
                    value: 0,
                    color: "#4253E5",
                },
            ],
            tableData: [],
            xAxisData: ["1", "2", "3", "4", "5", "6", "7", "8"],
            echartData: [83, 60, 30, 20, 89, 15, 10, 25],
            yAxisOrder: [
                "土建维修",
                "公共设施",
                "强电",
                "智能化弱电",
                "空调取暖",
                "给排水",
                "其他",
            ],
            orderData: [24, 39, 29, 39, 84, 90, 50],
            colors: ["#FFEB6D", "#427EDF", "#C7DFFF", "#81D9FF"],
            echartSub: [{
                    name: "照明",
                    value: "4000",
                },
                {
                    name: "空调插座",
                    value: "3564",
                },
                {
                    name: "特殊用电",
                    value: "3564",
                },
                {
                    name: "动力",
                    value: "2548",
                },
            ],
            xAxisWork: ["第一周", "第二周", "第三周", "第四周"],
            xAxisHour: ["第一周", "第二周", "第三周", "第四周"],
            seriesData: [254, 3254, 1654, 2454, 4757, 2011, 1211],
            seriesDataHour: [254, 3254, 1654, 2454, 4757, 2011, 1211],
            types: ["", "报修工单", "巡检报修", "维保工单", "人工工单"],
        });
        const api = inject('$api')
        const projectId = computed(() => {
            return store.projectId || getCookie("gh_projectId");
        });
        watch(projectId, (val) => {
            if (val) {
                getOrderToday();
                getOrderTrend();
                getOrderWork();
                getOrderHour();
                getOrderPend();
            }
        });
        const getOrderToday = async () => {
            let {
                data
            } = await api.getOrderToday({
                projectId: getCookie("gh_projectId"),
            });
            state.orderList[0].value = data.todayCount;
            state.orderList[1].value = data.lastCount;
            if (data.data) {
                state.yAxisOrder = [];
                state.orderData = [];
                state.echartSub = [];
                for (let key in data.data) {
                    state.yAxisOrder.push(key);
                    state.orderData.push(data.data[key]);
                    state.echartSub.push({
                        name: key,
                        value: data.data[key],
                    });
                }
            }
        };
        const getOrderTrend = async () => {
            let {
                data
            } = await api.getOrderTrend({
                projectId: getCookie("gh_projectId"),
            });

            state.xAxisData = [];
            state.echartData = [];
            if (data) {
                for (let key in data) {
                    state.xAxisData.push(key);
                    state.echartData.push(data[key]);
                }
            }
        };
        const getOrderWork = async () => {
            let {
                data
            } = await api.getOrderWork({
                projectId: getCookie("gh_projectId"),
            });

            state.xAxisWork = [];
            state.seriesData = [];
            if (data) {
                for (let key in data) {
                    state.xAxisWork.push("第" + key + "周");
                    state.seriesData.push(data[key]);
                }
            }
        };
        const getOrderHour = async () => {
            let {
                data
            } = await api.getOrderHour({
                projectId: getCookie("gh_projectId"),
            });

            state.xAxisHour = [];
            state.seriesDataHour = [];
            if (data) {
                for (let key in data) {
                    state.xAxisHour.push("第" + key + "周");
                    state.seriesDataHour.push(data[key]);
                }
            }
        };
        const getOrderPend = async () => {
            let {
                data
            } = await api.getOrderPend({
                projectId: getCookie("gh_projectId"),
            });
            state.tableData = data;
        };
        onMounted(() => {
            getOrderToday();
            getOrderTrend();
            getOrderWork();
            getOrderHour();
            getOrderPend();
        });
        return {
            ...toRefs(state),
            projectId,
        };
    },
};
</script>

<style lang="scss" scoped>
.overview_container {
    display: flex;
    flex-direction: column;
    padding: 0 15px;

    .overview_top {
        flex: 1;
        display: flex;

        .year_order,
        .day_order {
            margin-right: 25px;
        }

        .year_order,
        .month_yxgs,
        .day_order {
            flex: 1;
            display: flex;
            flex-direction: column;

            .work_hour {
                display: flex;
                height: 100%;

                .item {
                    flex: 1;
                    height: 100%;
                }
            }

            .today_echart {
                display: flex;
                height: calc(100% - 137px);
                padding: 20px 0;

                .item {
                    flex: 1;
                    height: 100%;
                }
            }

            .order {
                display: flex;
                height: 48px;
                box-shadow: 0px 1px 0px 0px rgba(240, 240, 240, 0.2);

                .list {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;

                    .icon {
                        width: 8px;
                        height: 8px;
                        margin-right: 6px;
                    }

                    .value {
                        font-size: 25px;
                        margin-left: 10px;
                        font-family: "DINAlternate-Bold", "DINAlternate";
                        font-weight: bold;
                        color: #ffffff;
                    }
                }
            }
        }
    }

    .overview_bottom {
        height: 60%;
    }
}
</style>
