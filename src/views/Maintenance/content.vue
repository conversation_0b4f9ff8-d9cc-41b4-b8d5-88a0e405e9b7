<template>
<div class="wrapper">
    <div class="left-content">
        <panel title="设备类型"></panel>
        <el-scrollbar class="tree-list tree">
            <el-tree ref="tree" :data="data" node-key="id" check-strictly :props="props" show-checkbox @check="handleCheckChange" />
        </el-scrollbar>
    </div>
    <div class="right_tab">
        <div class="btn-group search_box">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="add">新增</div>
            <div type="primary" icon="Minus" size="mini" class="delBtn" @click="delMain">删除</div>
        </div>
        <div class="table">
            <el-table :data="list" :height="tableHeight" fit @select="select" @select-all="select" table-layout="auto">
                <template #empty>
                    <noData />
                </template>
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <el-table-column prop="name" label="名称" align="center">
                </el-table-column>
                <el-table-column prop="content" label="内容" align="center">
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button type="text" class="editBtn" @click="edit(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
    <el-dialog align-center :append-to-body="true" draggable v-model="dialogContentVisible" title="维保内容管理" custom-class="addDiagram border0" width="700px">
        <el-form ref="form" :model="contentForm" :rules="rule">
            <el-row type="flex" :gutter="30">
                <el-col :span="12">
                    <el-form-item label="名称" prop="name">
                        <el-input placeholder="请输入名称" size="small" v-model="contentForm.name"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="内容" prop="content">
                        <el-input type="textarea" v-model="contentForm.content" placeholder="请输入内容" size="small">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" class="searchBtn" size="mini" @click="saveContent('form')">确 定</div>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    nextTick,
    onMounted,
    
    computed,
    watch
} from 'vue'
import { useAppStore } from '@/stores/app'

export default {
    name: 'maincontent',
    props: ['nav'],
    setup() {
        const api = inject('$api')
        const selectId = ref([])
        const form = ref(null)
        const tree = ref(null)
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            props: {
                label: 'name',
            },
            data: null,
            dialogContentVisible: false,
            stdData: [],
            items: [], //表格select
            contentForm: {
                id: null,
                name: '',
                content: '',
                projectId: null,
                deviceType: null,
                // type: 1
            },
            selectId: [], //tree check
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change',
                }, ],
                content: [{
                    required: true,
                    message: '内容不能为空',
                    trigger: 'change',
                }, ],
            },
        })
        onMounted(() => {
            getProjectDeviceType()
            getContentList()
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectDeviceType()
                getContentList()
            }
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        const getContentList = () => {
            api.getContent({
                deviceType: selectId.value.length > 0 ? selectId.value[0].id : null,
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size
            }).then((res) => {
                state.list = res.data;
                state.total = res.total;
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getContentList()
        }
        const getProjectDeviceType = () => {
            api.getDeviceTypeTree({
                projectId: getCookie("gh_projectId")
            }).then((res) => {
                state.data = res.data
            })
        }
        const handleCheckChange = (data, state) => {
            selectId.value = []
            let keys = tree.value.getCheckedKeys()
            if (keys.length == 0 && state.checkedKeys.length == 0) {
                tree.value.setChecked(data.id, false)
            } else {
                if (keys.length > 0)
                    keys.forEach((k) => {
                        if (k != data.id) tree.value.setChecked(k, false)
                    })
                tree.value.setChecked(data.id, true)
                selectId.value.push(data)
            }
            getContentList()
        }

        const delMain = () => {
            if (state.items.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择要删除的维保内容',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.deleteContent({
                    ids: state.items,
                }).then((res) => {
                    getContentList()
                    state.items = []
                })
            })
        }
        const edit = (row) => {
            state.dialogContentVisible = true
            state.contentForm = Object.assign({}, row)
            selectId.value[0] = {
                id: row.deviceType,
            }
        }
        const add = () => {
            let keys = tree.value.getCheckedKeys()
            if (keys.length === 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择设备类型',
                })
                return
            }
            state.contentForm = {
                id: null,
                name: '',
                content: '',
                projectId: null,
                deviceType: null
            }
            state.dialogContentVisible = true
            nextTick(() => {
                form.value.resetFields()
            })
        }

        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }
        const saveContent = (formName) => {
            proxy.$refs[formName].validate((validate) => {
                if (validate) {
                    state.contentForm.projectId = getCookie("gh_projectId")
                    state.contentForm.deviceType = selectId.value[0].id
                    if (state.contentForm.id > 0) {
                        api.editMaintenanceContent(state.contentForm).then((res) => {
                            state.dialogContentVisible = false
                            getContentList()
                        })
                    } else {
                        api.addMaintenanceContent(state.contentForm).then((res) => {
                            ElMessage.success(res.msg)
                            state.dialogContentVisible = false
                            getContentList()
                        })
                    }
                }
            })
        }
        return {
            ...toRefs(state),
            tree,
            selectId,
            form,
            getContentList,
            handleCurrentChange,
            getProjectDeviceType,
            handleCheckChange,
            delMain,
            edit,
            add,
            select,
            saveContent,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;

    .right_tab {
        flex: 1;
        padding: 0 20px;
    }

    .left-content {
        width: 377px;
        height: 500px;
        padding: 8px;

        .tree-list {
            border: 1px solid #2b2e32;
        }
    }
}
</style>
