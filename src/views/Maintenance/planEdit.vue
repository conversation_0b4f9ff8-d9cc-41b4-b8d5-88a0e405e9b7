<template>
    <el-dialog align-center :append-to-body="true" draggable custom-class="addDiagram" width="840px" @open="open"
        @closed="close" :model-value="show" @update:model-value="updateShow" :title="title">
        <panel title="维保计划" />
        <el-form label-position="right" label-width="130px" class="form" ref="form" :model="planForm" :rules="rule">
            <el-form-item label="维保名称:" prop="name">
                <el-input v-model="planForm.name" placeholder="请输入维保名称"></el-input>
            </el-form-item>

            <el-form-item label="维保周期(天):" prop="period">
                <el-input v-model.number="planForm.period" placeholder="请输入周期"></el-input>
            </el-form-item>
            <el-form-item label="超时时间(天):" prop="period">
                <el-input v-model.number="planForm.delay" placeholder="请输入周期"></el-input>
            </el-form-item>
            <el-form-item label="开始时间(天):" prop="period">
                <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" style="width:100%" v-model="planForm.firstTime"
                    popper-class="select_panel" type="date" placeholder="选择日期">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="状态:">
                <el-select v-model="planForm.status" placeholder="请选择" class="w100">
                    <el-option :value="true" label="启用"></el-option>
                    <el-option :value="false" label="禁用"></el-option>
                </el-select>
            </el-form-item>

        </el-form>
        <panel title="设备关联" />
        <div class="btn-group search_box">
            <div icon="Plus" type="primary" class="searchBtn" size="mini" @click="addDevice">新增</div>
        </div>
        <div class="table">
            <el-table :data="planForm.devices" size="small" height="300" fit table-layout="auto">
                <template #empty>
                    <noData />
                </template>
                <el-table-column prop="name" label="设备名称" align="center">
                </el-table-column>

                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button type="text" @click="remove(scope.$index, scope.row)" class="del">移除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="center search_box">
            <div type="primary" class="searchBtn" size="mini" @click="savePlan('form')">保存</div>
        </div>
        <el-dialog align-center :append-to-body="true" v-model="dialogDeviceVisible" title="设备选择" draggable
            custom-class="addDiagram border0" width="700px">
            <el-scrollbar class="tree">
                <el-tree :filter-node-method="filterNode" ref="tree" :load="loadNode" lazy node-key="id" :props="props"
                    show-checkbox />
            </el-scrollbar>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn" size="mini" @click="saveDevice">确 定</div>
                </div>
            </template>
        </el-dialog>
    </el-dialog>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    computed,
    
    onMounted,
    watch
} from 'vue'
import {
    useRoute,
    useRouter
} from 'vue-router'
import {
    ElMessage
} from 'element-plus'
import { useAppStore } from '@/stores/app'


export default {
    props: ['show', 'id'],

    setup(props, { emit }) {
        const route = useRoute()
        const router = useRouter()
        const store = useAppStore()
        const tree = ref(null)
        const api = inject('$api')
        const state = reactive({
            planForm: {
                id: 0,
                name: '',
                status: true,
                period: 30,
                projectId: 0,
                count: 0,
                firstTime: null,
                delay: 7,
                devices: [],
            },
            title: '',
            dialogDeviceVisible: false,
            props: {
                label: 'name',
                children: 'children',
            },
            devices: [],
            filter: '',
            rule: {
                name: [{
                    required: true,
                    message: '维保名称不能空',
                    trigger: 'change',
                },],
                period: [{
                    required: true,
                    message: '维保周期不能为空',
                    trigger: 'change',
                },
                {
                    type: 'number',
                    mesaage: '输入正确的数字',
                },
                ],
            },
        })
        onMounted(() => {
            // let id = route.params.id
            // if (id > 0) {
            //     getPlanList(id)
            // }
        })
        const open = () => {
            if (props.id > 0) {
                getPlanList(props.id)
            }
        }
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getPlanList()
            }
        })
        const getPlanList = (id) => {
            api.getMainPlan({
                id: id,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                if (res.data.length > 0) {
                    state.planForm = res.data[0]
                    state.planForm.status = state.planForm.status
                }
            })
        }
        // 点击新增
        const addDevice = () => {
            state.dialogDeviceVisible = true
        }
        const saveDevice = () => {
            let nodes = tree.value.getCheckedNodes()

            let data= nodes.filter(d => !d.type);
            let existData=JSON.parse(JSON.stringify(state.planForm.devices));
            for(let i=0;i<data.length;i++){
                let exist = existData.some(device => device.id == data[i].id);
                if (!exist) {
                    state.planForm.devices.push({
                        id: data[i].id,
                        name: data[i].name,
                    });
                }
            }
            state.dialogDeviceVisible = false
        }
        const savePlan = (formName) => {
            proxy.$refs[formName].validate((validate) => {
                if (validate) {
                    state.planForm.projectId = getCookie('gh_projectId')
                    state.planForm.count = state.planForm.devices.length
                    if (props.id == 0) {
                        api.addMainPlan(state.planForm).then((res) => {
                            if (res.success)
                                ElMessage.success(res.msg)
                            back()
                        })
                    } else {
                        api.editMainPlan(state.planForm).then((res) => {
                            if (res.success)
                                ElMessage.success("编辑成功")
                            back()
                        })
                    }
                }
            })
        }
        const back = () => {
            emit("update:show", false)
            emit("getPlanList")
        }
        const remove = (index, row) => {
            state.planForm.devices.splice(index, 1)
        }
        const loadNode = (node, resolve) => {
            if (node.level === 0 && getCookie("gh_projectId") > 0) {
                state.node = node
                state.resolve = resolve
                api.getDeviceTypeTree({
                    projectId: getCookie("gh_projectId"),
                }).then((res) => {
                    let data = []
                    res.data.forEach((d) => {
                        data.push({
                            id: d.id,
                            name: d.name,
                            type: true,
                            // disabled: true,
                        })
                    })
                    return resolve(data)
                })
            } else if (node.level == 1) {
                api.getBasicDevices({
                    projectId: getCookie("gh_projectId"),
                    deviceType: node.data.id,
                }).then((res) => {
                    let data = []
                    res.data.forEach((d) => {
                        data.push({
                            id: d.id,
                            name: d.name,
                            isLeaf: true,
                            code: d.code,
                            deviceType: d.deviceTypeName,
                            areaName: d.areaName,
                        })
                    })
                    return resolve(data)
                })
            }
            return resolve([])
        }
        const filterNode = (value, data) => {
            if (!value) return true
            return data.name.indexOf(value) !== -1
        }
        const close = () => {
            emit('update:show', false)
        }
        const updateShow = (value) => {
            emit('update:show', value)
        }
        return {
            ...toRefs(state),
            tree,
            getPlanList,
            addDevice,
            saveDevice,
            savePlan,
            back,
            remove,
            loadNode,
            filterNode,
            open,
            close,
            updateShow
        }
    },

}
</script>

<style lang="scss" scoped>
.plan_container {
    padding: 0 15px;

    .wrapper {
        display: flex;
        flex-direction: column;
        height: 100%;

        .table {
            height: 40%;
        }

    }
}

.del {
    color: #b4061a;
}

.del:hover {
    color: #b4061a;
}

.del :focus {
    color: #b4061a;
}

.btn-group {
    margin-top: 20px;
}

// .el-form {
//     width: 75%;
//     margin: 20px auto;

//     .el-form-item {
//         width: calc(50% - 70px);
//         display: inline-block;
//     }
// }

.tree {
    height: 300px;
}
</style>
