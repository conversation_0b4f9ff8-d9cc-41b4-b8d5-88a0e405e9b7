<template>
<div class="setting">
    <div class="btns">
        <div class="center cursor" :class="[component=='content'?'active':'']" @click="change('content')">维保内容</div>
        <div class="center cursor" :class="[component=='plan'?'active':'']" @click="change('plan')">维保计划</div>
    </div>
    <div>
        <component :is="component"></component>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs
} from "vue";

import content from './content.vue'
import plan from './plan.vue'
export default defineComponent({
    name: "weibosetting",
    components: {
        content,
        plan
    },
    setup() {
        const state = reactive({
            component: 'content',
        })

        const change = (name) => {
            state.component = name;
        }

        return {
            ...toRefs(state),
            change,
        }
    }
})
</script>

<style lang="scss" scoped>
.setting {
    display: flex;

    .btns {

        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: bold;
        color: #84B2FF;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        padding: 25px;

        &>div {
            margin-bottom: 15px;
            width: 136px;
            height: 34px;
            background: rgba(27, 109, 194, 0.2);
            border: 1px solid #58B3F5;
            box-shadow: 0px 0px 8px 0px #58B3F5 inset;
        }

    }

    &>div:nth-child(2) {
        flex: 1
    }

    .active {
        background: #1B6DC2 !important;
    }
}
</style>
