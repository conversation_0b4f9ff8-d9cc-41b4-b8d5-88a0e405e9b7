<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box " size="small">
        <el-form-item label="名称">
            <el-input v-model="keyword" placeholder="请输入名称查询"></el-input>
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="search">查询</div>
        </el-form-item>
        <div class="btn-group ">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addPlan">新增</div>
            <div type="primary" icon="Minus" class="delBtn" size="mini" @click="del">删除</div>
        </div>
    </el-form>

    <div class="table">
        <el-table :data="list" height="530" fit @select="select" @select-all="select" table-layout="auto">
            <template #empty>
                <noData />
            </template>
            <el-table-column type="selection" width="55" align="center">
            </el-table-column>
            <el-table-column prop="name" label="计划名称" align="center">
            </el-table-column>
            <el-table-column prop="period" label="维保周期(天)" align="center">
            </el-table-column>
            <el-table-column prop="firstTime" label="开始时间" align="center">
            </el-table-column>
            <el-table-column prop="delay" label="超时时间(天)" align="center">
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span style="color: #13d4d9" v-if="scope.row.status">启用</span>
                    <span style="color: red" v-if="!scope.row.status">禁用</span>
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" class="editBtn" @click="edit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>

    <edit v-model:show="show" :id="id" @getPlanList="getPlanList"></edit>
</div>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    computed,
    
    onMounted,
    reactive,
    toRefs,
    watch
} from 'vue'
import {
    useRouter
} from 'vue-router'

import edit from './planEdit.vue'
import { useAppStore } from '@/stores/app'
export default {
    name: 'mainplan',
    components: {
        edit
    },
    setup() {
        const router = useRouter()
        const store = useAppStore()
        const api = inject('$api')
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            data: null,
            items: [], //表格select
            date: [],
            type: '',
            keyword: '',
            show: false,
            id: 0
        })

        onMounted(() => {
            getPlanList()
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) getPlanList()
        })
        const getPlanList = () => {
            api.getMainPlan({
                keyword: state.keyword,
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getPlanList()
        }

        const del = () => {
            if (state.items.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择要删除的巡检线路',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.deletePlan({
                    ids: state.items,
                }).then((res) => {
                    getPlanList()
                    state.items = []
                })
            })
        }

        const edit = (row) => {
            state.id = row.id;
            state.show = true;
        }
        const addPlan = () => {
            state.id = 0;
            state.show = true;
        }

        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }

        const search = () => {
            state.page = 1
            getPlanList()
        }
        return {
            ...toRefs(state),
            getPlanList,
            search,
            select,
            handleCurrentChange,
            del,
            addPlan,
            edit,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    flex-direction: column;
}
</style>
