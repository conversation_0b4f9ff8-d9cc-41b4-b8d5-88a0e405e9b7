<template>
    <div class="energy_container">
        <div class="content-top">
            <div class="item-column column1">
                <sub-title title="24小时能耗-电/kwh"></sub-title>
                <div class="body_chart">
                    <LineChart :data="edata"></LineChart>
                </div>
            </div>
            <div class="item-column column2">
                <sub-title title="24小时能耗-水/T"></sub-title>
                <div class="body_chart">
                    <LineChart :data="wdata"></LineChart>
                </div>
            </div>
            <!-- <div class="item-column column3">
                <sub-title title="24小时能耗-气/kwh"></sub-title>
                <div class="body_chart">
                    <LineChart :data="gdata"></LineChart>
                </div>
            </div> -->
        </div>
        <div class="content-row">
            <div class="box">
                <div class="item-panel">
                    <sub-title title="耗电量/kwh"></sub-title>
                    <div class="top">
                        <div class="box">
                            <img src="@/assets/images/jr.svg" />
                            <Line />
                            <div class="item">
                                <div class="tit">今日实时</div>
                                <div class="num">
                                    {{ numberFormat(electricityData.currentDayValue) }}
                                    <i :class="electricityData.currentDayValue > electricityData.lastDayValue ? 'iconfont iconshangsheng' : 'iconfont iconxiajiang'"
                                        :style="{ color: electricityData.currentDayValue > electricityData.lastDayValue ? '#d81e06' : '#1afa29', fontSize: '22px' }"></i>
                                </div>
                            </div>

                        </div>
                        <div class="box">
                            <img src="@/assets/images/qt.svg" />
                            <Line />
                            <div class="item">
                                <div class="tit">预计全天</div>
                                <div class="num">{{ numberFormat(predict(electricityData.currentDayValue)) }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="card-item">
                            <div class="item">
                                <div class="name yesterday">昨日</div>
                                <count-to :start-val="0" :end-val="electricityData.lastDayValue" :duration="2000"
                                 :decimals="2"   class="total">
                                </count-to>
                            </div>
                            <div class="item">
                                <div class="name month">本月</div>
                                <count-to :start-val="0" :end-val="electricityData.currentMonthValue" :duration="2000"
                                  :decimals="2"  class="total">
                                </count-to>
                            </div>
                        </div>
                        <div class="card-item">
                            <div class="item">
                                <div class="name ultino">上月</div>
                                <count-to :start-val="0" :end-val="electricityData.lastMonthValue" :duration="2000"
                                    class="total">
                                </count-to>
                            </div>
                            <div class="item">
                                <div class="name year">本年</div>
                                <count-to :start-val="0" :end-val="electricityData.currentYearValue" :duration="2000"
                                    class="total">
                                </count-to>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="item-panel">
                    <sub-title title="耗水量/T"></sub-title>
                    <div class="top">
                        <div class="box">
                            <img src="@/assets/images/jr.svg" />
                            <Line />
                            <div class="item">
                                <div class="tit">今日实时</div>
                                <div class="num">
                                    {{ numberFormat(waterData.currentDayValue) }}
                                    <i :class="waterData.currentDayValue > waterData.lastDayValue ? 'iconfont iconshangsheng' : 'iconfont iconxiajiang'"
                                        :style="{ color: waterData.currentDayValue > waterData.lastDayValue ? '#d81e06' : '#1afa29', fontSize: '22px' }"></i>
                                </div>
                            </div>
                        </div>
                        <div class="box">
                            <img src="@/assets/images/qt.svg" />
                            <Line />
                            <div class="item">
                                <div class="tit">预计全天</div>
                                <div class="num">{{ numberFormat(predict(waterData.currentDayValue)) }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="card-item">
                            <div class="item">
                                <div class="name yesterday">昨日</div>
                                <count-to :start-val="0" :end-val="waterData.lastDayValue" :duration="2000"
                                    class="total"></count-to>
                            </div>
                            <div class="item">
                                <div class="name month">本月</div>
                                <count-to :start-val="0" :end-val="waterData.currentMonthValue" :duration="2000"
                                    class="total">
                                </count-to>
                            </div>
                        </div>
                        <div class="card-item">
                            <div class="item">
                                <div class="name ultino">上月</div>
                                <count-to :start-val="0" :end-val="waterData.lastMonthValue" :duration="2000" class="total">
                                </count-to>
                            </div>
                            <div class="item">
                                <div class="name year">本年</div>
                                <count-to :start-val="0" :end-val="waterData.currentYearValue" :duration="2000"
                                    class="total">
                                </count-to>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
</template>
    
<script>
import CountTo from '@/components/vueCountTo/vue-countTo.vue'
import card from '@/components/energy/card.vue'
import meterChart from '@/components/energy/meterChart.vue'
import rankChart from '@/components/energy/rankChart.vue'
import rippleChart from '@/components/energy/rippleChart.vue'
import lineChart from './componet/lineChart1.vue'
import {
    reactive,
    toRefs,
    onMounted,
    computed,
    getCurrentInstance
} from 'vue'

import {
    getCookie
} from '@/utils/cookie'

export default {
    name: 'trend',
    components: {
        card,
        meterChart,
        rankChart,
        rippleChart,
        CountTo,
        "LineChart": lineChart
    },
    setup() {
        const api = inject('$api')
        const state = reactive({
            rankData: [0, 0],
            rankText: ['今日实时', '预计全天'],
            electricityData: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0,
            },
            waterData: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0
            },
            gasData: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0,
            },
            realAllCose: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0,
            },
            data: {
                electric: 0,
                gas: 0,
                water: 0,
                count: 0,
                areaAll: 0,
            },
            bodyBgImage: 'url(' + new URL('@/assets/images/home.jpg', import.meta.url).href + ')',
            styleObject: {

            },
            edata: [],
            wdata: [],
            gdata: []
        })

        onMounted(() => {
            getEnergyOver(1, state.electricityData)
            getEnergyOver(2, state.waterData)
            // getEnergyOver(3, state.gasData)
            api.energyDay({
                type: 1,
                projectId: getCookie('gh_projectId') || 2
            }).then(res => {
                state.edata = res.data;
            })
            api.energyDay({
                type: 2,
                projectId: getCookie('gh_projectId') || 2
            }).then(res => {
                state.wdata = res.data;
            })
            // api.energyDay({
            //     type: 3,
            //     projectId: getCookie('gh_projectId') || 2
            // }).then(res => {
            //     state.gdata = res.data;
            // })
        })
        const numberFormat = (num) => {
            if(num)
            {
                num=Number(num).toFixed(2)
            }
            if (num >= 10000) {
                return toFixed1(num / 10000) + '万'
            } else {
                return num
            }
        }
        const toFixed1 = (num) => {
            num = num.toString()
            const index = num.indexOf('.')
            if (index !== -1) {
                num = num.substring(0, index + 2)
                return parseFloat(num).toFixed(1)
            } else {
                num = num.substring(0)
                return parseFloat(num)
            }
        }
        const predict = (data) => {
            if (data !== 0) {
                let num = data*24 / new Date().getHours()
                return num.toFixed(2)
            } else return 0
        }
        // 用量统计
        const getEnergyOver = (type, data) => {
            api.getEnergyOverView({
                type: type
            }).then((res) => {
                res.data.forEach((element) => {
                    Object.keys(element).forEach((key) => {
                        if (key !== 'time') {
                            data[key] = element[key]
                        }
                    })
                })
            })
        }
        //费用统计
        const getPeriod = () => {
            api.periodCharge().then((res) => {
                res.data.forEach((element) => {
                    Object.keys(element).forEach((key) => {
                        switch (key) {
                            case 'currentDayValue':
                                state.realAllCose[key] = element[key]
                                break
                            case 'lastDayValue':
                                state.realAllCose[key] = element[key]
                                break
                            case 'currentMonthValue':
                                state.realAllCose[key] = element[key]
                                break
                            case 'lastMonthValue':
                                state.realAllCose[key] = element[key]
                                break
                            case 'currentYearValue':
                                state.realAllCose[key] = element[key]
                                break
                        }
                    })
                })
                state.rankData = [
                    state.realAllCose.currentDayValue,
                    state.realAllCose.currentDayValue,
                ]
            })
        }
        // 获取本年平均每天的能耗
        const getUnitAreaEnergy = () => {
            api.unitAreaEnergyAll().then((res) => {
                state.data = {
                    electric: res.data.electric,
                    gas: res.data.gas,
                    water: res.data.water,
                    count: res.data.count,
                    areaAll: 20000,
                }
            })
        }
        const comprehensiveElectric = computed(() => {
            const data = comprehensiveEnergy.value / 0.1229
            return Number(data.toFixed(2))
        })
        const comprehensiveEnergy = computed(() => {
            if (state.data.count > 0 && state.data.areaAll > 0) {
                let elecCoal = 0;
                let gasCoal = 0;
                let waterCoal = 0;
                if (state.data.electric > 0) {
                    elecCoal =
                        ((state.data.electric / state.data.count) * 365 * 0.1229) /
                        state.data.areaAll
                }
                if (state.data.gas > 0) {
                    gasCoal =
                        ((state.data.gas / state.data.count) * 365 * 1.2143) /
                        state.data.areaAll
                }
                if (state.data.water > 0) {
                    waterCoal =
                        ((state.data.water / state.data.count) * 365 * 0.0857) /
                        state.data.areaAll
                }
                const num = Number((elecCoal + gasCoal + waterCoal).toFixed(2))
                return num
            } else {
                return 0
            }
        })

        return {
            ...toRefs(state),
            getEnergyOver,
            getPeriod,
            getUnitAreaEnergy,
            comprehensiveElectric,
            comprehensiveEnergy,
            predict,
            numberFormat,
            toFixed1
        }
    },
}
</script>
    
<style lang="scss" scoped>
.energy_container {
    // padding-top: 70px;
    // padding-bottom: 117px;
    width: 100%;
    background: linear-gradient(270deg, #000812 0%, rgba(0, 14, 30, 0.8) 72.19%, rgba(0, 15, 32, 0.5) 83.65%, rgba(0, 12, 26, 0) 100%);
    .content-top {
        width: 100%;
        height: 290px;
        display: flex;

        .column1,
        .column2 {
            flex: 1;
            margin-right: 15px;
        }

        .column3 {
            flex: 1;
        }

        .body_chart {
            width: 100%;
            height: calc(100% - 49px);
        }

        .row-chart {
            height: calc(100% - 48px);
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .line-b {
            width: 1px;
            height: 138px;
            background-color: rgba(151, 151, 151, 0.5);
            margin: 0 10px;
        }

        &-right {
            width: 280px;
            height: 100%;
            display: flex;
            flex-direction: column;

            .box {
                height: 45%;
                display: flex;
                align-items: center;

                .item {
                    margin-left: 5px;
                }
            }
        }

        .column_chart {
            flex: 1;
            height: 100%;

            .chart_row {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: space-around;
                align-items: center;

                .item {
                    flex: 1;
                }
            }
        }
    }

    .content-row {
        width: 100%;
        flex: 1;
        height: calc(100% - 300px);

        .box {
            display: flex;
            height: 100%;

            .item-panel {
                flex: 1;
                margin-right: 30px;

                &:last-child {
                    margin-right: 0;
                }

                .top {
                    display: flex;
                    flex-direction: row;
                    padding: 10px 0 10px 0;

                    .box {
                        flex: 1;
                        display: flex;
                        align-items: center;

                        .item {
                            margin-left: 5px;
                        }
                    }
                }

                &:last-child {
                    margin-right: 0;
                }

                .card-content {
                    height: calc(100% - 168px);
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    .card-item {
                        display: flex;
                        height: calc(50% - 10px);
                        color: #cdcdcd;

                        .item {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            border: 1px solid #363b3d;
                            padding: 5px 0;

                            &:first-child {
                                margin-left: 0px;
                                margin-right: 15px;
                            }

                            &:last-child {
                                margin-right: 0;
                            }

                            .total {
                                width: 78%;
                                height: 30px;
                                line-height: 30px;
                                background: rgba(255, 255, 255, 0.05);
                                text-align: center;
                                font-size: 25px;
                                font-family: "DINAlternate-Bold", "DINAlternate";
                                font-weight: bold;
                                color: #3de9fa;
                            }

                            .name {
                                font-size: 18px;
                                font-family: "PingFangSC-Medium", "PingFang SC";
                                font-weight: 500;
                                margin-bottom: 20px;
                            }

                            .yesterday {
                                background: linear-gradient(180deg, #ffffff 0%, #ed7327 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }

                            .month {
                                background: linear-gradient(180deg, #ffffff 0%, #e5950d 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }

                            .ultino {
                                background: linear-gradient(180deg, #ffffff 0%, #27edbb 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }

                            .year {
                                background: linear-gradient(180deg, #ffffff 0%, #27a6ed 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }
                        }
                    }
                }
            }
        }

        .item-column {
            .row-chart {
                height: calc(100% - 28px);
                display: flex;
            }

            .right {
                width: 220px;
            }

            .column_chart {
                flex: 1;
                height: 100%;

                .chart_row {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: space-around;

                    .item {
                        flex: 1;
                    }
                }
            }
        }
    }

    .num {
        height: 47px;
        font-size: 32px;
        font-family: "DINAlternate-Bold", "DINAlternate";
        font-weight: bold;
        color: #3de9fa;
        line-height: 47px;
    }

    .tit {
        height: 22px;
        font-size: 16px;
        font-family: "PingFangSC-Regular", "PingFang SC";
        font-weight: 400;
        color: #778897;
        line-height: 22px;
        margin-bottom: 8px;
    }
}
</style>
    