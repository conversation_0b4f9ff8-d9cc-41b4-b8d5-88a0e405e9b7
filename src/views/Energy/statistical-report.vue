<template>
    <div class="energy_container">
        <!-- <div class="content_tab">
            <div class="left_tab">
                <span class="btn" v-for="(item, i) in dosageTab" :key="i" :class="{ active: dosageType == item.id }"
                    @click="changeDosage(item.id)">{{ item.name }}</span>
            </div>
            <type-btn :typeData="typeData" @clickDeviceType="changeMater" />
        </div> -->
        <div class="content_body">
            <!-- <div class="content_body_item">
                <sub-title2 title="统计概要"></sub-title2>
                <div class="card_list">
                    <div class="item1">
                        <div class="card_list-left">
                            累计总能耗
                            <div class="total_val">{{ statisticalSummary.totalValue }}</div>
                            <img src="@/assets/images/zl.svg" />
                        </div>
                        <div class="card_list-right">
                            <div class="info" style="margin-bottom:35px">
                                <div class="icon">
                                    <img src="@/assets/images/7.svg" />
                                </div>
                                <div class="day">
                                    近7日总能耗
                                    <div class="value">{{ statisticalSummary.sevenValue }}</div>
                                </div>
                                <div class="tb">
                                    同比
                                    <div class="week">较上周0%</div>
                                </div>
                            </div>
                            <div class="info">
                                <div class="icon">
                                    <img src="@/assets/images/30.svg" />
                                </div>
                                <div class="day">
                                    近30日总能耗
                                    <div class="value">{{ statisticalSummary.thirtyValue }}</div>
                                </div>
                                <div class="tb">
                                    同比
                                    <div class="month">较上月0%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="borderLeft"></div>
                    <div class="item2">
                        <div class="alarm">
                            <div class="box energy">
                                <div class="icon">
                                    <img src="@/assets/images/jr.svg" />
                                </div>
                                <Line />
                                <div class="count">
                                    <div class="tit">能源报警总数</div>
                                    <div class="num">0</div>
                                </div>
                            </div>
                            <div class="box">
                                <div class="icon">
                                    <img src="@/assets/images/qt.svg" />
                                </div>
                                <Line />
                                <div class="count">
                                    <div class="tit">已处理报警数</div>
                                    <div class="num">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="borderLeft"></div>
                    <div class="item3">
                        <div class="echart">
                            <on-line-chart :total="total" :online="online"></on-line-chart>
                        </div>
                        <div class="box">
                            <div class="device_name">总错误率</div>
                            <div class="device_num">{{ ((online / total) * 100).toFixed(2) }}%</div>
                        </div>
                    </div>
                    <div class="borderLeft"></div>
                    <div class="item4">
                        <div class="order">
                            转工单数：<span class="order_count">0</span>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="content_body_item">
                <sub-title2 title="整体趋势">
                    <tabs :typeData="chartSelectBtn" ref="tabs" @clickDeviceType="clickDateBtn" />
                </sub-title2>
                <el-form :inline="true" class="form-inline form" size="small">
                    <el-form-item label="时间选择">
                        <el-date-picker @change="selectedDate" v-model="date" type="daterange" range-separator="至"
                            value-format="YYYY-MM-DD" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
                <div class="item_chart">
                    <columnarChart :unitName="unitName" :chartData="overTrendData" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import tabs from '@/components/energy/tabs.vue'
import typeBtn from "@/components/energy/typeBtn.vue"
import columnarChart from "@/components/energy/columnarChart.vue"
import onLineChart from '@/components/echarts/onLineChart.vue'
import dayjs from "dayjs"
import {
    reactive,
    toRefs,
    onMounted,
    getCurrentInstance
} from 'vue'
export default {
    name: 'statisticalReport',
    components: {
        typeBtn,
        tabs,
        columnarChart,
        onLineChart
    },
    setup() {
        const api = inject('$api')
        const state = reactive({
            typeData: [{
                name: '电',
                id: 1
            },
            {
                name: '水',
                id: 2
            },
            // {
            //     name: '气',
            //     id: 3
            // }
            ],
            meterType: 1,
            dosageTab: [{
                name: '能源分项',
                id: 'category'
            },
            {
                name: '建筑分区',
                id: 'area'
            },
            {
                name: '功能分区',
                id: 'feature'
            }
            ],
            dosageType: '',
            dateLabel: [],
            chartSelectBtn: [{
                name: '近7日',
                id: 1
            },
            {
                name: '近30日',
                id: 2
            }
            ],
            chartBtnType: 1,
            date: [],
            statisticalSummary: {},
            overTrendData: {
                xData: [],
                yData1: [],
                yData2: []
            },
            total: 2805763,
            online: 100000,
            unitName: 'kWh'
        })

        onMounted(() => {
            state.dosageType = state.dosageTab[0].id;
            changeDosage(state.dosageType)
        })
        const changeMater = (id) => {
            if(id==2)
            {
                state.unitName = 't';
            }else
            {
                state.unitName = 'kWh';
            }
            state.meterType = id;
            //historyOverviewAllPage();
            overviewTrendPage()
        }
        const changeDosage = (data) => {
            state.dosageType = data;
           // historyOverviewAllPage();
            clickDateBtn(state.chartBtnType);
        }
        const historyOverviewAllPage = () => {
            api.historyOverviewAll({
                deviceType: state.meterType,
                measurement: state.dosageType
            }).then(res => {
                const data = res.data
                data.forEach(element => {
                    Object.keys(element).forEach(item => {
                        if (item !== 'time') {
                            if (state.statisticalSummary.hasOwnProperty(item)) {
                                state.statisticalSummary[item] = element[item]
                            } else {
                                state.statisticalSummary = Object.assign({}, state.statisticalSummary, {
                                    [item]: element[item]
                                })
                            }

                        }
                    })
                });
            })
        }
        const overviewTrendPage = () => {
            const params = {
                deviceType: state.meterType,
                measurement: state.dosageType,
                bt: dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }
            api.overviewTrend(params).then(res => {
                state.overTrendData = {
                    xData: [],
                    yData1: [],
                    yData2: []
                };
                //ts排序
                // res.data.thischartData.sort((a, b) => {
                //     return new Date(a.ts) - new Date(b.ts)
                // })
                res.data.thisChartData.sort((a,b)=>{
                    return new Date(a.ts) - new Date(b.ts)
                }).forEach(item => {
                    state.overTrendData.xData.push(item.ts.split(' ')[0]);
                    state.overTrendData.yData1.push(item._aggregate.toFixed(2))
                })
                res.data.lastChartData.forEach(item => {
                    state.overTrendData.yData2.push(item._aggregate.toFixed(2))
                })
            })
        }
        const selectedDate = (option) => {
            if (option != null && typeof option == 'object') {
                proxy.$refs.tabs.checkType = -1
                state.date[0] = dayjs(option[0]).format('YYYY-MM-DD 00:00:00');
                state.date[1] = dayjs(option[1]).format('YYYY-MM-DD 23:59:59');
                overviewTrendPage()
            }
        }
        const clickDateBtn = (id) => {
            state.chartBtnType = id
            switch (id) {
                case 1:
                    state.date[0] = dayjs().add(-7, 'day').format('YYYY-MM-DD 00:00:00');
                    state.date[1] = dayjs().format('YYYY-MM-DD 23:59:59');
                    break
                case 2:
                    state.date[0] = dayjs().add(-30, 'day').format('YYYY-MM-DD 00:00:00');
                    state.date[1] = dayjs().format('YYYY-MM-DD 23:59:59');
                    break
            }
            overviewTrendPage()
        }

    

        return {
            ...toRefs(state),
            overviewTrendPage,
            historyOverviewAllPage,
            clickDateBtn,
            selectedDate,
            changeDosage,
            changeMater
        }
    }
}
</script>

<style lang="scss" scoped>
.energy_container {

    width: 100%;

    .content_body {
        display: flex;
        flex-direction: column;
        height: calc(100% - 10px);
        padding: 0 15px;

        &_item {
            flex: 1;

            .card_list {
                height: calc(100% - 49px);
                width: 100%;
                display: flex;
                align-items: center;

                .borderLeft {
                    height: 45%;
                    border-left: 1px solid rgba(151, 151, 151, 0.5);
                    margin: 0 30px;
                }

                .item1 {
                    flex: 1;
                    display: flex;
                    height: 100%;
                    align-items: center;

                    .card_list-right {
                        flex: 1;
                        display: flex;
                        flex-direction: column;

                        .info {
                            display: flex;
                            background: #000000;
                            padding: 8px 10px;
                            justify-content: space-around;
                            align-items: center;
                            border: 1px solid rgba(255, 255, 255, 0.2);

                            .icon {
                                width: 70px;
                                height: 49px;

                                img {
                                    width: 100%;
                                    height: 100%;
                                }
                            }

                            .tb {
                                font-size: 14px;
                                font-family: "PingFangSC-Regular", "PingFang SC";
                                font-weight: 400;
                                color: #ffffff;

                                .week {
                                    color: #ffeb6d;
                                }

                                .month {
                                    color: #13d4d9;
                                }
                            }

                            .day {
                                font-size: 14px;
                                font-family: "PingFangSC-Regular", "PingFang SC";
                                font-weight: 400;
                                color: #778897;

                                .value {
                                    font-size: 25px;
                                    font-family: "DINAlternate-Bold", "DINAlternate";
                                    font-weight: bold;
                                    color: #ffffff;
                                }
                            }
                        }
                    }

                    &-left {
                        width: 300px;
                        font-size: 16px;
                        font-family: "PingFangSC-Regular", "PingFang SC";
                        font-weight: 400;
                        color: #778897;

                        .total_val {
                            font-size: 40px;
                            font-family: "DINAlternate-Bold", "DINAlternate";
                            font-weight: bold;
                            color: #e3731b;
                        }
                    }
                }

                .item3 {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 270px;
                    height: 100%;

                    .echart {
                        width: 120px;
                        margin-right: 10px;
                        height: 100%;
                    }

                    .box {
                        .device_name {
                            font-size: 18px;
                            font-family: "PingFangSC-Regular", "PingFang SC";
                            font-weight: 400;
                            color: #778897;
                        }

                        .device_num {
                            font-size: 32px;
                            font-family: "DINAlternate-Bold", "DINAlternate";
                            font-weight: bold;
                            color: #ffffff;
                        }
                    }
                }

                .item2 {
                    width: 380px;

                    .alarm {
                        display: flex;
                        flex-direction: column;

                        .energy {
                            margin-bottom: 10px;
                        }

                        .box {
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .icon {
                                width: 48px;
                                height: 48px;

                                img {
                                    width: 100%;
                                    height: 100%;
                                }
                            }

                            .count {
                                width: 150px;
                                padding-left: 5px;

                                .tit {
                                    font-size: 16px;
                                    font-family: "PingFangSC-Regular", "PingFang SC";
                                    font-weight: 400;
                                    color: #778897;
                                }

                                .num {
                                    font-size: 40px;
                                    font-family: "DINAlternate-Bold", "DINAlternate";
                                    font-weight: bold;
                                    color: #ffffff;
                                }
                            }
                        }
                    }
                }

                .item4 {
                    width: 360px;

                    .order {
                        display: flex;
                        align-items: center;
                        font-size: 32px;
                        font-family: "PingFangSC-Medium", "PingFang SC";
                        font-weight: 500;
                        color: #ffffff;

                        .order_count {
                            font-size: 60px;
                            font-family: "DINAlternate-Bold", "DINAlternate";
                            font-weight: bold;
                            color: #e3731b;
                        }
                    }
                }
            }

            .item_chart {
                width: 100%;
                height: calc(100% - 90px);
            }
        }
    }
}
</style>
