<template>
    <div class="energy_container">
        <div class="top">
            <div class="content_tab">
                <div class="left_tab">
                    <span class="btn" v-for="(item, i) in typeData" :key="i" :class="{ active: currentType == item.id }"
                        @click="changeMaterType(item.id)">{{ item.name }}</span>
                </div>
                <div class="select_date ">
                    <type-btn :typeData="dateTypeData" @clickDeviceType="selectDateType" />
                    <el-date-picker size="mini" @change="selectedDate" popper-class="select_panel" v-model="date"
                        :type="dateType" :value-format="showFormat" placeholder="请选择">
                    </el-date-picker>
                </div>
            </div>
        </div>
        <div class="bott">
            <div class="energy_container_left">
                <div class="tree_search">
                    <el-input v-model="keyWord" placeholder="请输入" popper-class="tree_search" @change="getDevicePage">
                        <template #suffix>
                            <i class="el-input__icon el-icon-search" />
                        </template>
                    </el-input>
                </div>
                <div class="type-list">
                    <el-scrollbar class="tree">
                        <el-tree ref="tree" :data="treeData" node-key="id" default-expand-all check-strictly show-checkbox
                            @node-click="clickOption" @check="checkOption" />
                    </el-scrollbar>
                </div>
            </div>
            <div class="energy_container_right">
                <div class="content_card_row">
                    <div class="card_chart">
                        <curve-chart :unitName="unitName" :chartData="analysisChartData" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import typeBtn from "@/components/energy/typeBtn.vue"
import dayjs from 'dayjs'
import card from "@/components/energy/card.vue"
import curveChart from "@/components/energy/curveChart.vue"
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    ref,
    toRefs,
    onMounted,
    
    computed,
    watch
} from 'vue'
import { useAppStore } from "@/stores/app"

export default {
    name: 'single',
    components: {
        typeBtn,
        curveChart,
        card
    },
    setup() {
        const api = inject('$api')
        const store = useAppStore()
        const tree = ref(null)
        const state = reactive({
            typeData: [{
                name: '电',
                id: 1
            },
            {
                name: '水',
                id: 2
            },
                // {
                //     name: '气',
                //     id: 3
                // }
            ],
            treeData: [],
            currentType: 1,
            keyWord: '',
            selectedMonth: '',
            dateTypeData: [{
                name: '年',
                id: 'Year'
            },
            {
                name: '月',
                id: 'Month'
            },
            {
                name: '日',
                id: 'Hour'
            }
            ],
            analysisDate: [],

            props: {
                label: 'name',
                value: 'id'
            },
            nodeId: null,
            dateType: 'year',
            showFormat: 'YYYY',
            analysisChartData: {
                xData: [],
                yData: []
            },
            date: '',
            timeTag: 'Year',
            unitName: 'kWh'
        })

        onMounted(() => {
            getDevicePage()
            state.selectedMonth = dayjs().format('YYYY-MM');
            selectDateType('Year')
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getDevicePage()
            }
        })
        const getDevicePage = () => {
            api.getEnergyDeviceTree({
                keyword: state.keyWord,
                projectId: getCookie("gh_projectId"),
                // type: state.currentType
            }).then(res => {
                state.treeData = res.data;
            });
        }

        const changeMaterType = (id) => {
            if (id == 2) {
                state.unitName = 't';
            } else {
                state.unitName = 'kWh';
            }
            state.nodeId = null;
            state.currentType = id
            getDevicePage();
            energyAnalysis();
        }

        const clickOption = (node, data) => {
            if (data.checked) {
                tree.value.setCheckedKeys([])
                setCurrentNode(null)
            } else {
                tree.value.setCheckedKeys([node.id])
                setCurrentNode(node.id)
            }
        }
        const checkOption = (node, data, type) => {
            if (data.checkedKeys.includes(node.id)) {
                tree.value.setCheckedKeys([node.id])
                setCurrentNode(node.id)
            } else {
                tree.value.setCheckedKeys([])
                setCurrentNode(null)
            }
        }
        const setCurrentNode = (id) => {
            state.nodeId = id;
            energyAnalysis();
        }
        const selectedDate = () => {
            switch (state.dateType) {
                case 'year':
                    state.analysisDate[0] = dayjs(state.date).startOf('year').format('YYYY-MM-DD 00:00:00');
                    state.analysisDate[1] = dayjs(state.date).endOf('year').format('YYYY-MM-DD 23:59:59');
                    break
                case 'month':
                    state.analysisDate[0] = dayjs(state.date).startOf('month').format('YYYY-MM-DD 00:00:00');
                    state.analysisDate[1] = dayjs(state.date).endOf('month').format('YYYY-MM-DD 23:59:59');
                    break
                case 'date':
                    state.analysisDate[0] = dayjs(state.date).format('YYYY-MM-DD 00:00:00');
                    state.analysisDate[1] = dayjs(state.date).format('YYYY-MM-DD 23:59:59');
                    break
            }
            energyAnalysis();
        }
        const energyAnalysis = () => {
            let type = "Year"
            if (state.timeTag == "Month") {
                type = "Day";
            } else if (state.timeTag == "Year") {
                type = 'Month'
            } else if (state.timeTag == "Hour") {
                type = "Hour"
            }

            api.singleEnergyAnalysis({
                bt: dayjs(state.analysisDate[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: dayjs(state.analysisDate[1]).format('YYYY-MM-DD HH:mm:ss'),
                deviceType: state.currentType,
                measurement: "device",
                timeTag: type,
                projectId: getCookie("gh_projectId"),
                id: state.nodeId
            }).then(res => {
                state.analysisChartData.xData = [], state.analysisChartData.yData = [];
                switch (state.dateType) {
                    case 'year':
                        // state.analysisChartData.yData = new Array(12);
                        let ydata = new Array(12).fill(0);
                        state.analysisChartData.xData = [dayjs(state.date).year() + '-01', dayjs(state.date).year() + '-02', dayjs(state.date).year() + '-03', dayjs(state.date).year() + '-04', dayjs(state.date).year() + '-05', dayjs(state.date).year() + '-06', dayjs(state.date).year() + '-07', dayjs(state.date).year() + '-08', dayjs(state.date).year() + '-9', dayjs(state.date).year() + '-10', dayjs(state.date).year() + '-11', dayjs(state.date).year() + '-12'];
                        if (res.data && res.data.length) {
                            res.data.forEach(item => {
                                ydata[dayjs(item.ts).month()] = item._aggregate;
                            })
                        }
                        state.analysisChartData.yData = ydata;
                        break
                    case 'month':

                        if (res.data && res.data.length) {
                            let ydata = new Array(dayjs().daysInMonth()).fill(0);
                            for (let i = 0; i < dayjs().daysInMonth(); i++) {
                                state.analysisChartData.xData.push(dayjs(state.date).format("YYYY-MM-") + (i + 1));
                            }
                            res.data.forEach(item => {
                                ydata[dayjs(item.ts).date() - 1] = item._aggregate;
                            })
                            state.analysisChartData.yData = ydata;
                        }else 
                        {
                            let ydata = new Array(dayjs().daysInMonth()).fill(0);
                            for (let i = 0; i < dayjs().daysInMonth(); i++) {
                                state.analysisChartData.xData.push(dayjs(state.date).format("YYYY-MM-") + (i + 1));
                            }
                            state.analysisChartData.yData = ydata;
                        }

                        break
                    case 'date':
                        if (res.data && res.data.length) {
                            let ydata = new Array(24).fill(0);
                            for (let i = 0; i < 24; i++) {
                                state.analysisChartData.xData.push(i + "时");
                            }
                            res.data.forEach(item => {
                                ydata[dayjs(item.ts).hour()] = item._aggregate;
                            })
                            state.analysisChartData.yData = ydata;
                        }else
                        {
                            let ydata = new Array(24).fill(0);
                            for (let i = 0; i < 24; i++) {
                                state.analysisChartData.xData.push(i + "时");
                            }
                            state.analysisChartData.yData = ydata;
                        }

                }
            })
        }
        const selectDateType = (data) => {
            state.timeTag = data;
            switch (data) {
                case 'Year':
                    state.dateType = 'year';
                    state.date = dayjs().format('YYYY');
                    state.showFormat = 'YYYY'
                    state.analysisDate[0] = dayjs(state.date).startOf('year').format('YYYY-MM-DD 00:00:00');
                    state.analysisDate[1] = dayjs(state.date).endOf('year').format('YYYY-MM-DD 23:59:59');
                    break
                case 'Month':
                    state.dateType = 'month';
                    state.date = dayjs().format('YYYY-MM');
                    state.showFormat = 'YYYY-MM'
                    state.analysisDate[0] = dayjs(state.date).startOf('month').format('YYYY-MM-DD 00:00:00');
                    state.analysisDate[1] = dayjs(state.date).endOf('month').format('YYYY-MM-DD 23:59:59');
                    break
                case 'Hour':
                    state.dateType = 'date';
                    state.date = dayjs().format('YYYY-MM-DD');
                    state.showFormat = 'YYYY-MM-DD'
                    state.analysisDate[0] = dayjs(state.date).format('YYYY-MM-DD 00:00:00');
                    state.analysisDate[1] = dayjs(state.date).format('YYYY-MM-DD 23:59:59');
                    break
            }
            energyAnalysis();
        }
        return {
            ...toRefs(state),
            tree,
            getDevicePage,
            changeMaterType,
            clickOption,
            checkOption,
            setCurrentNode,
            selectedDate,
            energyAnalysis,
            selectDateType,
            projectId
        }
    }
}
</script>

<style lang="scss" scoped>
.energy_container {
    // padding-top: 100px;
    // padding-bottom: 140px;
    width: 100%;

    .select_date {
        height: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .bott {
        display: flex;
        justify-content: space-between;
        padding: 15px 15px 0 15px;
        height: calc(100% - 100px);

        .energy_container_left {
            width: 377px;
            height: calc(100% - 50px);
            padding: 8px;
            border: 1px solid #2b2e32;
            overflow: hidden;

            .type-list {
                width: 100%;
                margin: 15px 0 0 0;
                height: calc(100% - 65px);
                overflow: auto;
            }
        }

        .energy_container_right {
            flex: 1;
            margin-left: 15px;

            .content_card_row {
                display: flex;
                width: 100%;
                height: 100%;
                justify-content: space-between;

                .card_chart {
                    width: 100%;
                    height: calc(100% - 36px);
                }
            }
        }
    }

    :deep(.el-input) {
        --el-input-bg-color: #021424 !important;
        --el-input-icon-color: #7A9BBD !important;
        --el-input-placeholder-color: #7A9BBD !important;
        --el-input-hover-border-color: #3E5B7B !important;
        --el-border-color: #3E5B7B !important;
        --el-color-primary: #3E5B7B !important;
    }

    :deep(.el-input__inner) {
        color: #7A9BBD;
        font-size: 16px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
        border: none !important;
    }
}
</style>
