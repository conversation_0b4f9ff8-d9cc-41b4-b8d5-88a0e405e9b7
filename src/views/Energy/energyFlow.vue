<template>
  <div class="energy_container">
    <div class="content_tab">
      <div class="left_tab">
        <span class="btn" v-for="(item, i) in dosageTab" :key="i" :class="{ active: dosageType == item.id }"
          @click="changeDosage(item.id)">{{ item.name }}</span>
      </div>
      <type-btn :typeData="deviceType" @clickDeviceType="handleDeviceType" />
    </div>
    <div class="container_content">
      <div class="container_left">
        <sub-title2 title="能流图"></sub-title2>
        <div class="card_chart">
          <sanChart :unitName="unitName" :nodes="nodes" :links="links"></sanChart>
        </div>
      </div>
      <div class="container_right">
        <div class="item_row">
          <sub-title2 title="能耗排行">
            <tabs :typeData="dateType" @clickDeviceType="changeDate" />
          </sub-title2>
          <el-form :inline="true" class="search_box">
            <el-form-item size="small" label="时间选择">
              <el-date-picker v-model="slectedDate" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" @change="changeDate">
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="card_body_content">
            <rankChart :unitName="unitName" :xData="rankData" :yData="rankText" color="#32EC7C" :barWidth="12" />
          </div>
        </div>
        <div class="item_row">
          <sub-title2 title="能耗分布"></sub-title2>
          <div class="distribution">
            <pieChart :yData="pieData" />
            <div class="content">
              <div class="tit">具体用量</div>
              <div v-for="(item, index) in pieData.slice(0, 4)" :key="index" class="list">
                {{ item.name }}
                <div class="val">{{ item.value }}{{ getUnitName() }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tabs from '@/components/energy/tabs.vue'
import typeBtn from '@/components/energy/typeBtn.vue'
import card from '@/components/energy/card.vue'
import rankChart from '@/components/energy/rankChart.vue'
import pieChart from '@/components/energy/pieChart.vue'
import sanChart from '@/components/energy/sanChart.vue'
import dayjs from 'dayjs'
import {
  reactive,
  toRefs,
  onMounted,
  getCurrentInstance
} from 'vue'
export default {
  name: 'energyFlow',
  components: {
    typeBtn,
    tabs,
    card,
    rankChart,
    pieChart,
    sanChart,
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const state = reactive({
      url: '',
      deviceType: [{
        name: '电',
        id: 1,
      },
      {
        name: '水',
        id: 2,
      },
        // {
        //   name: '气',
        //   id: 3,
        // },
      ],
      currentDeviceType: 1,
      dateType: [{
        name: '今天',
        id: 1,
      },
      {
        name: '昨天',
        id: 2,
      },
      {
        name: '本月',
        id: 3,
      },
      ],
      date: [],
      slectedDate: [],
      rankData: [],
      rankText: [],
      pieData: [],
      dosageTab: [{
        name: '能源分项',
        id: 'category',
      },
      {
        name: '建筑分区',
        id: 'area',
      },
      {
        name: '功能分区',
        id: 'feature',
      },
      ],
      dosageType: '',
      nodes: [],
      links: [],
      colors: [
        '#AC78CD',
        '#84D6B9',
        '#F27EB2',
        '#897CBD',
        '#6992C3',
        '#ADD76F',
        '#CB8762',
        '#FFE056',
        '#93E1ED',
        '#F27E7E',
      ],
      unitName: 'kwh',
    })
    onMounted(() => {
      state.dosageType = state.dosageTab[0].id
      state.slectedDate[0] = dayjs().format('YYYY-MM-DD 00:00:00')
      state.slectedDate[1] = dayjs().format('YYYY-MM-DD 23:59:59')
      handleDeviceType(1)
    })

    const handleDeviceType = (data) => {
      if (data == 2) {
        state.unitName = 't';

      }else{
        state.unitName = 'kwh';
      }
      state.currentDeviceType = data
      switch (data) {
        case 1:
          state.url =
            'http://' + process.env.NODE_ENV == 'development' ?
              window.DEV_CONFIG_API :
              window.PROD_CONFIG_API + '?diagramId=1415'
          break
        case 2:
          state.url =
            'http://' + process.env.NODE_ENV == 'development' ?
              window.DEV_CONFIG_API :
              window.PROD_CONFIG_API + '?diagramId=1415'
          break
        case 3:
          state.url =
            'http://' + process.env.NODE_ENV == 'development' ?
              window.DEV_CONFIG_API :
              window.PROD_CONFIG_API + '?diagramId=1415'
          break
      }
      getEnergyFlow()
    }
    const changeDosage = (id) => {
      state.dosageType = id
      switch (id) {
        case 'category':
          state.url =
            'http://' + process.env.NODE_ENV == 'development' ?
              window.DEV_CONFIG_API :
              window.PROD_CONFIG_API + '?diagramId=1415'
          break
        case 'feature':
          state.url =
            'http://' + process.env.NODE_ENV == 'development' ?
              window.DEV_CONFIG_API :
              window.PROD_CONFIG_API + '?diagramId=1415'
          break
        case 'area':
          state.url =
            'http://' + process.env.NODE_ENV == 'development' ?
              window.DEV_CONFIG_API :
              window.PROD_CONFIG_API + '?diagramId=1415'
          break
      }
      getEnergyFlow()
    }
    const changeDate = (data) => {
      switch (data) {
        case 1:
          state.slectedDate[0] = dayjs().format('YYYY-MM-DD 00:00:00')
          state.slectedDate[1] = dayjs().format('YYYY-MM-DD 23:59:59')
          break
        case 2:
          state.slectedDate[0] = dayjs()
            .add(-1, 'day')
            .format('YYYY-MM-DD 00:00:00')
          state.slectedDate[1] = dayjs()
            .add(-1, 'day')
            .format('YYYY-MM-DD 23:59:59')
          break
        case 3:
          state.slectedDate[0] = dayjs()
            .startOf('month')
            .format('YYYY-MM-DD 00:00:00')
          state.slectedDate[1] = dayjs()
            .endOf('month')
            .format('YYYY-MM-DD 23:59:59')
          break
      }
      getEnergyFlow()
    }
    const changeDateCom = () => {
      state.slectedDate[0] = dayjs().format(state.date[0] + ' 00:00:00')
      state.slectedDate[1] = dayjs().format(state.date[1] + ' 23:59:59')
      getEnergyFlow()
    }
    const getEnergyFlow = () => {
      const params = {
        bt: state.slectedDate[0],
        et: state.slectedDate[1],
        deviceType: state.currentDeviceType,
        measurement: state.dosageType,
      }
      api.energyFlow(params).then((res) => {
        (state.pieData = []);
        (state.rankData = []);
        (state.rankText = []);
        state.nodes = []
        state.links = []
        //是不是只有一级数据
        let exist = true
        let name = state.dosageType + 'name'

        res.data = res.data.filter(item => item.categoryname)

        for (let i = 0; i < res.data.length; i++) {
          if (res.data[i].code && res.data[i].code.split('|').length > 2) {
            exist = false
          }
        }
        if (exist) {
          state.nodes.push({
            name: '总能耗',
            itemStyle: {
              color: '#6FA8F7',
            },
          })
        }
        res.data.sort((a, b) => {
          return b._aggregate - a._aggregate
        })
        for (let i = 0; i < res.data.length; i++) {
          let element = res.data[i]
          let obj = {
            name: element[name],
            value: element._aggregate.toFixed(2),
          }
          // if ( (state.dosageType == 'area' && res.data[i][name].includes('/')) ||  state.dosageType != 'area' ) {
            state.rankData.push(element._aggregate.toFixed(2))
            state.rankText.push(element[name])
            state.pieData.push(obj)
          // }
          state.nodes.push({
            name: element[name],
            itemStyle: {
              color: state.colors[i % state.colors.length],
            },
          })

          if (exist) {
            state.links.push({
              value: element._aggregate.toFixed(2),
              source: '总能耗',
              target: element[name],
            })
          }
          if (element.code && !exist) {
            for (let i = 0; i < res.data.length; i++) {
              if (
                res.data[i].code != element.code &&
                res.data[i].code.startsWith(element.code + '|')
              ) {
                state.links.push({
                  value: res.data[i]._aggregate.toFixed(2),
                  source: element[name],
                  target: res.data[i][name],
                })
              }
            }
          }
        }
       
      })
    }
    const getUnitName = () => {
      switch (state.currentDeviceType) {
        case 1:
          return ' kWh'
        case 2:
          return ' t'
        case 3:
          return ' kwh'
      }
    }
    return {
      ...toRefs(state),
      handleDeviceType,
      changeDosage,
      changeDate,
      changeDateCom,
      getEnergyFlow,
      getUnitName,
    }
  },
}
</script>

<style lang="scss" scoped>
.energy_container {
  // padding-top: 70px;
  //   padding-bottom: 117px;
  //   width: 100%;
    

  .container_content {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 100px);
    padding: 0 15px;
  }

  .container_left {
    width: 60%;
    height: 100%;

    .card_chart {
      height: calc(100% - 28px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .container_right {
    width: calc(40% - 12px);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .item_row {
      width: 100%;
      height: 50%;

      .card_body_content {
        height: calc(100% - 116px);
        width: 100%;
      }

      .card_flooter {
        width: 100%;
        height: 40px;
        text-align: right;
        padding: 0 16px;

        .el-button {
          padding: 6px 16px;
          background: transparent;
          border: solid 1px #3e82d2;
          color: #3e82d2;
          border-radius: 12px;
        }
      }

      .distribution {
        width: 100%;
        display: flex;
        flex-direction: row;
        height: calc(100% - 28px);
        justify-content: space-around;
        align-items: center;

        .pie_chart,
        .content {
          flex: 1;
        }

        .content {
          font-size: 16px;
          font-family: "PingFangSC-Medium", "PingFang SC";
          font-weight: 500;
          color: #889cc3;
          padding: 0 50px;

          .tit {
            margin-bottom: 23px;
          }

          .list {
            display: flex;
            font-size: 14px;
            font-family: "PingFangSC-Regular", "PingFang SC";
            font-weight: 400;
            color: #778897;
            width: 300px;
            height: 40px;
            line-height: 40px;
            background: linear-gradient(270deg,
                rgba(29, 63, 79, 0.5) 0%,
                rgba(34, 43, 57, 0) 100%);
            margin-bottom: 10px;

            .val {
              font-size: 25px;
              font-family: "DINAlternate-Bold", "DINAlternate";
              font-weight: bold;
              color: #ffffff;
              margin-left: 15px;
            }
          }
        }
      }
    }
  }
}
</style>