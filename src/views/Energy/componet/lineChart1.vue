<template>
<div class="line_chart" ref="lineChartRef">
</div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,

    watch,
    reactive,
    toRefs,
    nextTick
} from 'vue'
export default {
    name: 'lineChart',
    props: ['data'],
    setup(props) {
        let echarts = inject('ec') //引入
        const lineChartRef = ref(null)
        const state = reactive({
            chart: null,
        });

        watch(props, val => {
            initChart();
        })
        onMounted(() => {
            nextTick(() => {
                // initChart()
            })
        })

        const initChart = () => {
            let option = {
                grid: {
                    top: '10px',
                    right: '5px',
                    left: '20px',
                    bottom: '10px',
                    containLabel: true,
                },
                xAxis: {
                    type: 'category',
                    data: (function () {
                        let date = [];
                        for (var i = 0; i < 24; i++) {
                            date.push(i + "时");
                        }
                        return date;
                    })(),

                    axisLabel: { //  改变x轴字体颜色和大小
                        // rotate: 45,
                        textStyle: {
                            color: "#9bc8ff",
                            fontSize: 12
                        }
                    },

                },
                yAxis: {
                    type: 'value',
                    axisLine: { //  改变y轴颜色
                        show: false,
                    },
                    axisLabel: { //  改变y轴字体颜色和大小
                        //formatter: '{value} m³ ', //  给y轴添加单位
                        textStyle: {
                            color: "#9bc8ff",
                            fontSize: 16
                        },
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#1f3097', //横向网格线颜色
                            width: 1,
                            type: 'dotted'
                        }
                    }
                },
                series: [{
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 7,
                    markPoint: {
                        symbol: "circle"
                    },
                    name: '项目1',
                    data: props.data,
                    itemStyle: {
                        normal: {
                            color: "#294E8F",
                            borderColor: "#3D7EEB",
                            borderWidth: 2
                        }
                    },
                    lineStyle: {
                        normal: {
                            width: 2,
                            color: "#327BFA",
                            shadowColor: "#327BFA",
                            shadowBlur: 10
                        }
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: '#3695FF' // 0% 处的颜色
                            }, {
                                offset: 0.5,
                                color: '#0c2645' // 100% 处的颜色
                            }],
                            global: false // 缺省为 false
                        }
                    }
                }]
            };
            let chart = echarts.init(lineChartRef.value); //定义
            chart.setOption(option, 500); //展示
            chart.resize(); //刷新画布

        }

        return {
            ...toRefs(state),
            lineChartRef
        }

    },

}
</script>

<style lang="scss">
.line_chart {
    width: 100%;
    height: 100%;
}
</style>
