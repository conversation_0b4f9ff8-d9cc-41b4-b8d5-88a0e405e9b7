<template>
<div class="line_chart" ref="lineChartRef">
1111
</div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,

    watch,
    reactive,
    toRefs,
    nextTick
} from 'vue'
export default {
    name: 'lineChart',
    props: ['data'],
    async setup(props) {
        let echarts = inject('ec') //引入
        const lineChartRef = ref(null)
        const state = reactive({
            chart: null,
        });
        window.onresize = function () {
            //state.chart.resize()
        }

        watch(() => props.data, val => {
            // if (state.chart) {
            //     let option = state.chart.getOption();
            //     if (option.series && option.series.length > 0) {
            //         option.series[0].data = val;
            //     }
            //     state.chart.setOption(option, true);
            // }
        })

        const getChartData = () => {
            state.chart.setOption({});
            state.chart.hideLoading();
            state.chart.resize(); //刷新画布
        }
        const resize = () => {
            if (state.chart) {
                state.chart.resize();
            }
        }
        const initChart = () => {
            let option = {
                grid: {
                    top: '10%',
                    right: '5%',
                    left: '15%',
                    bottom: '10%'
                },
                xAxis: {
                    type: 'category',
                    data: (function () {
                        let date = [];
                        for (var i = 0; i < 24; i++) {
                            date.push(i + "时");
                        }
                        return date;
                    })(),

                    axisLabel: { //  改变x轴字体颜色和大小
                        // rotate: 45,
                        textStyle: {
                            color: "#9bc8ff",
                            fontSize: 12
                        }
                    },

                },
                yAxis: {
                    type: 'value',
                    axisLine: { //  改变y轴颜色
                        show: false,
                    },
                    axisLabel: { //  改变y轴字体颜色和大小
                        //formatter: '{value} m³ ', //  给y轴添加单位
                        textStyle: {
                            color: "#9bc8ff",
                            fontSize: 16
                        },
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#1f3097', //横向网格线颜色
                            width: 1,
                            type: 'dotted'
                        }
                    }
                },
                series: [{
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 7,
                    markPoint: {
                        symbol: "circle"
                    },
                    name: '项目1',
                    data: [10, 20, 30, 60, 80, 100, 110, 100, 165, 166, 180, 190, 200, 250, 300, 400, 410, 300, 260, 250, 200, 190, 150, 100],
                    itemStyle: {
                        normal: {
                            color: "#294E8F",
                            borderColor: "#3D7EEB",
                            borderWidth: 2
                        }
                    },
                    lineStyle: {
                        normal: {
                            width: 2,
                            color: "#327BFA",
                            shadowColor: "#327BFA",
                            shadowBlur: 10
                        }
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: '#3695FF' // 0% 处的颜色
                            }, {
                                offset: 0.5,
                                color: '#0c2645' // 100% 处的颜色
                            }],
                            global: false // 缺省为 false
                        }
                    }
                }]
            };
            state.chart = echarts.init(lineChartRef.value); //定义
            state.chart.setOption(option); //展示
            state.chart.resize(); //刷新画布
            window.addEventListener('resize', () => {
                state.resize();
            })
        }
        await nextTick();
      //  initChart();
        // getChartData();

        return {
            ...toRefs(state),
            lineChartRef
        }

    },

}
</script>

<style lang="scss">
.line_chart {
    width: 100%;
    height: 400px;
}
</style>
