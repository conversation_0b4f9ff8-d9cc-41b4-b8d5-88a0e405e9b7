<template>
  <div class="meter">
    <div class="header">
      {{ data.name }}
    </div>
    <div class="content">
      <div class="top">
        <div class="img">
          <img :src="icon" alt="">
        </div>
        <div class="_right">
          <div class="tit" v-for="item in data.deviceStandards.slice(0, 4)" :key="item.id">{{ item.name }}:
            <span>{{ item.value }}{{ getUnitName(item&&item.deviceParams?item.deviceParams[0].unit:'') }}</span>
          </div>
        </div>
      </div>
      <div class="bott">
        <el-row gutter="10">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" v-for="(item,i) in data.deviceStandards" 
            :key="item.id" class="col">
            <div>{{ item.name }}:</div>
            <div> {{ item.value }}{{ getUnitName(item&&item.deviceParams?item.deviceParams[0].unit:'') }}</div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>

import { reactive, toRefs } from "vue";
import { inject, nextTick, onMounted } from 'vue';
export default {
  props: ['data', 'units'],
  sockets: {
    live(res) {
      this.subscribeData(res)
    },
    onVarsChangedCallback(res) {
      this.subscribeData(res)
    },
  },
  setup(props) {
    const state = reactive({
      icon: new URL("../@/assets/images/meter.jpg", import.meta.url).href,
      sockets: null,
      meter: {}
    });
    onMounted(() => {
      state.sockets = inject('socket')

    })

    const subscribeData = (res) => {
      if (res) {
        let data = JSON.parse(res)
        if (data.batchDefinitionId == 'real' && data.clientId == 'meter') {
          data.data.forEach(item => {
            state.meter[item.id] = Number(item.value).toFixed(2)
          })
        }
      }
    }

    const getUnitName = (value) => {
      let name = ''
      for (let i = 0; i < props.units.length; i++) {
        if (props.units[i].tagValue == value) {
          name = props.units[i].tagName
          break
        }
      }
      return name
    }


    return {
      ...toRefs(state),
      getUnitName,
      subscribeData
    };
  },
};
</script>
<style lang="scss" scoped>
.meter {
  padding: 10px;
  background: rgba(47, 54, 60, 0.3);
  border: 1px solid #4a5966;
  color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    font-family: "PingFangSC-Medium", "PingFang SC";
    font-weight: 500;

    .code {
      font-size: 14px;
      font-family: "PingFangSC-Regular", "PingFang SC";
      font-weight: 400;
      color: #9ca4b7;
    }
  }

  .footer {
    font-size: 12px;
    font-family: "PingFangSC-Regular", "PingFang SC";
    font-weight: 400;
    color: #9ca4b7;
  }

  .content {
    display: flex;
    flex-direction: column;
    margin: 10px 0;

    .top {
      flex: 1;
      display: flex;

      .img {
        width: 108px;
        height: 108px;
        background: rgba(47, 54, 60, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      ._right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: #889cc3;
        margin-left: 18px;

        .tit {
          font-size: 16px;
          font-family: "PingFangSC-Regular", "PingFang SC";
          font-weight: 400;
          line-height: 25px;

          span {
            color: #fff;
          }
        }
      }
    }

    .bott {
      margin-top: 18px;

      .meter_table {
        width: 100%;

        .t_header {
          height: 31px;
          background: rgba(68, 114, 141, 0.1);
          box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);
        }

        tr {
          line-height: 42px;

          .t {
            padding-left: 17px;
          }

          .center {
            text-align: center;
          }
        }
      }
    }
  }

  .col{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}</style>