<template>
<div class="chart" ref="myEcharts"></div>
</template>

<script>
import {ref,watch,onMounted,inject,nextTick} from 'vue'
export default {
    props: ['data', 'legend'],
    setup(props){
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        const initChart = () => {
            var dataStyle = {
                normal: {
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    },
                    shadowBlur: 0,
                    shadowColor: '#203665'
                }
            };
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '5%',
                    right: '5%',
                    left: '10%',
                    bottom: '10%'
                },
                xAxis: [{
                    type: 'category',
                    data:props.legend|| ['给水', '排水', '电梯', '热泵机组', '空调', '照明', '送风机'],
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,0.12)'
                        }
                    },
                    axisLabel: {
                        margin: 10,
                        color: '#9bc8ff',
                        textStyle: {
                            fontSize: 12
                        },
                    },
                }],
                yAxis: [{
                    axisLabel: {
                        formatter: '{value}',
                        color: '#9bc8ff',
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: 'rgba(255,255,255,1)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#1f3097', //横向网格线颜色
                            width: 1,
                            type: 'dotted'
                        }
                    }
                }],
                series: [{
                    type: 'bar',
                    data:props.data|| [5000, 2600, 1900, 1700, 1550, 1200, 1000],
                    barWidth: '10px',
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: '#71A4FB' // 0% 处的颜色
                            }, {
                                offset: 1,
                                color: '#3348FF' // 100% 处的颜色
                            }], false),
                            barBorderRadius: [30, 30, 30, 30],
                            shadowColor: 'rgba(0,160,221,1)',
                            shadowBlur: 4,
                        }
                    },

                }]
            };
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }

        return {
            myEcharts
        }

    }
   
   
   

};
</script>

<style lang="scss">
.chart {
    width: 100%;
    height: 100%;
}
</style>
