<template>
    <div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: ['data'],
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        const initChart = () => {
            var color = ['#fb734e', '#e32f46', '#94d96c', '#0bbcb7', '#1a9bfc', '#7049f0'];
            var dataStyle = {
                normal: {
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    },
                    shadowBlur: 40,
                    borderWidth: 10,
                    shadowColor: 'rgba(0, 0, 0, 0)' //边框阴影
                }
            };
            var placeHolderStyle = {
                normal: {
                    color: '#393d50',
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    }
                },
                emphasis: {
                    color: '#393d50'
                }
            };
            var option = {
                title: {
                    text: '',
                    x: 'center',
                    y: 'center',
                    textStyle: {
                        fontWeight: 'normal',
                        fontSize: 24,
                        color: "#fff",
                    }
                },
                tooltip: {
                    trigger: 'item',
                    show: true,
                    formatter: "{b} : <br/>{d}%",
                    backgroundColor: 'rgba(0,0,0,0.7)', // 背景
                    padding: [8, 10], //内边距
                    extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.4);', //添加阴影
                },
                series: [{
                    name: '故障',
                    type: 'pie',
                    clockWise: false,
                    radius: ['90%', '100%'],
                    center: ['50%', '50%'],
                    itemStyle: dataStyle,
                    hoverAnimation: false,
                    startAngle: 90,
                    label: {
                        borderRadius: '10',
                    },
                    data: [{
                        value: props.data.normalCount || 54.6,
                        name: '故障',
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: color[0]
                                }, {
                                    offset: 1,
                                    color: color[1]
                                }])
                            }
                        }
                    },
                    {
                        value: props.data.count - props.data.normalCount || 45.4,
                        name: '',
                        tooltip: {
                            show: false
                        },
                        itemStyle: placeHolderStyle
                    },
                    ]
                },
                {
                    name: '运行',
                    type: 'pie',
                    clockWise: false,
                    radius: ['70%', '80%'],
                    center: ['50%', '50%'],
                    itemStyle: dataStyle,
                    hoverAnimation: false,
                    startAngle: 90,
                    data: [{
                        value: props.data.statusCount || 56.7,
                        name: '运行',
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: color[2]
                                }, {
                                    offset: 1,
                                    color: color[3]
                                }])
                            }
                        }
                    },
                    {
                        value: props.data.count - props.data.statusCount || 43.3,
                        name: '',
                        tooltip: {
                            show: false
                        },
                        itemStyle: placeHolderStyle
                    },
                    ]
                },
                {
                    name: '总数',
                    type: 'pie',
                    clockWise: false,
                    radius: ['50%', '60%'],
                    center: ['50%', '50%'],
                    itemStyle: dataStyle,
                    hoverAnimation: false,
                    startAngle: 90,
                    data: [{
                        value: props.data.count || 30,
                        name: '总数',
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: color[4]
                                }, {
                                    offset: 1,
                                    color: color[5]
                                }]),
                            }
                        }
                    },
                    {
                        value: 0,
                        name: '',
                        tooltip: {
                            show: false
                        },
                        itemStyle: placeHolderStyle
                    },
                    ]
                }
                ]
            };
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 100%;
    width: 100%;
}
</style>
