<template>
<div class="chart" ref="myEcharts"></div>
</template>

    
    
<script>
import {
    ref,
    watch,
    onMounted,
    inject,
    nextTick
} from 'vue'
export default {
    props: ['data', 'legend'],
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        const initChart = () => {
            let option = {
                grid: {
                    top: '10%',
                    right: '5%',
                    left: '15%',
                    bottom: '10%'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: (function () {
                        let date = [];
                        for (var i = 0; i < 24; i++) {
                            date.push(i + "时");
                        }
                        return date;
                    })(),

                    axisLabel: { //  改变x轴字体颜色和大小
                        // rotate: 45,
                        textStyle: {
                            color: "#9bc8ff",
                            fontSize: 12
                        }
                    },

                },
                yAxis: {
                    type: 'value',
                    axisLine: { //  改变y轴颜色
                        show: false,
                    },
                    axisLabel: { //  改变y轴字体颜色和大小
                        //formatter: '{value} m³ ', //  给y轴添加单位
                        textStyle: {
                            color: "#9bc8ff",
                            fontSize: 16
                        },
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#1f3097', //横向网格线颜色
                            width: 1,
                            type: 'dotted'
                        }
                    }
                },
                series: [{
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 7,
                    markPoint: {
                        symbol: "circle"
                    },
                    name: '项目1',
                    data:props.data|| [10, 20, 30, 60, 80, 100, 110, 100, 165, 166, 180, 190, 200, 250, 300, 400, 410, 300, 260, 250, 200, 190, 150, 100],
                    itemStyle: {
                        normal: {
                            color: "#294E8F",
                            borderColor: "#3D7EEB",
                            borderWidth: 2
                        }
                    },
                    lineStyle: {
                        normal: {
                            width: 2,
                            color: "#327BFA",
                            shadowColor: "#327BFA",
                            shadowBlur: 10
                        }
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: '#3695FF' // 0% 处的颜色
                            }, {
                                offset: 0.5,
                                color: '#0c2645' // 100% 处的颜色
                            }],
                            global: false // 缺省为 false
                        }
                    }
                }]
            };
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }

        return {
            myEcharts
        }

    }

};
</script>
    
    
<style lang="scss">
.chart {
    width: 100%;
    height: 100%;
}
</style>
