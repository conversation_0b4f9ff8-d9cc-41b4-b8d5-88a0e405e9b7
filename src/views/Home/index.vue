<template>
    <div class="">
        <div class="wrapper_left">
            <!-- 新风系统 - 优化版环形进度展示 -->
            <div class="item">
                <sub-title title='新风系统' subTitle='Fresh Air System' />
                <div class="item-body fresh-air-system">
                    <div class="system-overview">
                        <div class="circle-progress-enhanced">
                            <div class="circle-chart-enhanced">
                                <!-- 旋转的风扇背景 -->
                                <div class="fan-animation">
                                    <div class="fan-blade"></div>
                                    <div class="fan-blade"></div>
                                    <div class="fan-blade"></div>
                                    <div class="fan-center"></div>
                                </div>
                                <!-- 进度环 -->
                                <div class="progress-ring">
                                    <svg class="progress-svg" viewBox="0 0 100 100">
                                        <defs>
                                            <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style="stop-color:#2FE5E7;stop-opacity:1" />
                                                <stop offset="50%" style="stop-color:#7BF959;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#2FE5E7;stop-opacity:1" />
                                            </linearGradient>
                                        </defs>
                                        <circle class="progress-bg" cx="50" cy="50" r="45" />
                                        <circle class="progress-fill" cx="50" cy="50" r="45" 
                                                :style="{ strokeDasharray: `${(freshAirSystem.online + freshAirSystem.running) / (freshAirSystem.online + freshAirSystem.running + freshAirSystem.stopped + freshAirSystem.offline) * 283} 283` }" />
                                    </svg>
                                </div>
                                <!-- 中心内容 -->
                                <div class="circle-inner-enhanced">
                                    <div class="circle-value-enhanced">{{ freshAirSystem.online + freshAirSystem.running }}</div>
                                    <div class="circle-label-enhanced">运行中</div>
                                    <div class="efficiency-indicator">
                                        <span class="efficiency-text">效率</span>
                                        <span class="efficiency-value">96%</span>
                                    </div>
                                </div>
                                <!-- 能耗粒子效果 -->
                                <div class="air-particles">
                                    <div class="particle"></div>
                                    <div class="particle"></div>
                                    <div class="particle"></div>
                                    <div class="particle"></div>
                                    <div class="particle"></div>
                                </div>
                            </div>
                        </div>
                        <div class="status-grid-enhanced">
                            <div class="status-card online">
                                <div class="card-background"></div>
                                <div class="card-content">
                                    <div class="status-icon">
                                        <i class="iconfont icon-online"></i>
                                        <div class="icon-pulse online-pulse"></div>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-value">{{ freshAirSystem.online }}</div>
                                        <div class="status-label">在线设备</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="status-card running">
                                <div class="card-background"></div>
                                <div class="card-content">
                                    <div class="status-icon">
                                        <i class="iconfont icon-on"></i>
                                        <div class="icon-pulse running-pulse"></div>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-value">{{ freshAirSystem.running }}</div>
                                        <div class="status-label">运行中</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="status-card stopped">
                                <div class="card-background"></div>
                                <div class="card-content">
                                    <div class="status-icon">
                                        <i class="iconfont icon-off"></i>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-value">{{ freshAirSystem.stopped }}</div>
                                        <div class="status-label">停止</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="status-card offline">
                                <div class="card-background"></div>
                                <div class="card-content">
                                    <div class="status-icon">
                                        <i class="iconfont icon-offline"></i>
                                        <div class="icon-pulse offline-pulse"></div>
                                    </div>
                                    <div class="status-info">
                                        <div class="status-value">{{ freshAirSystem.offline }}</div>
                                        <div class="status-label">离线设备</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排风系统 - 管道流动版 -->
            <div class="item">
                <sub-title title='排风系统' subTitle='Exhaust System' />
                <div class="item-body exhaust-system-v2">
                    <div class="exhaust-overview">
                        <!-- 系统效率指标 -->
                        <div class="efficiency-metric">
                            <div class="efficiency-display">
                                <div class="efficiency-circle">
                                    <div class="efficiency-value">{{ Math.round((exhaustSystem.online / (exhaustSystem.online + exhaustSystem.offline)) * 100) }}%</div>
                                    <div class="efficiency-label">运行效率</div>
                                </div>
                                <div class="efficiency-indicator">
                                    <div class="indicator-ring"></div>
                                    <div class="indicator-pulse"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 设备状态展示 -->
                        <div class="device-status-section">
                            <div class="status-header">
                                <div class="header-icon">
                                    <i class="iconfont icon-efficiency"></i>
                                </div>
                                <div class="header-info">
                                    <div class="total-devices">{{ exhaustSystem.online + exhaustSystem.offline }}</div>
                                    <div class="header-label">排风设备</div>
                                </div>
                            </div>
                            
                            <div class="status-cards">
                                <div class="status-card online-card">
                                    <div class="card-icon">
                                        <i class="iconfont icon-device-online"></i>
                                        <div class="icon-pulse online-pulse"></div>
                                    </div>
                                    <div class="card-content">
                                        <div class="card-value">{{ exhaustSystem.online }}</div>
                                        <div class="card-label">在线设备</div>
                                    </div>
                                    <div class="card-progress">
                                        <div class="progress-fill online-progress" 
                                             :style="{ width: `${(exhaustSystem.online / (exhaustSystem.online + exhaustSystem.offline)) * 100}%` }">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="status-card offline-card">
                                    <div class="card-icon">
                                        <i class="iconfont icon-device-offline"></i>
                                        <div class="icon-pulse offline-pulse"></div>
                                    </div>
                                    <div class="card-content">
                                        <div class="card-value">{{ exhaustSystem.offline }}</div>
                                        <div class="card-label">离线设备</div>
                                    </div>
                                    <div class="card-progress">
                                        <div class="progress-fill offline-progress" 
                                             :style="{ width: `${(exhaustSystem.offline / (exhaustSystem.online + exhaustSystem.offline)) * 100}%` }">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空气质量系统 - 空气质量指数 -->
            <div class="item">
                <sub-title title='空气质量系统' subTitle='Air Quality System' />
                <div class="item-body air-quality-system">
                    <div class="air-quality-display">
                        <div class="aqi-main">
                            <div class="aqi-circle excellent">
                                <div class="aqi-number">28</div>
                                <div class="aqi-text">优</div>
                            </div>
                            <div class="air-status">
                                <div class="status-text">空气质量优</div>
                                <div class="status-desc">适宜各类人群活动</div>
                            </div>
                        </div>
                        <div class="pollutant-bars">
                            <div class="pollutant-item">
                                <div class="pollutant-info">
                                    <span class="pollutant-name">甲醛</span>
                                    <span class="pollutant-value">{{ airQualitySystem.formaldehyde }}</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill excellent"
                                        :style="{ width: `${(airQualitySystem.formaldehyde / 0.1) * 100}%` }"></div>
                                </div>
                            </div>
                            <div class="pollutant-item">
                                <div class="pollutant-info">
                                    <span class="pollutant-name">TVOC</span>
                                    <span class="pollutant-value">{{ airQualitySystem.tvoc }}</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill good"
                                        :style="{ width: `${(airQualitySystem.tvoc / 1) * 100}%` }"></div>
                                </div>
                            </div>
                            <div class="pollutant-item">
                                <div class="pollutant-info">
                                    <span class="pollutant-name">CO₂</span>
                                    <span class="pollutant-value">{{ airQualitySystem.co2 }}</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill moderate"
                                        :style="{ width: `${(airQualitySystem.co2 / 1000) * 100}%` }"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 超低温系统 - 冷库监控 -->
            <div class="item" style="margin-bottom: 0;">
                <sub-title title='超低温系统' subTitle='Ultra Low Temperature System' />
                <div class="item-body ultra-temp-system">
                    <div class="freezer-monitor">
                        <div class="freezer-container">
                            <div class="freezer-box">
                                <div class="cooling-waves">
                                    <div class="wave wave-1"></div>
                                    <div class="wave wave-2"></div>
                                    <div class="wave wave-3"></div>
                                </div>
                                <div class="temp-sensor">
                                    <i class="iconfont icon-temp-sensor"></i>
                                </div>
                                <div class="temp-reading">
                                    <div class="temp-value">{{ ultraLowTempSystem.avgTemperature }}°C</div>
                                </div>
                            </div>
                            <div class="freezer-status">
                                <div class="status-led active"></div>
                                <span class="status-text">制冷中</span>
                            </div>
                        </div>
                        <div class="environment-info">
                            <div class="info-card humidity-card">
                                <div class="card-header">
                                    <i class="iconfont icon-humidity"></i>
                                    <span>湿度</span>
                                </div>
                                <div class="card-value">{{ ultraLowTempSystem.avgHumidity }}%</div>
                                <div class="humidity-bar">
                                    <div class="humidity-fill" :style="{ width: `${ultraLowTempSystem.avgHumidity}%` }">
                                    </div>
                                </div>
                            </div>
                            <div class="info-card energy-card">
                                <div class="card-header">
                                    <i class="iconfont icon-energy"></i>
                                    <span>功耗</span>
                                </div>
                                <div class="card-value">2.8kW</div>
                                <div class="energy-indicator normal"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="wrapper_right">
            <!-- 能耗系统 - 优化版能耗看板 -->
            <div class="item">
                <sub-title title='能耗系统' subTitle='Energy Consumption System' />
                <div class="item-body energy-consumption">
                    <div class="energy-dashboard">
                        <!-- 主要指标卡片 -->
                        <div class="main-metrics">
                            <div class="metric-card primary-card">
                                <div class="card-header">
                                    <div class="metric-icon daily-icon">
                                        <i class="iconfont icon-daily"></i>
                                        <div class="icon-glow"></div>
                                    </div>
                                    <div class="metric-trend">
                                        <div class="trend-arrow up">↗</div>
                                        <span class="trend-percent">+5.2%</span>
                                    </div>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value">{{ energySystem.dailyConsumption }}</div>
                                    <div class="metric-label">今日用电</div>
                                    <div class="metric-unit">kWh</div>
                                </div>
                                <div class="card-decoration"></div>
                            </div>
                            
                            <div class="metric-card special-card">
                                <div class="card-header">
                                    <div class="metric-icon savings-icon">
                                        <i class="iconfont icon-savings"></i>
                                        <div class="icon-pulse"></div>
                                    </div>
                                    <div class="savings-badge">节能</div>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value savings-value">{{ energySystem.totalSavings }}</div>
                                    <div class="metric-label">累计节能</div>
                                    <div class="metric-unit">kWh</div>
                                </div>
                                <div class="energy-particles">
                                    <div class="particle"></div>
                                    <div class="particle"></div>
                                    <div class="particle"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 次要指标 -->
                        <div class="secondary-metrics">
                            <div class="metric-row">
                                <div class="mini-card monthly-card">
                                    <div class="mini-icon">
                                        <i class="iconfont icon-monthly"></i>
                                    </div>
                                    <div class="mini-content">
                                        <div class="mini-value">{{ (energySystem.monthlyConsumption / 1000).toFixed(1) }}K</div>
                                        <div class="mini-label">本月</div>
                                    </div>
                                    <div class="mini-progress">
                                        <div class="progress-fill" style="width: 68%"></div>
                                    </div>
                                </div>
                                
                                <div class="mini-card yearly-card">
                                    <div class="mini-icon">
                                        <i class="iconfont icon-yearly"></i>
                                    </div>
                                    <div class="mini-content">
                                        <div class="mini-value">{{ (energySystem.yearlyConsumption / 1000).toFixed(0) }}K</div>
                                        <div class="mini-label">本年</div>
                                    </div>
                                    <div class="mini-progress">
                                        <div class="progress-fill yearly" style="width: 82%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 门禁系统 - 门状态显示 -->
            <div class="item">
                <sub-title title='门禁系统' subTitle='Access Control System' />
                <div class="item-body access-control">
                    <div class="door-status">
                        <div class="door-visual">
                            <div class="door-frame">
                                <div class="door-panel"></div>
                                <div class="door-handle"></div>
                                <div class="access-indicator online"></div>
                            </div>
                        </div>
                        <div class="door-stats">
                            <div class="stat-row">
                                <div class="stat-item">
                                    <span class="stat-label">在线设备</span>
                                    <span class="stat-value online">{{ accessControlSystem.online }}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">离线设备</span>
                                    <span class="stat-value offline">{{ accessControlSystem.offline }}</span>
                                </div>
                            </div>
                            <div class="total-devices">
                                总计: {{ accessControlSystem.online + accessControlSystem.offline }} 台
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频监控系统 - 监控网格 -->
            <div class="item">
                <sub-title title='视频监控' subTitle='Video Surveillance System' />
                <div class="item-body video-surveillance">
                    <div class="camera-grid">
                        <div class="camera-overview">
                            <div class="camera-total">
                                <div class="total-number">{{ videoSystem.online + videoSystem.offline }}</div>
                                <span class="total-label">摄像头总数</span>
                            </div>
                            <div class="camera-icon">
                                <i class="iconfont iconshexiangji"></i>
                                <div class="recording-dot"></div>
                            </div>
                        </div>
                        <div class="camera-stats">
                            <div class="stat-grid">
                                <div class="stat-box online">
                                    <div class="stat-number">{{ videoSystem.online }}</div>
                                    <div class="stat-text">在线摄像头</div>
                                </div>
                                <div class="stat-box offline">
                                    <div class="stat-number">{{ videoSystem.offline }}</div>
                                    <div class="stat-text">离线摄像头</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 漏水检测系统 - 水滴动画 -->
            <div class="item" style="margin-bottom: 0;">
                <sub-title title='漏水检测' subTitle='Water Leak Detection System' />
                <div class="item-body water-leak-system">
                    <div class="leak-detection">
                        <div class="water-visual">
                            <div class="water-container">
                                <div class="water-drop normal"></div>
                                <div class="water-level"></div>
                                <div class="ripple-effect"></div>
                            </div>
                            <div class="detection-sensors">
                                <div class="sensor active"></div>
                                <div class="sensor active"></div>
                                <div class="sensor warning"></div>
                            </div>
                        </div>
                        <div class="leak-stats">
                            <div class="status-indicator normal">
                                <div class="indicator-dot"></div>
                                <span class="indicator-text">正常区域</span>
                                <span class="indicator-count">{{ waterLeakSystem.normal }}</span>
                            </div>
                            <div class="status-indicator alarm">
                                <div class="indicator-dot"></div>
                                <span class="indicator-text">报警区域</span>
                                <span class="indicator-count">{{ waterLeakSystem.alarm }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import energySub from '@/components/home/<USER>';
import Alarm from '@/components/echarts/weekAlarmEchart.vue'
import LifecycleRadarChart from '@/components/echarts/lifecycleRadarChart.vue';
import PieChart from '@/components/echarts/pieChart.vue';

import {
    getCookie,
} from '@/utils/cookie';
import dayjs from 'dayjs';
import { useAppStore } from '@/stores/app';

export default defineComponent({
    name: "home",
    components: {
        energySub,
        Alarm,
        LifecycleRadarChart,
        PieChart
    },
    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            // 新风系统数据
            freshAirSystem: {
                online: 12,
                running: 8,
                stopped: 3,
                offline: 1
            },

            // 排风系统数据
            exhaustSystem: {
                online: 15,
                offline: 2
            },

            // 空气质量系统数据
            airQualitySystem: {
                formaldehyde: 0.05,
                tvoc: 0.32,
                co2: 680
            },

            // 超低温系统数据
            ultraLowTempSystem: {
                avgTemperature: -18.5,
                avgHumidity: 25
            },

            // 能耗系统数据
            energySystem: {
                dailyConsumption: 1250.5,
                monthlyConsumption: 38420.8,
                yearlyConsumption: 456780.2,
                totalSavings: 15680.3
            },

            // 门禁系统数据
            accessControlSystem: {
                online: 28,
                offline: 2
            },

            // 视频监控系统数据
            videoSystem: {
                online: 45,
                offline: 3
            },

            // 漏水检测系统数据
            waterLeakSystem: {
                normal: 24,
                alarm: 1
            }
        })

        onMounted(() => {
            // 这里可以添加API调用来获取真实数据
            // loadSystemData();
        })

        const loadSystemData = () => {
            // 新风系统数据获取
            // getFreshAirSystemData();

            // 排风系统数据获取
            // getExhaustSystemData();

            // 空气质量数据获取
            // getAirQualityData();

            // 超低温系统数据获取
            // getUltraLowTempData();

            // 能耗系统数据获取
            // getEnergySystemData();

            // 门禁系统数据获取
            // getAccessControlData();

            // 视频监控数据获取
            // getVideoSystemData();

            // 漏水检测数据获取
            // getWaterLeakData();
        }

        return {
            ...toRefs(state),
        }
    }
});
</script>

<style lang="scss" scoped>
.z100 {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 100;

    .wrapper_left,
    .wrapper_right {
        display: flex;
        flex-direction: column;
        width: 393px;
        height: 100vh;
        z-index: 198;
        position: absolute;
        padding: 60px 0 20px 0;
        box-sizing: border-box;
        overflow: hidden;
    }

    .item {
        flex: 1;
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;

        &:last-child {
            margin-bottom: 0;
        }

        &-body {
            display: flex;
            flex-direction: column;
            padding: 8px;
            color: #fff;
            background: url("@/assets/images/dialog/panel_bg.png") no-repeat;
            background-size: 100% 100%;
            flex: 1;
            min-height: 0;
        }
    }

    .wrapper_left {

        // 新风系统 - 优化版环形进度样式
        .fresh-air-system {
            height: 100%;
            display: flex;
            flex-direction: column;

            .system-overview {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 8px;
                gap: 12px;
            }

            // 增强版圆形进度
            .circle-progress-enhanced {
                width: 90px;
                height: 90px;
                flex-shrink: 0;
                position: relative;
            }

            .circle-chart-enhanced {
                width: 100%;
                height: 100%;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            // 风扇动画
            .fan-animation {
                position: absolute;
                width: 70px;
                height: 70px;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                animation: rotate-fan 8s linear infinite;
                opacity: 0.3;
                z-index: 1;
            }

            .fan-blade {
                position: absolute;
                width: 30px;
                height: 6px;
                background: linear-gradient(90deg, transparent, #2FE5E7, transparent);
                border-radius: 3px;
                top: 50%;
                left: 50%;
                transform-origin: 0 50%;

                &:nth-child(1) {
                    transform: translate(-50%, -50%) rotate(0deg);
                }
                &:nth-child(2) {
                    transform: translate(-50%, -50%) rotate(120deg);
                }
                &:nth-child(3) {
                    transform: translate(-50%, -50%) rotate(240deg);
                }
            }

            .fan-center {
                position: absolute;
                width: 8px;
                height: 8px;
                background: #2FE5E7;
                border-radius: 50%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                box-shadow: 0 0 8px #2FE5E7;
            }

            // 进度环
            .progress-ring {
                position: absolute;
                width: 85px;
                height: 85px;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 2;
            }

            .progress-svg {
                width: 100%;
                height: 100%;
                transform: rotate(-90deg);
            }

            .progress-bg {
                fill: none;
                stroke: rgba(47, 229, 231, 0.1);
                stroke-width: 3;
                stroke-linecap: round;
            }

            .progress-fill {
                fill: none;
                stroke: url(#progressGradient);
                stroke-width: 3;
                stroke-linecap: round;
                stroke-dasharray: 283;
                stroke-dashoffset: 0;
                transition: stroke-dasharray 1s ease-in-out;
                filter: drop-shadow(0 0 5px #2FE5E7);
            }

            // 中心内容
            .circle-inner-enhanced {
                position: relative;
                z-index: 3;
                text-align: center;
                background: rgba(16, 42, 67, 0.9);
                border-radius: 50%;
                width: 65px;
                height: 65px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border: 2px solid rgba(47, 229, 231, 0.3);
                backdrop-filter: blur(10px);
            }

            .circle-value-enhanced {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                color: #2FE5E7;
                font-weight: bold;
                text-shadow: 0 0 8px rgba(47, 229, 231, 0.8);
                line-height: 1;
            }

            .circle-label-enhanced {
                font-size: 8px;
                color: #C3D2E0;
                margin-top: 1px;
                line-height: 1;
            }

            .efficiency-indicator {
                margin-top: 2px;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .efficiency-text {
                font-size: 6px;
                color: #7BF959;
                line-height: 1;
            }

            .efficiency-value {
                font-size: 8px;
                color: #7BF959;
                font-family: "BEBAS", sans-serif;
                font-weight: bold;
                line-height: 1;
            }

            // 空气粒子效果
            .air-particles {
                position: absolute;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1;

                .particle {
                    position: absolute;
                    width: 3px;
                    height: 3px;
                    background: #2FE5E7;
                    border-radius: 50%;
                    opacity: 0.6;
                    animation: float-air-particle 3s infinite ease-in-out;

                    &:nth-child(1) {
                        top: 15%;
                        left: 25%;
                        animation-delay: 0s;
                        animation-duration: 2.5s;
                    }
                    &:nth-child(2) {
                        top: 35%;
                        right: 20%;
                        animation-delay: 0.5s;
                        animation-duration: 3s;
                    }
                    &:nth-child(3) {
                        bottom: 25%;
                        left: 30%;
                        animation-delay: 1s;
                        animation-duration: 2.8s;
                    }
                    &:nth-child(4) {
                        top: 60%;
                        right: 35%;
                        animation-delay: 1.5s;
                        animation-duration: 3.2s;
                    }
                    &:nth-child(5) {
                        bottom: 45%;
                        left: 60%;
                        animation-delay: 2s;
                        animation-duration: 2.7s;
                    }
                }
            }

            // 增强版状态网格
            .status-grid-enhanced {
                flex: 1;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 6px;
            }

            .status-card {
                position: relative;
                border-radius: 8px;
                overflow: hidden;
                transition: all 0.3s ease;
                cursor: pointer;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                }

                &.online {
                    border: 1px solid rgba(47, 229, 231, 0.3);
                    &:hover {
                        border-color: rgba(47, 229, 231, 0.6);
                        box-shadow: 0 8px 25px rgba(47, 229, 231, 0.2);
                    }
                }

                &.running {
                    border: 1px solid rgba(123, 249, 89, 0.3);
                    &:hover {
                        border-color: rgba(123, 249, 89, 0.6);
                        box-shadow: 0 8px 25px rgba(123, 249, 89, 0.2);
                    }
                }

                &.stopped {
                    border: 1px solid rgba(243, 243, 243, 0.2);
                    &:hover {
                        border-color: rgba(243, 243, 243, 0.4);
                        box-shadow: 0 8px 25px rgba(243, 243, 243, 0.1);
                    }
                }

                &.offline {
                    border: 1px solid rgba(252, 68, 68, 0.3);
                    &:hover {
                        border-color: rgba(252, 68, 68, 0.6);
                        box-shadow: 0 8px 25px rgba(252, 68, 68, 0.2);
                    }
                }
            }

            .card-background {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(16, 42, 67, 0.4);
                backdrop-filter: blur(10px);
                
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
                    pointer-events: none;
                }
            }

            .card-content {
                position: relative;
                z-index: 2;
                padding: 8px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .status-icon {
                position: relative;
                width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: rgba(16, 42, 67, 0.6);
                border: 1px solid rgba(255, 255, 255, 0.1);

                .iconfont {
                    font-size: 14px;
                    z-index: 2;
                    position: relative;
                }

                .online & .iconfont {
                    color: #2FE5E7;
                }
                .running & .iconfont {
                    color: #7BF959;
                }
                .stopped & .iconfont {
                    color: #F3F3F3;
                }
                .offline & .iconfont {
                    color: #FC4444;
                }
            }

            .icon-pulse {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 35px;
                height: 35px;
                border-radius: 50%;
                pointer-events: none;

                &.online-pulse {
                    background: radial-gradient(circle, rgba(47, 229, 231, 0.3) 0%, transparent 70%);
                    animation: status-pulse 2s infinite;
                }

                &.running-pulse {
                    background: radial-gradient(circle, rgba(123, 249, 89, 0.3) 0%, transparent 70%);
                    animation: status-pulse 2s infinite;
                }

                &.offline-pulse {
                    background: radial-gradient(circle, rgba(252, 68, 68, 0.3) 0%, transparent 70%);
                    animation: status-pulse 2s infinite;
                }
            }

            .status-info {
                flex: 1;
                text-align: left;
            }

            .status-value {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                font-weight: bold;
                line-height: 1;
                margin-bottom: 2px;

                .online & {
                    color: #2FE5E7;
                    text-shadow: 0 0 8px rgba(47, 229, 231, 0.6);
                }
                .running & {
                    color: #7BF959;
                    text-shadow: 0 0 8px rgba(123, 249, 89, 0.6);
                }
                .stopped & {
                    color: #F3F3F3;
                    text-shadow: 0 0 8px rgba(243, 243, 243, 0.3);
                }
                .offline & {
                    color: #FC4444;
                    text-shadow: 0 0 8px rgba(252, 68, 68, 0.6);
                }
            }

            .status-label {
                font-size: 9px;
                color: #C3D2E0;
                font-weight: 500;
                opacity: 0.9;
            }
        }

        // 排风系统 - 简洁卡片版样式
        .exhaust-system-v2 {
            height: 100%;
            display: flex;
            flex-direction: column;

            .exhaust-overview {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 8px;
                gap: 12px;
            }

            // 效率指标显示
            .efficiency-metric {
                flex-shrink: 0;
            }

            .efficiency-display {
                position: relative;
                width: 70px;
                height: 70px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .efficiency-circle {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(16, 42, 67, 0.8) 0%, rgba(47, 229, 231, 0.1) 70%);
                border: 2px solid rgba(47, 229, 231, 0.6);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                z-index: 2;
                box-shadow: 0 0 15px rgba(47, 229, 231, 0.3);
            }

            .efficiency-value {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                color: #2FE5E7;
                font-weight: bold;
                line-height: 1;
                text-shadow: 0 0 8px rgba(47, 229, 231, 0.6);
            }

            .efficiency-label {
                font-size: 8px;
                color: #C3D2E0;
                margin-top: 2px;
                text-align: center;
            }

            .efficiency-indicator {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
            }

            .indicator-ring {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border: 2px solid transparent;
                border-radius: 50%;
                border-top-color: rgba(123, 249, 89, 0.8);
                animation: rotate-ring 3s linear infinite;
            }

            .indicator-pulse {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 75px;
                height: 75px;
                border: 1px solid rgba(47, 229, 231, 0.3);
                border-radius: 50%;
                animation: efficiency-pulse 2s infinite;
            }

            // 设备状态部分
            .device-status-section {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .status-header {
                display: flex;
                align-items: center;
                gap: 8px;
                background: rgba(16, 42, 67, 0.2);
                padding: 6px 8px;
                border-radius: 6px;
                border: 1px solid rgba(47, 229, 231, 0.2);
            }

            .header-icon {
                .iconfont {
                    font-size: 18px;
                    color: #2FE5E7;
                }
            }

            .header-info {
                flex: 1;
                text-align: center;
            }

            .total-devices {
                font-size: 20px;
                font-family: "BEBAS", sans-serif;
                color: #2FE5E7;
                font-weight: bold;
                line-height: 1;
                text-shadow: 0 0 8px rgba(47, 229, 231, 0.5);
            }

            .header-label {
                font-size: 10px;
                color: #C3D2E0;
                margin-top: 2px;
            }

            // 状态卡片
            .status-cards {
                display: flex;
                flex-direction: column;
                gap: 6px;
                flex: 1;
            }

            .status-card {
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 6px 8px;
                display: flex;
                align-items: center;
                gap: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;

                &:hover {
                    transform: translateX(2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                }

                &.online-card {
                    border-left: 3px solid #2FE5E7;
                    &:hover {
                        border-left-color: #7BF959;
                        background: rgba(16, 42, 67, 0.4);
                    }
                }

                &.offline-card {
                    border-left: 3px solid #FC4444;
                    &:hover {
                        border-left-color: #FF6B6B;
                        background: rgba(16, 42, 67, 0.4);
                    }
                }
            }

            .card-icon {
                position: relative;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: rgba(16, 42, 67, 0.5);
                border: 1px solid rgba(255, 255, 255, 0.1);

                .iconfont {
                    font-size: 12px;
                    z-index: 2;
                    
                    .online-card & {
                        color: #2FE5E7;
                    }
                    .offline-card & {
                        color: #FC4444;
                    }
                }
            }

            .icon-pulse {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 30px;
                height: 30px;
                border-radius: 50%;
                pointer-events: none;

                &.online-pulse {
                    background: radial-gradient(circle, rgba(47, 229, 231, 0.3) 0%, transparent 70%);
                    animation: card-pulse 2s infinite;
                }

                &.offline-pulse {
                    background: radial-gradient(circle, rgba(252, 68, 68, 0.3) 0%, transparent 70%);
                    animation: warning-pulse 1.5s infinite;
                }
            }

            .card-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 2px;
            }

            .card-value {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                font-weight: bold;
                line-height: 1;

                .online-card & {
                    color: #2FE5E7;
                    text-shadow: 0 0 6px rgba(47, 229, 231, 0.5);
                }
                .offline-card & {
                    color: #FC4444;
                    text-shadow: 0 0 6px rgba(252, 68, 68, 0.5);
                }
            }

            .card-label {
                font-size: 10px;
                color: #C3D2E0;
                font-weight: 500;
            }

            .card-progress {
                width: 40px;
                height: 3px;
                background: rgba(16, 42, 67, 0.5);
                border-radius: 2px;
                overflow: hidden;
                flex-shrink: 0;
            }

            .progress-fill {
                height: 100%;
                border-radius: 2px;
                transition: width 1s ease;
                position: relative;

                &.online-progress {
                    background: linear-gradient(90deg, #2FE5E7, #7BF959);
                }

                &.offline-progress {
                    background: linear-gradient(90deg, #FC4444, #FF6B6B);
                }

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                    animation: progress-shimmer 2s infinite;
                }
            }
        }

        // 空气质量系统 - 空气质量指数样式
        .air-quality-system {
            height: 100%;
            display: flex;
            flex-direction: column;

            .air-quality-display {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 8px;
                gap: 12px;
            }

            .aqi-main {
                flex-shrink: 0;
                text-align: center;
            }

            .aqi-circle {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-bottom: 6px;
                border: 3px solid;
                background: rgba(16, 42, 67, 0.3);

                &.excellent {
                    border-color: #7BF959;
                    box-shadow: 0 0 8px rgba(123, 249, 89, 0.3);
                }
            }

            .aqi-number {
                font-size: 18px;
                font-family: "BEBAS", sans-serif;
                color: #7BF959;
                font-weight: bold;
                line-height: 1;
            }

            .aqi-text {
                font-size: 12px;
                color: #7BF959;
                font-weight: bold;
            }

            .air-status {
                margin-top: 4px;
            }

            .status-text {
                font-size: 12px;
                color: #F3F3F3;
                font-weight: bold;
            }

            .status-desc {
                font-size: 9px;
                color: #C3D2E0;
                margin-top: 2px;
            }

            .pollutant-bars {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .pollutant-item {
                background: rgba(16, 42, 67, 0.3);
                border-radius: 4px;
                padding: 6px;
            }

            .pollutant-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
            }

            .pollutant-name {
                font-size: 10px;
                color: #C3D2E0;
            }

            .pollutant-value {
                font-size: 12px;
                font-family: "BEBAS", sans-serif;
                color: #F3F3F3;
                font-weight: bold;
            }

            .progress-bar {
                height: 4px;
                background: rgba(16, 42, 67, 0.5);
                border-radius: 2px;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                border-radius: 2px;
                transition: width 0.5s ease;

                &.excellent {
                    background: #7BF959;
                }

                &.good {
                    background: #2FE5E7;
                }

                &.moderate {
                    background: #FFB347;
                }
            }
        }

        // 超低温系统 - 冷库监控样式
        .ultra-temp-system {
            height: 100%;
            display: flex;
            flex-direction: column;

            .freezer-monitor {
                display: flex;
                flex: 1;
                padding: 8px;
                gap: 8px;
            }

            .freezer-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .freezer-box {
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 8px;
                position: relative;
                overflow: hidden;
                border: 2px solid rgba(75, 145, 255, 0.4);
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(135deg, rgba(75, 145, 255, 0.1) 0%, rgba(47, 229, 231, 0.05) 100%);
                    pointer-events: none;
                }
            }

            .cooling-waves {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 60px;
                height: 60px;
            }

            .wave {
                position: absolute;
                border: 1px solid rgba(75, 145, 255, 0.3);
                border-radius: 50%;
                animation: cooling-wave 3s infinite;

                &.wave-1 {
                    width: 20px;
                    height: 20px;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    animation-delay: 0s;
                }

                &.wave-2 {
                    width: 35px;
                    height: 35px;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    animation-delay: 1s;
                }

                &.wave-3 {
                    width: 50px;
                    height: 50px;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    animation-delay: 2s;
                }
            }

            .temp-sensor {
                position: absolute;
                top: 8px;
                right: 8px;
                z-index: 2;

                .iconfont {
                    font-size: 14px;
                    color: #4B91FF;
                    animation: pulse 2s infinite;
                }
            }

            .temp-reading {
                position: relative;
                z-index: 2;
                text-align: center;
            }

            .temp-value {
                font-size: 18px;
                font-family: "BEBAS", sans-serif;
                color: #4B91FF;
                font-weight: bold;
                text-shadow: 0 0 10px rgba(75, 145, 255, 0.5);
                background: rgba(16, 42, 67, 0.6);
                padding: 4px 8px;
                border-radius: 12px;
                border: 1px solid rgba(75, 145, 255, 0.3);
            }

            .freezer-status {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
                background: rgba(16, 42, 67, 0.2);
                padding: 4px 8px;
                border-radius: 4px;
            }

            .status-led {
                width: 6px;
                height: 6px;
                border-radius: 50%;

                &.active {
                    background: #4B91FF;
                    box-shadow: 0 0 6px #4B91FF;
                    animation: blink 1.5s infinite;
                }
            }

            .status-text {
                font-size: 10px;
                color: #C3D2E0;
            }

            .environment-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .info-card {
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 6px;
                flex: 1;
                display: flex;
                flex-direction: column;
                border: 1px solid rgba(75, 145, 255, 0.2);
            }

            .card-header {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-bottom: 4px;

                .iconfont {
                    font-size: 12px;
                    color: #2FE5E7;
                }

                span {
                    font-size: 9px;
                    color: #C3D2E0;
                }
            }

            .card-value {
                font-size: 14px;
                font-family: "BEBAS", sans-serif;
                color: #F3F3F3;
                font-weight: bold;
                margin-bottom: 4px;
            }

            .humidity-bar {
                height: 3px;
                background: rgba(16, 42, 67, 0.5);
                border-radius: 2px;
                overflow: hidden;
            }

            .humidity-fill {
                height: 100%;
                background: linear-gradient(90deg, #2FE5E7, #7BF959);
                border-radius: 2px;
                transition: width 0.5s ease;
            }

            .energy-indicator {
                width: 100%;
                height: 3px;
                border-radius: 2px;

                &.normal {
                    background: #7BF959;
                    box-shadow: 0 0 4px rgba(123, 249, 89, 0.3);
                }
            }
        }

        // 能耗系统样式
        .energy-system {
            .energy-data-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }

                .energy-data {
                    flex: 1;
                    text-align: center;
                    background: rgba(16, 42, 67, 0.3);
                    border-radius: 6px;
                    padding: 8px;
                    box-sizing: border-box;
                    margin-right: 8px;

                    &:last-child {
                        margin-right: 0;
                    }

                    .energy-value {
                        font-size: 18px;
                        font-family: "BEBAS", sans-serif;
                        color: #F3F3F3;
                        background: rgba(16, 42, 67, 0.5);
                        padding: 4px 6px;
                        border-radius: 4px;
                        border-bottom: 1px solid rgba(47, 229, 231, 0.3);
                        text-shadow: 0 0 10px rgba(47, 229, 231, 0.5);
                        background-image: linear-gradient(180deg, #ffffff, #c3d2e0);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        font-weight: bold;
                        letter-spacing: 1px;
                    }

                    .energy-label {
                        font-size: 11px;
                        color: #C3D2E0;
                        margin-top: 3px;
                    }
                }
            }
        }

        .environment-monitor {
            .monitor-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-gap: 8px;
                padding: 3px;
            }

            .monitor-item {
                display: flex;
                align-items: center;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 6px;

                .monitor-icon {
                    width: 25px;
                    height: 25px;
                    margin-right: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .iconfont {
                        font-size: 18px;
                        color: #2FE5E7;
                        text-shadow: 0 0 5px rgba(47, 229, 231, 0.5);
                    }

                    .icon-temperature:before {
                        content: "\e76f";
                    }

                    .icon-humidity:before {
                        content: "\e76e";
                    }

                    .icon-tvoc:before {
                        content: "\e77a";
                    }

                    .icon-co2:before {
                        content: "\e765";
                    }

                    .icon-formaldehyde:before {
                        content: "\e777";
                    }

                    .icon-pm25:before {
                        content: "\e779";
                    }
                }

                .monitor-data {
                    flex: 1;

                    .monitor-value {
                        font-size: 16px;
                        font-family: "BEBAS", sans-serif;
                        color: #F3F3F3;
                        text-shadow: 0 0 8px rgba(47, 229, 231, 0.5);
                        background-image: linear-gradient(180deg, #ffffff, #c3d2e0);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        font-weight: bold;
                        letter-spacing: 1px;
                    }

                    .monitor-label {
                        font-size: 10px;
                        color: #C3D2E0;
                    }
                }
            }
        }

        .su {
            display: flex;
            flex-direction: column;
            white-space: nowrap;
            justify-content: center;
            flex: 1;
            padding: 6px;

            &>div {
                display: flex;
                justify-content: space-between;
                padding: 0 6px;
            }

            &>div:first-child {
                margin-bottom: 15px;
            }

            .text {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #EAEBEC;
                margin-top: 3px;
            }

            .num {
                font-size: 28px;
                font-family: "BEBAS";
                font-weight: 400;
                color: #F3F3F3;
                text-shadow: 0 0 8px rgba(47, 229, 231, 0.3);
            }

            &-item {
                flex: 1;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 10px 8px;
                margin: 0 3px;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
                }

                &>div:first-child {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-bottom: 3px;

                    .iconfont {
                        font-size: 28px;
                        color: #2FE5E7;
                        text-shadow: 0 0 10px rgba(47, 229, 231, 0.7);
                    }

                    .icon-on:before {
                        content: "\e72d";
                    }

                    .icon-off:before {
                        content: "\e786";
                    }

                    .icon-online:before {
                        content: "\e776";
                    }

                    .icon-offline:before {
                        content: "\e787";
                        color: #FC4444;
                        text-shadow: 0 0 10px rgba(252, 68, 68, 0.7);
                    }

                    .icon-normal:before {
                        content: "\e742";
                    }

                    .icon-alarm:before {
                        content: "\e743";
                    }
                }

                &>div:last-child {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                }
            }
        }
    }

    .wrapper_right {
        right: 0;

        // 能耗系统 - 优化版能耗看板样式
        .energy-consumption {
            height: 100%;
            display: flex;
            flex-direction: column;

            .energy-dashboard {
                display: flex;
                flex-direction: column;
                flex: 1;
                padding: 8px;
                gap: 8px;
            }

            .main-metrics {
                display: flex;
                gap: 8px;
                flex: 2;
            }

            .metric-card {
                flex: 1;
                background: rgba(16, 42, 67, 0.4);
                border-radius: 8px;
                padding: 10px;
                position: relative;
                overflow: hidden;
                border: 1px solid rgba(47, 229, 231, 0.2);
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
                    border-color: rgba(47, 229, 231, 0.4);
                }

                &.primary-card {
                    background: linear-gradient(135deg, rgba(47, 229, 231, 0.1) 0%, rgba(16, 42, 67, 0.4) 100%);
                    border-color: rgba(47, 229, 231, 0.3);
                }

                &.special-card {
                    background: linear-gradient(135deg, rgba(123, 249, 89, 0.1) 0%, rgba(16, 42, 67, 0.4) 100%);
                    border-color: rgba(123, 249, 89, 0.3);
                }
            }

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 8px;
            }

            .metric-icon {
                position: relative;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 6px;

                .iconfont {
                    font-size: 18px;
                    z-index: 2;
                    position: relative;
                }

                &.daily-icon {
                    background: rgba(47, 229, 231, 0.2);
                    border: 1px solid rgba(47, 229, 231, 0.3);

                    .iconfont {
                        color: #2FE5E7;
                    }
                }

                &.savings-icon {
                    background: rgba(123, 249, 89, 0.2);
                    border: 1px solid rgba(123, 249, 89, 0.3);

                    .iconfont {
                        color: #7BF959;
                    }
                }
            }

            .icon-glow {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 40px;
                height: 40px;
                background: radial-gradient(circle, rgba(47, 229, 231, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                animation: glow-pulse 3s infinite;
            }

            .icon-pulse {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 40px;
                height: 40px;
                background: radial-gradient(circle, rgba(123, 249, 89, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                animation: pulse-energy 2s infinite;
            }

            .metric-trend {
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .trend-arrow {
                font-size: 12px;
                font-weight: bold;

                &.up {
                    color: #7BF959;
                }

                &.down {
                    color: #FC4444;
                }
            }

            .trend-percent {
                font-size: 10px;
                color: #7BF959;
                background: rgba(123, 249, 89, 0.1);
                padding: 2px 6px;
                border-radius: 10px;
                border: 1px solid rgba(123, 249, 89, 0.2);
            }

            .savings-badge {
                background: linear-gradient(45deg, #7BF959, #2FE5E7);
                color: #000;
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
                box-shadow: 0 2px 8px rgba(123, 249, 89, 0.3);
            }

            .metric-content {
                text-align: center;
                position: relative;
                z-index: 2;
            }

            .metric-value {
                font-size: 20px;
                font-family: "BEBAS", sans-serif;
                color: #F3F3F3;
                font-weight: bold;
                line-height: 1;
                text-shadow: 0 0 15px rgba(47, 229, 231, 0.6);

                &.savings-value {
                    color: #7BF959;
                    text-shadow: 0 0 15px rgba(123, 249, 89, 0.6);
                }
            }

            .metric-label {
                font-size: 11px;
                color: #C3D2E0;
                margin: 2px 0;
                font-weight: 500;
            }

            .metric-unit {
                font-size: 9px;
                color: #8FA8B8;
                font-style: italic;
            }

            .card-decoration {
                position: absolute;
                top: -20px;
                right: -20px;
                width: 60px;
                height: 60px;
                background: radial-gradient(circle, rgba(47, 229, 231, 0.1) 0%, transparent 70%);
                border-radius: 50%;
                pointer-events: none;
            }

            .energy-particles {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;
                overflow: hidden;

                .particle {
                    position: absolute;
                    width: 4px;
                    height: 4px;
                    background: #7BF959;
                    border-radius: 50%;
                    animation: float-particle 4s infinite ease-in-out;

                    &:nth-child(1) {
                        top: 20%;
                        left: 20%;
                        animation-delay: 0s;
                    }

                    &:nth-child(2) {
                        top: 60%;
                        right: 30%;
                        animation-delay: 1.5s;
                    }

                    &:nth-child(3) {
                        bottom: 30%;
                        left: 60%;
                        animation-delay: 3s;
                    }
                }
            }

            .secondary-metrics {
                flex: 1;
            }

            .metric-row {
                display: flex;
                gap: 8px;
                height: 100%;
            }

            .mini-card {
                flex: 1;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 8px;
                display: flex;
                flex-direction: column;
                border: 1px solid rgba(47, 229, 231, 0.1);
                transition: all 0.3s ease;

                &:hover {
                    border-color: rgba(47, 229, 231, 0.3);
                    background: rgba(16, 42, 67, 0.4);
                }

                &.monthly-card {
                    border-left: 3px solid #FFB347;
                }

                &.yearly-card {
                    border-left: 3px solid #9B59B6;
                }
            }

            .mini-icon {
                align-self: center;
                margin-bottom: 6px;

                .iconfont {
                    font-size: 16px;
                    color: #2FE5E7;
                }
            }

            .mini-content {
                text-align: center;
                margin-bottom: 6px;
            }

            .mini-value {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                color: #F3F3F3;
                font-weight: bold;
                line-height: 1;
            }

            .mini-label {
                font-size: 10px;
                color: #C3D2E0;
                margin-top: 2px;
            }

            .mini-progress {
                height: 4px;
                background: rgba(16, 42, 67, 0.5);
                border-radius: 2px;
                overflow: hidden;
                margin-top: auto;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #2FE5E7, #7BF959);
                border-radius: 2px;
                transition: width 1s ease;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                    animation: shimmer 2s infinite;
                }

                &.yearly {
                    background: linear-gradient(90deg, #9B59B6, #E74C3C);
                }
            }
        }

        // 门禁系统 - 门状态样式（橙色主题）
        .access-control {
            height: 100%;
            display: flex;
            flex-direction: column;

            .door-status {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 8px;
                gap: 15px;
            }

            .door-visual {
                flex-shrink: 0;
            }

            .door-frame {
                width: 60px;
                height: 80px;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 4px;
                position: relative;
                border: 2px solid #4A90E2;
                overflow: hidden;
            }

            .door-panel {
                position: absolute;
                top: 4px;
                left: 4px;
                right: 4px;
                bottom: 4px;
                background: rgba(16, 42, 67, 0.6);
                border-radius: 2px;
            }

            .door-handle {
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 8px;
                background: #7BB3F0;
                border-radius: 2px;
            }

            .access-indicator {
                position: absolute;
                top: 4px;
                right: 4px;
                width: 8px;
                height: 8px;
                border-radius: 50%;

                &.online {
                    background: #4A90E2;
                    box-shadow: 0 0 8px #4A90E2;
                    animation: pulse 2s infinite;
                }
            }

            .door-stats {
                flex: 1;
            }

            .stat-row {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-bottom: 10px;
            }

            .stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: rgba(16, 42, 67, 0.3);
                padding: 6px 8px;
                border-radius: 4px;
                border-left: 2px solid rgba(74, 144, 226, 0.3);
            }

            .stat-label {
                font-size: 12px;
                color: #C3D2E0;
            }

            .stat-value {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                font-weight: bold;

                &.online {
                    color: #4A90E2;
                }

                &.offline {
                    color: #FC4444;
                }
            }

            .total-devices {
                text-align: center;
                font-size: 11px;
                color: #7BB3F0;
                background: rgba(74, 144, 226, 0.1);
                padding: 4px;
                border-radius: 3px;
                border: 1px solid rgba(74, 144, 226, 0.2);
            }
        }

        // 视频监控 - 监控网格样式
        .video-surveillance {
            height: 100%;
            display: flex;
            flex-direction: column;

            .camera-grid {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 8px;
                gap: 12px;
            }

            .camera-overview {
                flex-shrink: 0;
                text-align: center;
            }

            .camera-total {
                margin-bottom: 8px;
                text-align: center;
            }

            .total-number {
                font-size: 24px;
                font-family: "BEBAS", sans-serif;
                color: #2FE5E7;
                font-weight: bold;
                text-shadow: 0 0 10px rgba(47, 229, 231, 0.5);
                line-height: 1;
            }

            .total-label {
                font-size: 9px;
                color: #C3D2E0;
                display: block;
                margin-top: 2px;
            }

            .camera-icon {
                position: relative;
                display: inline-block;

                .iconfont {
                    font-size: 28px;
                    color: #2FE5E7;
                }

                .recording-dot {
                    position: absolute;
                    top: 2px;
                    right: 2px;
                    width: 6px;
                    height: 6px;
                    background: #FC4444;
                    border-radius: 50%;
                    animation: blink 1.5s infinite;
                }
            }

            .camera-stats {
                flex: 1;
            }

            .stat-grid {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .stat-box {
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 8px;
                text-align: center;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 2px;
                    border-radius: 6px 6px 0 0;
                }

                &.online::before {
                    background: #2FE5E7;
                }

                &.offline::before {
                    background: #FC4444;
                }
            }

            .stat-number {
                font-size: 18px;
                font-family: "BEBAS", sans-serif;
                font-weight: bold;
                margin-bottom: 2px;

                .online & {
                    color: #2FE5E7;
                }

                .offline & {
                    color: #FC4444;
                }
            }

            .stat-text {
                font-size: 10px;
                color: #C3D2E0;
            }
        }

        // 漏水检测 - 水滴动画样式
        .water-leak-system {
            height: 100%;
            display: flex;
            flex-direction: column;

            .leak-detection {
                display: flex;
                align-items: center;
                flex: 1;
                padding: 8px;
                gap: 12px;
            }

            .water-visual {
                flex-shrink: 0;
                text-align: center;
            }

            .water-container {
                width: 60px;
                height: 60px;
                position: relative;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 50%;
                margin-bottom: 8px;
                overflow: hidden;
            }

            .water-drop {
                position: absolute;
                top: 10px;
                left: 50%;
                transform: translateX(-50%);
                width: 12px;
                height: 15px;
                background: #2FE5E7;
                border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
                animation: water-drop-fall 3s infinite;
                box-shadow: 0 0 8px rgba(47, 229, 231, 0.6);

                &.normal {
                    background: #2FE5E7;
                    box-shadow: 0 0 8px rgba(47, 229, 231, 0.6);
                }

                &.warning {
                    background: #FC4444;
                    box-shadow: 0 0 8px rgba(252, 68, 68, 0.6);
                }
            }

            .water-level {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 20px;
                background: linear-gradient(to top, rgba(47, 229, 231, 0.4), rgba(123, 249, 89, 0.2), transparent);
                border-radius: 0 0 50% 50%;
                animation: water-level-wave 2s ease-in-out infinite;
            }

            .ripple-effect {
                position: absolute;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 20px;
                height: 20px;
                border: 2px solid rgba(47, 229, 231, 0.6);
                border-radius: 50%;
                animation: water-ripple 2.5s infinite;
                box-shadow: 0 0 10px rgba(47, 229, 231, 0.3);
            }

            .detection-sensors {
                display: flex;
                gap: 4px;
                justify-content: center;
            }

            .sensor {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: rgba(16, 42, 67, 0.3);

                &.active {
                    background: #7BF959;
                    box-shadow: 0 0 6px rgba(123, 249, 89, 0.8);
                    animation: sensor-pulse-green 1.8s infinite;
                }

                &.warning {
                    background: #FC4444;
                    box-shadow: 0 0 6px rgba(252, 68, 68, 0.8);
                    animation: sensor-warning-blink 1.2s infinite;
                }
            }

            .leak-stats {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .status-indicator {
                display: flex;
                align-items: center;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 6px 8px;
                gap: 8px;

                &.normal .indicator-dot {
                    background: #7BF959;
                    box-shadow: 0 0 6px #7BF959;
                }

                &.alarm .indicator-dot {
                    background: #FC4444;
                    box-shadow: 0 0 6px #FC4444;
                }
            }

            .indicator-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                flex-shrink: 0;
            }

            .indicator-text {
                font-size: 12px;
                color: #C3D2E0;
                flex: 1;
            }

            .indicator-count {
                font-size: 16px;
                font-family: "BEBAS", sans-serif;
                font-weight: bold;

                .normal & {
                    color: #7BF959;
                }

                .alarm & {
                    color: #FC4444;
                }
            }
        }

        // 能耗系统样式
        .energy-system {
            .energy-data-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }

                .energy-data {
                    flex: 1;
                    text-align: center;
                    background: rgba(16, 42, 67, 0.3);
                    border-radius: 6px;
                    padding: 8px;
                    box-sizing: border-box;
                    margin-right: 8px;

                    &:last-child {
                        margin-right: 0;
                    }

                    .energy-value {
                        font-size: 18px;
                        font-family: "BEBAS", sans-serif;
                        color: #F3F3F3;
                        background: rgba(16, 42, 67, 0.5);
                        padding: 4px 6px;
                        border-radius: 4px;
                        border-bottom: 1px solid rgba(47, 229, 231, 0.3);
                        text-shadow: 0 0 10px rgba(47, 229, 231, 0.5);
                        background-image: linear-gradient(180deg, #ffffff, #c3d2e0);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        font-weight: bold;
                        letter-spacing: 1px;
                    }

                    .energy-label {
                        font-size: 11px;
                        color: #C3D2E0;
                        margin-top: 3px;
                    }
                }
            }
        }

        .environment-monitor {
            .monitor-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                grid-gap: 8px;
                padding: 3px;
            }

            .monitor-item {
                display: flex;
                align-items: center;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 6px;

                .monitor-icon {
                    width: 25px;
                    height: 25px;
                    margin-right: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .iconfont {
                        font-size: 18px;
                        color: #2FE5E7;
                        text-shadow: 0 0 5px rgba(47, 229, 231, 0.5);
                    }

                    .icon-temperature:before {
                        content: "\e76f";
                    }

                    .icon-humidity:before {
                        content: "\e76e";
                    }

                    .icon-tvoc:before {
                        content: "\e77a";
                    }

                    .icon-co2:before {
                        content: "\e765";
                    }

                    .icon-formaldehyde:before {
                        content: "\e777";
                    }

                    .icon-pm25:before {
                        content: "\e779";
                    }
                }

                .monitor-data {
                    flex: 1;

                    .monitor-value {
                        font-size: 16px;
                        font-family: "BEBAS", sans-serif;
                        color: #F3F3F3;
                        text-shadow: 0 0 8px rgba(47, 229, 231, 0.5);
                        background-image: linear-gradient(180deg, #ffffff, #c3d2e0);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        font-weight: bold;
                        letter-spacing: 1px;
                    }

                    .monitor-label {
                        font-size: 10px;
                        color: #C3D2E0;
                    }
                }
            }
        }

        .su {
            display: flex;
            flex-direction: column;
            white-space: nowrap;
            justify-content: center;
            flex: 1;
            padding: 6px;

            &>div {
                display: flex;
                justify-content: space-between;
                padding: 0 6px;
            }

            &>div:first-child {
                margin-bottom: 15px;
            }

            .text {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #EAEBEC;
                margin-top: 3px;
            }

            .num {
                font-size: 28px;
                font-family: "BEBAS";
                font-weight: 400;
                color: #F3F3F3;
                text-shadow: 0 0 8px rgba(47, 229, 231, 0.3);
            }

            &-item {
                flex: 1;
                background: rgba(16, 42, 67, 0.3);
                border-radius: 6px;
                padding: 10px 8px;
                margin: 0 3px;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
                }

                &>div:first-child {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-bottom: 3px;

                    .iconfont {
                        font-size: 28px;
                        color: #2FE5E7;
                        text-shadow: 0 0 10px rgba(47, 229, 231, 0.7);
                    }

                    .icon-on:before {
                        content: "\e72d";
                    }

                    .icon-off:before {
                        content: "\e786";
                    }

                    .icon-online:before {
                        content: "\e776";
                    }

                    .icon-offline:before {
                        content: "\e787";
                        color: #FC4444;
                        text-shadow: 0 0 10px rgba(252, 68, 68, 0.7);
                    }

                    .icon-normal:before {
                        content: "\e742";
                    }

                    .icon-alarm:before {
                        content: "\e743";
                    }
                }

                &>div:last-child {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                }
            }
        }
    }
}

// 动画效果
@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

@keyframes water-drop-fall {
    0% {
        transform: translateX(-50%) translateY(0) scale(1);
        opacity: 1;
    }
    20% {
        transform: translateX(-50%) translateY(8px) scale(1.1);
        opacity: 0.9;
    }
    80% {
        transform: translateX(-50%) translateY(25px) scale(0.9);
        opacity: 0.7;
    }
    100% {
        transform: translateX(-50%) translateY(35px) scale(0.8);
        opacity: 0;
    }
}

@keyframes water-ripple {
    0% {
        transform: translateX(-50%) scale(0.3);
        opacity: 1;
        border-width: 3px;
    }
    50% {
        transform: translateX(-50%) scale(1);
        opacity: 0.6;
        border-width: 2px;
    }
    100% {
        transform: translateX(-50%) scale(1.8);
        opacity: 0;
        border-width: 1px;
    }
}

@keyframes water-level-wave {
    0%, 100% {
        height: 20px;
        opacity: 0.4;
    }
    50% {
        height: 25px;
        opacity: 0.6;
    }
}

@keyframes sensor-pulse-green {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 6px rgba(123, 249, 89, 0.8);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.3);
        box-shadow: 0 0 12px rgba(123, 249, 89, 1);
    }
}

@keyframes sensor-warning-blink {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    25% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
    75% {
        opacity: 0.3;
        transform: scale(0.9);
    }
}

@keyframes sparkle {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes cooling-wave {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }

    50% {
        opacity: 0.6;
    }

    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes scan-sweep {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes glow-pulse {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes pulse-energy {
    0%, 100% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes float-particle {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-10px) scale(1.2);
        opacity: 1;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

// 新风系统动画
@keyframes rotate-fan {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes float-air-particle {
    0%, 100% {
        transform: translateY(0) translateX(0) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-8px) translateX(3px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-5px) translateX(-2px) scale(0.8);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-10px) translateX(4px) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes status-pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.3);
        opacity: 0.8;
    }
}

// 排风系统简洁动画
@keyframes rotate-ring {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes efficiency-pulse {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

@keyframes card-pulse {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

@keyframes warning-pulse {
    0%, 100% {
        opacity: 0.4;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.3);
    }
}

@keyframes progress-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes warning-blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

// 图标定义
.iconfont {
    &.icon-daily:before {
        content: "\e760";
    }

    &.icon-monthly:before {
        content: "\e761";
    }

    &.icon-yearly:before {
        content: "\e762";
    }

    &.icon-savings:before {
        content: "\e763";
    }

    &.icon-camera:before {
        content: "\e764";
    }

    &.icon-temp-sensor:before {
        content: "\e765";
    }

    &.icon-humidity:before {
        content: "\e766";
    }

    &.icon-energy:before {
        content: "\e767";
    }

    &.icon-fingerprint:before {
        content: "\e768";
    }

    &.icon-access-online:before {
        content: "\e769";
    }

    &.icon-access-offline:before {
        content: "\e770";
    }

    &.icon-efficiency:before {
        content: "\e771";
    }

    &.icon-device-online:before {
        content: "\e772";
    }

    &.icon-device-offline:before {
        content: "\e773";
    }
}

// 自适应全屏样式
@media screen and (max-width: 1920px) {

    .z100 .wrapper_left,
    .z100 .wrapper_right {
        width: 380px;
    }
}

@media screen and (max-width: 1600px) {

    .z100 .wrapper_left,
    .z100 .wrapper_right {
        width: 350px;
    }
}

@media screen and (max-width: 1366px) {

    .z100 .wrapper_left,
    .z100 .wrapper_right {
        width: 320px;
    }
}

@media screen and (max-height: 900px) {

    .z100 .wrapper_left,
    .z100 .wrapper_right {
        padding: 50px 0 15px 0;
    }

    .z100 .item {
        margin-bottom: 12px;
    }
}

@media screen and (max-height: 768px) {

    .z100 .wrapper_left,
    .z100 .wrapper_right {
        padding: 40px 0 10px 0;
    }

    .z100 .item {
        margin-bottom: 8px;
    }

    .z100 .item-body {
        padding: 6px;
    }

    .z100 .su-item {
        padding: 8px 6px;
    }

    .z100 .num {
        font-size: 24px !important;
    }

    .z100 .text {
        font-size: 12px !important;
    }
}

@media screen and (max-height: 600px) {

    .z100 .wrapper_left,
    .z100 .wrapper_right {
        padding: 30px 0 5px 0;
    }

    .z100 .item {
        margin-bottom: 5px;
    }

    .z100 .item-body {
        padding: 4px;
    }

    .z100 .su-item {
        padding: 6px 4px;
    }

    .z100 .num {
        font-size: 20px !important;
    }

    .z100 .text {
        font-size: 11px !important;
    }

    .z100 .iconfont {
        font-size: 20px !important;
    }
}
</style>
