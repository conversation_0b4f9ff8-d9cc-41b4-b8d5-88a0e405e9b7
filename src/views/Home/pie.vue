<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: ['data'],
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        const initChart = () => {
            var dataStyle = {
                normal: {
                    label: {
                        show: false
                    },
                    labelLine: {
                        show: false
                    },
                    shadowBlur: 0,
                    shadowColor: '#203665'
                }
            };
          var   option = {
                series: [{
                    name: '第一个圆环',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 50],
                    itemStyle: dataStyle,
                    hoverAnimation: false,
                    center: ['15%', '50%'],
                    data: [{
                        value: props.data.count|| 0,
                        label: {
                            normal: {
                                rich: {
                                    a: {
                                        color: '#3a7ad5',
                                        align: 'center',
                                        fontSize: 12,
                                        fontWeight: "bold"
                                    },
                                    b: {
                                        color: '#fff',
                                        align: 'center',
                                        fontSize: 12
                                    }
                                },
                                formatter: function (params) {
                                    return "{b|设备总数}\n\n" + "{a|" + params.value + "个}" ;
                                },
                                position: 'center',
                                show: true,
                                textStyle: {
                                    fontSize: '14',
                                    fontWeight: 'normal',
                                    color: '#fff'
                                }
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#2c6cc4',
                                shadowColor: '#2c6cc4',
                                shadowBlur: 0
                            }
                        }
                    }, {
                        value: 0,
                        name: 'invisible',
                        itemStyle: {
                            normal: {
                                color: '#24375c'
                            },
                            emphasis: {
                                color: '#24375c'
                            }
                        }
                    }]
                }, {
                    name: '第二个圆环',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 50],
                    itemStyle: dataStyle,
                    hoverAnimation: false,
                    center: ['50%', '50%'],
                    data: [{
                        value:props.data.normalCount|| 0,
                        label: {
                            normal: {
                                rich: {
                                    a: {
                                        color: '#d03e93',
                                        align: 'center',
                                        fontSize: 12,
                                        fontWeight: "bold"
                                    },
                                    b: {
                                        color: '#fff',
                                        align: 'center',
                                        fontSize: 12
                                    }
                                },
                                formatter: function (params) {
                                    return "{b|故障数量}\n\n" + "{a|" + params.value + "个}" ;
                                },
                                position: 'center',
                                show: true,
                                textStyle: {
                                    fontSize: '14',
                                    fontWeight: 'normal',
                                    color: '#fff'
                                }
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#ef45ac',
                                shadowColor: '#ef45ac',
                                shadowBlur: 0
                            }
                        }
                    }, {
                        value: props.data.count-props.data.normalCount|| 50,
                        name: 'invisible',
                        itemStyle: {
                            normal: {
                                color: '#412a4e'
                            },
                            emphasis: {
                                color: '#412a4e'
                            }
                        }
                    }]
                }, {
                    name: '第三个圆环',
                    type: 'pie',
                    clockWise: false,
                    radius: [40, 50],
                    itemStyle: dataStyle,
                    hoverAnimation: false,
                    center: ['85%', '50%'],
                    data: [{
                        value: props.data.statusCount|| 0,
                        label: {
                            normal: {
                                rich: {
                                    a: {
                                        color: '#603dd0',
                                        align: 'center',
                                        fontSize: 12,
                                        fontWeight: "bold"
                                    },
                                    b: {
                                        color: '#fff',
                                        align: 'center',
                                        fontSize: 12
                                    }
                                },
                                formatter: function (params) {
                                    return "{b|运行数量}\n\n" + "{a|" + params.value + "个}" ;
                                },
                                position: 'center',
                                show: true,
                                textStyle: {
                                    fontSize: '14',
                                    fontWeight: 'normal',
                                    color: '#fff'
                                }
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#613fd1',
                                shadowColor: '#613fd1',
                                shadowBlur: 0
                            }
                        }
                    }, {
                        value:props.data.count-props.data.statusCount|| 0,
                        name: 'invisible',
                        itemStyle: {
                            normal: {
                                color: '#453284'
                            },
                            emphasis: {
                                color: '#453284'
                            }
                        }
                    }]
                }]
            }
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 100%;
    width: 100%;
}
</style>
