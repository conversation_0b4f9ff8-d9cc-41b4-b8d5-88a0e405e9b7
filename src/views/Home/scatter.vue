<template>
<div class="chart" ref="myEcharts"></div>
</template>

    
    
<script>
import {
    ref,
    watch,
    onMounted,
    inject,
    nextTick
} from 'vue'
export default {
    props: ['alarms',],
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        const initChart = () => {
            var chartData = [{
                    name: '周一',
                    value: 13,
                },
                {
                    name: '周二',
                    value: 20,
                },
                {
                    name: '周三',
                    value: 30,
                },
                {
                    name: '周四',
                    value: 30,
                },
                {
                    name: '周五',
                    value: 60,
                },
                {
                    name: '周六',
                    value: 60,
                },
                {
                    name: '周日',
                    value: 100,
                }
            ];

            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    top: '5%',
                    right: '5%',
                    bottom: '10%',
                    left: '10%'
                },
                xAxis: {
                    type: 'category',
                    axisLabel: {
                        show: true,
                        interval: 0, //类目间隔 设置为 1，表示『隔一个标签显示一个标签』
                        textStyle: {
                            color: '#9bc8ff',
                            fontSize: 13
                        },
                        formatter: '{value}'
                    },
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(15,45,134,.9)'
                        }
                    },
                    axisTick: {
                        show: false //坐标轴小标记
                    },
                    data: (function (data) {
                        var arr = [];
                        data.forEach(function (items) {
                            arr.push(items.name); //name
                        });
                        return arr;
                    })(chartData) //载入横坐标数据
                },
                yAxis: {
                    type: 'value',
                    name: '（个）',
                    nameTextStyle: {
                        color: '#93d3fc',
                        fontSize: 12,
                        align: 'right'
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#9bc8ff',
                            fontSize: 13
                        },
                        interval: 0, //类目间隔 设置为 1，表示『隔一个标签显示一个标签』
                        margin: 10,
                        //formatter: '{value}'
                    },
                    axisLine: { //y轴线
                        show: true,
                        lineStyle: {
                            color: 'rgba(15,45,134,.9)'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#1f3097', //横向网格线颜色
                            width: 1,
                            type: 'dotted'
                        }
                    },
                    axisTick: {
                        show: false //坐标轴小标记
                    }
                },
                series: [{
                        name: '报警',
                        type: 'scatter',
                        stack: '总量',
                        label: {
                            normal: {
                                show: false,
                                position: 'top',
                                textStyle: {
                                    color: '#9bc8ff',
                                    fontSize: 12
                                },
                                formatter: '{c}' //图形上显示数字
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#e73e97', //颜色
                            }
                        },
                        symbol: 'circle', //circle, rect, roundRect, triangle,  pin, diamond, arrow
                        symbolPosition: 'end',
                        symbolSize: 12,
                        // symbolOffset: [0, ],
                        data:props.alarms|| (function (data) {
                            var arr = [];
                            data.forEach(function (items) {
                                arr.push({
                                    name: items.name,
                                    value: items.value,
                                    symbolSize: 10
                                })
                            });
                            return arr;
                        })(chartData) //载入数据并设置图形尺寸
                    },

                ]
            };

            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }

        return {
            myEcharts
        }

    }

};
</script>
    
    
<style lang="scss">
.chart {
    width: 100%;
    height: 100%;
}
</style>
