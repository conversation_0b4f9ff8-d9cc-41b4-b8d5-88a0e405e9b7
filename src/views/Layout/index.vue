<template>
<div class="layout">
    <Content>
        <router-view></router-view>
    </Content>
</div>
</template>

<script>


import {
    computed,
    defineComponent,
    onMounted,
    reactive,
    toRefs,
    watch,
} from 'vue';

import {
    useRoute
} from 'vue-router';

import Content from './Content.vue';
import { useAppStore } from '@/stores/app';
export default defineComponent({
    components: {
        Content
    },
    setup() {
        const route = useRoute()
        const store = useAppStore()
        const state = reactive({
            isShow: true
        })
 

        return {
            ...toRefs(state),
        }
    }
})
</script>

<style lang="scss" scoped>
.layout {
    position: relative;
    width: 100%;
    height: 100%;
   
    overflow: hidden;
}

</style>
