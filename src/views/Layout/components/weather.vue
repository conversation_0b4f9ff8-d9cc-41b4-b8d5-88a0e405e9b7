<template>
    <div class="weather center">
        <div class="row">
            <el-switch active-text="联网实时" inactive-text="手动模拟" v-model="mode" @change="changeMode"></el-switch>
        </div>
        <div class="row">
            <div class="h_center " @click="changeWeather('clear_skies')">
                <img src="../img/晴.png" />
                <div>晴天</div>
            </div>
            <div class="h_center " @click="changeWeather('cloudy')">
                <img src="../img/多云.png" />
                <div>多云</div>
            </div>
            <div class="h_center" @click="changeWeather('rain_t')">
                <img src="../img/阵雨.png" />
                <div>雷阵雨</div>
            </div>
            <div class="h_center" @click="changeWeather('overcast')">
                <img src="../img/阴.png" />
                <div>阴</div>
            </div>
        </div>
        <div class="row">
            <div class="h_center" @click="changeWeather('rain_light')">
                <img src="../img/小雨.png" />
                <div>小雨</div>
            </div>
            <div class="h_center" @click="changeWeather('rain')">
                <img src="../img/大雨.png" />
                <div>大雨</div>
            </div>
            <div class="h_center" @click="changeWeather('foggy')">
                <img src="../img/雾霾.png" />
                <div>雾霾</div>
            </div>
            <div class="h_center" @click="changeWeather('snow')">
                <img src="../img/小雪.png" />
                <div>小雪</div>
            </div>
        </div>
        <div class="row">
            <div class="h_center" @click="changeWeather('snow_b')">
                <img src="../img/大雪.png" />
                <div>大雪</div>
            </div>



        </div>

        <div class="close" @click="clickWeather">
            <i class="iconfont iconchahao"></i>
        </div>
        <div class="time">
            <el-slider v-model="time" @input="changeTime" :min="0" :max="24" :show-tooltip="false" show-stops :step="2"
                :marks="marks" />
        </div>
    </div>
</template>

<script>
import { useMitt } from '@/hooks/mitt';



export default defineComponent({
    setup(props, {
        emit
    }) {
        const emitter = useMitt();
      
        const api = inject('$api')

        const state = reactive({
            time: 15,
            mode: true,//默认自动联网
            emit: null,
            marks: {
                0: '0',
                2: '2',
                4: '4',
                6: '6',
                8: '8',
                10: '10',
                12: '12',
                14: '14',
                16: '16',
                18: '18',
                20: '20',
                22: '22',
                24: '24'
            }

        });
        const clickWeather = () => {

            emit('clickWeather')
        }
        const changeTime = (val) => {
            emitter.miitEmit('ue', {
                type: 'time',
                value: val
            })
        }
        const changeWeather = (val) => {
            // if (state.mode) {
                emitter.miitEmit('ue', {
                    type: 'weather',
                    value: val
                })
            // }
        }

        const changeMode = (val) => {
            //true to string
         sessionStorage.setItem('netWeather',val.toString())
  
         emitter.miitEmit('changeNetWeather',val)
            
        }

        onMounted(() => {
          sessionStorage.getItem('netWeather')?state.mode=sessionStorage.getItem('netWeather').toLowerCase()=='true'?true:false:true  
        })

        return {
            ...toRefs(state),
            clickWeather,
            changeWeather,
            changeTime,
            changeMode,
        }
    },
})
</script>

<style lang="scss" scoped>
.weather {
    width: 418px;
    height: 400px;
    background: #020912;
    opacity: 0.85;
    border-top: 1px solid #6C9ED1;

    font-size: 14px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: 300;
    color: #92ACCC;
    flex-direction: column;

    position: relative;

    img {
        margin-bottom: 5px;
    }

    .h_center {
        width: 72px;
        height: 80px;
        background: #020D1C;
        border: 1px solid #2D435E;
        cursor: pointer;
    }

    .row {
        width: 339px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .row:last-of-type {
        margin-bottom: unset;
    }

    .close {
        color: white;
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
    }

    .time {
        width: 338px;
        margin: 0 auto;
    }

}

:deep(.el-slider__bar) {
    background: linear-gradient(-90deg, #1E79F2, #1CD3DE);

}

:deep(.el-slider__runway) {
    background: #2D435E !important;

}

:deep(.el-switch__label.is-active),
:deep(.el-switch__label) {
    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: bold;
    font-style: italic;
    color: #FFFFFF;
}
</style>
