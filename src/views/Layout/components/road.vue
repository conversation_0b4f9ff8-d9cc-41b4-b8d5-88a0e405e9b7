<template>
<div class="road">

    <div class="scene">
        <div class="name">手动控制</div>
        <div @click="clickManual('default')" class=" cursor center">
            <span>第一人称</span>
        </div>
        <div @click="clickManual('third')" class="cursor center">
            <span>第三人称</span>
        </div>
        <div @click="clickManual('fly')" class="cursor center">
            <span>飞行视角</span>
        </div>
    </div>

    <!-- <div>
        <el-switch active-text="手动控制" inactive-text="自动控制" v-model="mode"></el-switch>
    </div> -->

    <div class="lines">
        <div class="name">自动漫游</div>
        <div @click="clickAuto('1')" class="line center cursor">1</div>
        <div @click="clickAuto('2')" class="line center cursor">2</div>
        <div @click="clickAuto('3')" class="line center cursor">3</div>
    </div>

    <div class="speed">
        <div class="name">移动速度</div>
        <el-slider v-model="speed" @change="changeSpeed" :show-tooltip="true" :step="0.1" :min="0" :max="1" />
    </div>

    <div class="close" @click="clickRoad">
        <i class="iconfont iconchahao"></i>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
    inject
} from 'vue'

export default defineComponent({
    setup(props, {
        emit
    }) {
        const state = reactive({
            time: 50,
            mode: true,
            speed:0.5
        });
        const emitter = inject('mitt')

   


        const clickRoad = () => {

            emit('clickRoad')
        }
        const clickAuto = (val) => {
            emitter.miitEmit('ue', {
                type: 'path',
                value:val
            })
        }


        const clickManual = (val) => {
            emitter.miitEmit('ue', {
                type: 'person',
                value:val
            })
        }
        const changeSpeed = (val) => {
            emitter.miitEmit('ue', {
                type: 'speed',
                value:val
            })
        }
        return {
            ...toRefs(state),
            clickRoad,
            clickAuto,
            clickManual,
            changeSpeed
        }
    },
})
</script>

<style lang="scss" scoped>
.road {
    width: 418px;
    height: 327px;
    background: #020912;
    opacity: 0.85;
    border-top: 1px solid #6C9ED1;

    font-size: 16px;
    font-family: "Alibaba-PuHuiTi";
    font-weight: bold;
    font-style: italic;
    color: #FFFFFF;
    display: flex;
    flex-direction: column;

    &>div {
        margin-bottom: 20px;
        padding-left: 10px;
    }

    .close {
        color: white;
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
    }

    .scene {
        margin-top: 60px;
        display: flex;
        align-items: center;

        &>div:nth-of-type(3) {
            margin: 0 13px;
        }

        .name {
            margin-right: 13px;
        }

        & .center {
            height: 30px;
            width: 84px;
            background-image: url("@/views/Layout/img/视角按钮.png");
            background-repeat: no-repeat;
            background-size: contain;

            span {
                font-size: 16px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: bold;
                font-style: italic;
                color: #FFFFFF;
                background: linear-gradient(180deg, #FFFFFF 0%, #8ED6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }

    .lines {
        display: flex;
        align-items: center;

        .name {
            margin-right: 13px;
            white-space: nowrap;
        }

        .line {
            width: 32px;
            height: 32px;
            background: url("@/views/Layout/img/漫游.png") no-repeat;
        }

        .line:nth-of-type(3) {
            margin: 0 13px;
        }

        .line:nth-of-type(1) {
            margin-left: 14px;
        }

    }

    .time {
        width: 60%;
        margin: 0 auto;

    }

    .speed {
        display: flex;
        align-items: center;
        padding-right: 10px;
        .name {
            margin-right: 13px;
            white-space: nowrap;
        }
    }

}

:deep(.el-slider__bar) {
    background: linear-gradient(-90deg, #1E79F2, #1CD3DE);

}

:deep(.el-slider__runway) {
    background: #2D435E !important;
}

:deep(.el-switch__label.is-active),
:deep(.el-switch__label) {
    font-size: 16px !important;
    font-family: "Alibaba-PuHuiTi";
    font-weight: bold;
    font-style: italic;
    color: #FFFFFF;
}

:deep(.el-switch__label *) {
    font-size: 16px !important;
}
</style>
