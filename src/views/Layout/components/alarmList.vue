<template>
    <div class="alarm_list">
        <el-scrollbar class="alarm_scrollbar scrollbar">
            <template v-if="list.length > 0">
                <div v-for="item in list" :key="item.id" class="list" @click="handleCurrentRow(item)">
                    <div class="alarm_name">
                        {{ item.deviceName ? item.deviceName : item.alarmDesc }}
                        <div class="date">
                            <div>
                                {{ getTime(item.createTime) }}
                            </div>

                            <div class="cursor" @click.stop="zoomToPosition(item)">
                                <i class="iconfont icondingwei4 locat"></i>
                            </div>
                        </div>
                    </div>
                    <div class="msg">{{ item.stdName ||item.alarmDesc}}</div>
                    <div class="type"
                        :class="item.alarmLevel == 0 ? 'pt' : item.alarmLevel == 2 ? 'yz' : item.alarmLevel == 1 ? 'jj' : ''">
                        {{ getLevelName(item.alarmLevel) }}
                    </div>
                </div>
            </template>
            <noData v-else></noData>
        </el-scrollbar>
        <div class="center page">
            <el-pagination layout="prev, pager, next" :total="total"  @current-change="handleCurrentChange"/>
        </div>

        <div class="alarm_btn">
            <div class="btn" @click="onClick('stopAudio')">
                <i class="iconfont iconV" :class="isClose ? 'gb' : ''"></i>
                <div class="label">{{ isClose ? '开启' : '关闭' }}声音</div>
            </div>
            <div class="btn" @click="onClick('clearAll')">
                <i class="iconfont iconhuozaibaojing"></i>
                <div class="label">一键消警</div>
            </div>
            <div class="btn" @click="onClick('alarmActive')">
                <i class="iconfont iconshishishuju"></i>
                <div class="label">实时报警</div>
            </div>
            <div class="btn" @click="onClick('alarmRecord')">
                <i class="iconfont iconlishishuju"></i>
                <div class="label">历史报警</div>
            </div>
            <div class="btn" @click="onClick('analysis')">
                <i class="iconfont iconfenxi"></i>
                <div class="label">报警分析</div>
            </div>
            <div class="btn" @click="onClick('isAuto')">
                <el-switch v-model="auto" active-color="#13ce66" inactive-color="#ff4949" width="32">
                </el-switch>
                <div class="label">自动</div>
            </div>
        </div>
    </div>
</template>

<script>

import dayjs from "dayjs";
import {
    getCookie
} from "@/utils/cookie";
import { useAppStore } from "@/stores/app";
import { useMitt } from "@/hooks/mitt";

export default {
    name: 'popalarm',
    props:['total'],
    setup() {
        const emitter = useMitt();
        const store = useAppStore();
        const list = computed(() => {
            let arr = [...store.r_alarm, ...store.h_alarm];
            arr.forEach((a, b) => {
                return dayjs(b.createTime).valueOf - dayjs(a.createTime).valueOf()
            })
            return arr;
        });
        const router = useRouter();
        const api = inject('$api')
        const state = reactive({
            sockets: null,
            levels: [],
            auto: false,
            size:10,
            page:1,
        });
        state.sockets = inject("socket");
        onMounted(() => {

            // if (getCookie("gh_projectId") != "0")
            //     state.sockets.emit(
            //         "SendHisAlarm",
            //         getCookie("gh_projectId"),
            //         getCookie("gh_id")
            //     );
            getLevels()
            getSources()

            let auto = sessionStorage.getItem('auto');
            if (auto && auto == 'true') {
                state.auto = true;
            } else if (auto && auto == 'false') {
                state.auto = false;
            }
        });
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getLevels()
                getSources()
            }
        })
        const isClose = computed(() => {
            return store.isClose
        })


        const getTime = (time) => {
            let showTime = "";
            if (time) {
                showTime = dayjs(time).format("YYYY-MM-DD HH:mm:ss");
            }
            return showTime;
        };
        const getLevels = () => {
            api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: "alarm_level",
            }).then((res) => {
                state.levels = res.data;
            });
        }
        const getSources = () => {
            api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: "alarm_type",
            }).then((res) => {
                state.sources = res.data;
            });
        }
        const getLevelName = (data) => {
            let name = "";
            state.levels.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName;
                    return false;
                }
            });
            return name;
        };
        const handleCurrentRow = (data) => {
            //应急指挥的联动左侧，并在gis中定位
            if (router.path == '/emergency') {
                emitter.miitEmit('alarmEvent', data);
            } else { //其他页面显示报警
                emitter.miitEmit('showLink', data);
            }
        }
        const onClick = (type) => {
            if (type === 'alarmActive') {
                store.SET_DIALOG({
                    name: "实时报警",
                    component: "alarmactive"
                })
                return;
            } else if (type === 'alarmRecord') {
                store.SET_DIALOG({
                    name: "历史报警",
                    component: "alarmrecord"
                })
                return;
            } else if (type === 'analysis') {
                store.SET_DIALOG({
                    name: "报警分析",
                    component: "alarmanalysis"
                })
                return;
            } else if (type == "clearAll") {
                store.SET_ALARM_R([])
                // 一键消警
                state.sockets.emit('ProcessAlarmAll', getCookie('gh_id'), getCookie('gh_projectId'));
                state.sockets.emit(
                    "SendHisAlarm",
                    getCookie("gh_projectId"),
                    getCookie("gh_id")
                );
                emitter.miitEmit('stopAudio', true);
                emitter.miitEmit('ue', {
                    type: 'alarm',
                    data: []
                })
            } else if (type == 'stopAudio') {


                // 关闭声音
                store.SET_CLOSE_ALARM(!isClose.value);
                emitter.miitEmit('stopAudio');
            } else if (type == 'isAuto') {
                sessionStorage.setItem("auto", state.auto ? 'true' : false);
            } else if (type == "close") {

            }
        };

        const zoomToPosition = async (item) => {
            //TODO:定位
            emitter.miitEmit('ue', {
                type: 'alarm',
                data: [item]
            })
        };

        const handleCurrentChange = (page) => {
            state.page = page
            state.sockets.emit(
                "SendHisAlarm",
                getCookie("gh_projectId"),
                page,state.size
            );
        }


        return {
            ...toRefs(state),
            zoomToPosition,
            getTime,
            getLevelName,
            onClick,
            handleCurrentRow,
            list,
            isClose,
            getLevels,
            getSources,
            projectId,
            handleCurrentChange
        };
    }
};
</script>

<style lang="scss" scoped>
.alarm_list {
    top: 60px;
    height: calc(100% - 88px);
    z-index: 999;
    background-image: url("@/assets/images/dialog/bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .gb {
        color: #b4061a !important;
        // cursor: not-allowed;
    }

    .date {
        display: flex;
        justify-content: center;
        align-items: center;

        div:nth-of-type(1) {
            margin-right: 10px;
        }
    }
}
</style>
