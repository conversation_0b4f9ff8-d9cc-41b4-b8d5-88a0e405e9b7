<template>
    <div class="wrapper">
        <div class="left-tree">
            <panel title="设备类型"></panel>
            <div>
                <el-scrollbar class="tree-list tree">
                    <el-tree ref="tree" :data="data" node-key="id" check-strictly :props="props" show-checkbox
                        @check="handleCheckChange" />
                </el-scrollbar>
            </div>

        </div>
        <div class="table-content">
            <el-form :inline="true" class="search_box " size="small">

                <el-form-item label="名称">
                    <el-input v-model="keyword" placeholder="请输入名称查询"></el-input>
                </el-form-item>
                <el-form-item>
                    <div size="small" class="searchBtn" type="text" @click="search">查询</div>
                </el-form-item>
                <el-form-item class="form-item-btn">
                    <div type="primary" size="mini" icon="Plus" class="searchBtn" @click="add">入库</div>
                </el-form-item>
                <el-form-item>
                    <el-upload class="upload" :action="url + '/maintenance-service/spare/upload'" :on-success="importAssets"
                        name="file" :show-file-list="false" :data="param" :headers="headers">
                        <div type="primary" size="mini" icon="Plus" class="searchBtn">导入
                        </div>
                    </el-upload>
                </el-form-item>
            </el-form>

            <el-table :data="list" class="table" height="calc(100% - 68px)" fit @select="select" @select-all="select"  table-layout="auto">
                <!-- <template #empty>
                    <no-data />
                </template> -->
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column prop="name" label="名称" align="center"></el-table-column>
                <el-table-column prop="spareModel" label="型号" align="center" ></el-table-column>
                <el-table-column prop="factory" label="生产商" align="center"></el-table-column>
                <el-table-column prop="price" label="单价" align="center"></el-table-column>
                <el-table-column prop="unit" label="单位" align="center">
                    <template #default="scope">
                        <span>{{ getUnitName(scope.row.unit) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="position" label="库位" align="center"></el-table-column>
                <el-table-column prop="count" label="库存数量" align="center">
                    <template #default="scope">
                        <span style="color: #13d4d9">{{ scope.row.count }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="count" label="操作" align="center">
                    <template #default="scope">
                        <el-button class="editBtn" type="text" @click="addNum(scope.row)">库存</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>
        </div>
        <spare-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getSparePage="getSparePage" />

        <el-dialog align-center :append-to-body="true" draggable top="100px" v-model="dialogNumVisible" title="库存管理"
            custom-class="addDiagram border0" width="700px">
            <el-form ref="form" :model="spare" :rules="rule">
                <el-form-item label="增加库存">
                    <el-input placeholder="请输入数量" size="small" v-model="num"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn" size="small" @click="saveNum">确 定</div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    getCookie
} from '@/utils/cookie'
import Spare from '@/model/spare'
import {
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    onMounted,
    watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import spareDialog from './components/spareDialog.vue'
import { useAppStore } from '@/stores/app'


export default {
    name: 'spare',
    props: ['nav'],
    components: {
        spareDialog
    },
    setup() {
        const tree = ref(null)
        const form = ref(null)
        const selectId = ref([])
        const store = useAppStore()
        const api = inject('$api')
        const state = reactive({
            tableHeight: window.innerHeight * 0.6,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            props: {
                label: 'name',
            },
            keyword: '',
            data: null,
            items: [], //表格select
            spare: new Spare(),
            units: [], // 巡检项数据类型  状态 数值
            selectId: [], //tree check
            url:'',
            param: {
                projectId: getCookie('gh_projectId'),
            },
            headers: {
                Authorization: 'bearer ' + getCookie('gh_token'),
            },
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change',
                },],
                spareModel: [{
                    required: true,
                    message: '型号不能空',
                    trigger: 'change',
                },],
                count: [{
                    required: true,
                    message: '数量不能空',
                    trigger: 'change',
                },
                {
                    type: 'number',
                    message: '请输入正确的数字',
                },
                ],
                position: [{
                    required: true,
                    message: '库位不能空',
                    trigger: 'change',
                },],
                unit: [{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                },],
            },
            dialogNumVisible: false,
            num: null,
            row: null,
            dialogData: {
                visible: false,
                title: '库存管理',
                device: {
                    deviceType: '',
                    deviceModel: '',
                    factory: '',
                }
            }
        })
        onMounted(() => {
            state.url = window.PROD_BASE_API;
            getProjectDeviceType()
            getDicUtilPage()
            getSparePage()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectDeviceType()
                getDicUtilPage()
                getSparePage()
            }
        })
        const getDicUtilPage = () => {
            api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.units = res.data
            })
        }
        const getSparePage = () => {
            api.getSpare({
                deviceType: selectId.value.length > 0 ? selectId.value[0].id : null,
                projectId: getCookie('gh_projectId'),
                keyword: state.keyword,
                page: state.page,
                size: state.size
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getSparePage()
        }
        const getProjectDeviceType = () => {
            api.getDeviceTypeTree({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.data = res.data
            })
        }
        const handleCheckChange = (data, state) => {
            selectId.value = []
            let keys = tree.value.getCheckedKeys()
            if (keys.length == 0 && state.checkedKeys.length == 0) {
                tree.value.setChecked(data.id, false)
            } else {
                if (keys.length > 0)
                    keys.forEach((k) => {
                        if (k != data.id) tree.value.setChecked(k, false)
                    })
                tree.value.setChecked(data.id, true)
                selectId.value.push(data)
            }
            state.page = 1
            getSparePage()
        }
        const add = () => {
            state.dialogData.visible = true
            state.dialogData.title = '库存管理',
                state.dialogData.device = {
                    deviceType: '',
                    deviceModel: '',
                    factory: '',
                }
        }
        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }

        const getUnitName = (value) => {
            let name = ''
            for (let i = 0; i < state.units.length; i++) {
                if (state.units[i].tagValue == value) {
                    name = state.units[i].tagName
                    break
                }
            }
            return name
        }
        const search = () => {
            state.page = 1
            getSparePage()
        }
        const addNum = (row) => {
            state.row = {
                ...row
            }
            state.num = state.row.count
            state.dialogNumVisible = true
        }
        const saveNum = () => {
            if (parseFloat(state.num).toString() == 'NaN') {
                ElMessage({
                    message: '请输入数字',
                    type: 'error',
                })
                return
            }
            if (state.num < state.row.count) {
                ElMessage({
                    message: '不能小于当前库存',
                    type: 'error',
                })
                return
            }
            state.row.count = state.num
            state.row.projectId = getCookie('gh_projectId')

            api.editSpare(state.row).then((res) => {
                getSparePage()
                state.dialogNumVisible = false
            })
        }

        return {
            ...toRefs(state),
            form,
            tree,
            selectId,
            getDicUtilPage,
            getSparePage,
            handleCurrentChange,
            getProjectDeviceType,
            handleCheckChange,
            select,
            add,
            getUnitName,
            search,
            addNum,
            saveNum,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    height: 100%;

    .left-tree {
        background: rgba(16, 52, 87, 0.8) !important;
        width: 377px;
        height: 100%;
        padding: 8px;

        .tree-list {
            // border: 1px solid #2b2e32;
        }

        &>div:last-child {
            height: calc(100% - 40px);
        }
    }

    .table-content {
        // flex: 1;
        height: 100%;
        width: calc(100% - 377px);
    }
}
</style>
