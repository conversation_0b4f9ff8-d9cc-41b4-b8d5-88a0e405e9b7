<template>
<el-dialog align-center :append-to-body="true" draggable custom-class="addDiagram border0" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <panel title="备件信息"></panel>
    <el-form :model="dialogData.device" class="form" ref="form1" label-width="100px">
        <el-row type="flex" gutter="30">
            <el-col :span="12">
                <el-form-item label="设备类型:" prop="deviceType">
                    <el-cascader popper-class="cascader" v-model="dialogData.device.deviceType" style="width: 100%" :options="types" clearable :props="props1" placeholder="请选择"></el-cascader>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="设备型号:">
                    <el-input v-model="dialogData.device.deviceModel" placeholder="请输入设备型号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="生产商:">
                    <el-input v-model="dialogData.device.factory"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
    <panel title="备件添加"></panel>
    <div class="btn-group search_box">
        <div type="primary" size="mini" icon="Plus" class="searchBtn" @click="addSpares">新增</div>
    </div>
    <div class="table">
        <el-table :data="spares" style="width: 100%" height="200px" fit table-layout="auto">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="name" label="设备名称" align="center">
            </el-table-column>
            <el-table-column prop="price" label="备件单价" align="center">
            </el-table-column>
            <el-table-column prop="count" label="备件数量" align="center">
            </el-table-column>
            <el-table-column prop="position" label="备件库位" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" @click="remove(scope.$index, scope.row)" class="del">移除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="save('form1')">保存</div>
        </div>
    </template>

    <el-dialog align-center :append-to-body="true" draggable v-model="dialogSpareVisible" title="新增" custom-class="addDiagram border0" width="500px">
        <el-form ref="addForm" :model="spare" :rules="rule" label-width="80px">
            <el-form-item label="名称" prop="name">
                <el-input placeholder="请输入名称" v-model="spare.name"></el-input>
            </el-form-item>

            <el-form-item label="型号" prop="spareModel">
                <el-input placeholder="请输入型号" v-model="spare.spareModel"></el-input>
            </el-form-item>

            <el-form-item label="生产商">
                <el-input placeholder="请输入生产商" v-model="spare.factory"></el-input>
            </el-form-item>

            <el-form-item label="数量" prop="count">
                <el-input placeholder="请输入数量" v-model.number="spare.count"></el-input>
            </el-form-item>
            <el-form-item label="库位" prop="position">
                <el-input placeholder="请输入库存" v-model="spare.position"></el-input>
            </el-form-item>
            <el-form-item label="单价" prop="price">
                <el-input placeholder="请输入库存" v-model="spare.price"></el-input>
            </el-form-item>

            <el-form-item label="单位" prop="unit">
                <el-select v-model="spare.unit"  class="w100">
                    <el-option v-for="item in units" :label="item.tagName" :value="parseInt(item.tagValue)" :key="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" size="small" class="searchBtn" @click="saveSpare('addForm')">确 定</div>
            </div>
        </template>
    </el-dialog>
</el-dialog>
</template>

<script>
import Spare from '@/model/spare'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import { useAppStore } from '@/stores/app'

export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            device: {
                deviceType: '',
                deviceModel: '',
                factory: '',
            }
        }

    },
    setup(props) {
        const api = inject('$api')
        const addForm = ref(null)
        const store = useAppStore()
        const state = reactive({
            spare: new Spare(),
            dialogSpareVisible: false,
            props1: {
                label: 'name',
                value: 'id',
                checkStrictly: true,
            },
            spares: [],
            types: [],
            units: [],
            list: [],
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change',
                }, ],
                spareModel: [{
                    required: true,
                    message: '型号不能空',
                    trigger: 'change',
                }, ],
                count: [{
                        required: true,
                        message: '数量不能空',
                        trigger: 'change',
                    },
                    {
                        type: 'number',
                        message: '请输入正确的数字',
                    },
                ],
                position: [{
                    required: true,
                    message: '库位不能空',
                    trigger: 'change',
                }, ],
                deviceType: [{
                    required: true,
                    message: '设备类型不能为空',
                    type: 'array',
                    trigger: 'change',
                }, ],
                price: [{
                        required: false,
                        message: '请输入价格',
                        trigger: 'blur'
                    },
                    {
                        pattern: /(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,2})?$)/,
                        message: '请输入正确的价格',
                    },
                ],
                unit: [{
                    required: true,
                    message: '请选择',
                    trigger: 'change',
                }, ],
            },
        })

        onMounted(() => {
            getProjectDeviceType()
            getDicUtil()
        })
        const projectId = computed(() => {
            return store.state.user.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectDeviceType()
                getDicUtil()
            }
        })
        const getDicUtil = () => {
            api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie('gh_projectId'),
            }).then(res => {
                state.units = res.data
            })
        }
        const getProjectDeviceType = () => {
            api.getDeviceTypeTree({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.types = res.data
            })
        }
        const addSpares = () => {
            state.spare = new Spare()
            state.dialogSpareVisible = true
            nextTick(() => {
                addForm.value.resetFields()
            })
        }
        const saveSpare = (formName) => {
            proxy.$refs[formName].validate((validate) => {
                if (validate) {
                    state.spares.push({
                        ...state.spare,
                        projectId: getCookie('gh_projectId'),
                    })
                    state.dialogSpareVisible = false
                }
            })
        }

        const remove = (index, row) => {
            state.spares.splice(index, 1)
        }
        const save = (formName) => {
            proxy.$refs[formName].validate((validate) => {
                const {
                    deviceType,
                    deviceModel,
                    factory
                } = props.dialogData.device
                if (validate) {
                    api.addSpare({
                        deviceType: deviceType.length > 0 ? deviceType[deviceType.length - 1] : null,
                        deviceModel: deviceModel.length > 0 ?
                            deviceModel[deviceModel.length - 1] : null,
                        factory,
                        spares: state.spares,
                    }).then((res) => {
                        if (res.success) {
                            props.dialogData.visible = false
                            proxy.$emit('getSparePage')
                            ElMessage({
                                type: 'success',
                                message: res.msg
                            })
                        }
                    })
                }
            })
        }

        return {
            ...toRefs(state),
            addForm,
            getProjectDeviceType,
            addSpares,
            saveSpare,
            remove,
            save,
            projectId,
            getDicUtil
        }
    }
}
</script>

<style lang="scss" scoped>
.form,
.btn-group {
    margin-top: 20px;
}
</style>
