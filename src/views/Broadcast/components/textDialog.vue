<template>
<el-dialog align-center :append-to-body="true" draggable custom-class="addDiagram border0" :title="dialogData.title" v-model="dialogData.visible" width="940px">
    <el-steps :active="dialogData.milepostActive" finish-status="success">
        <el-step v-for="(item,i) in dialogData.milepost" :key="i" :title="item.title"></el-step>
    </el-steps>
    <el-scrollbar>
        <div class="h" v-if="dialogData.milepostActive==1">
            <div class="tree">
                <el-tree check-on-click-node="true" :data="data" show-checkbox node-key="endpointID" :props="props" ref="tree" @check="getCheck">
                </el-tree>
            </div>

        </div>
        <div class="h" v-if="dialogData.milepostActive==2">
            <el-form v-if="dialogData.milepost[1].title=='输入文本'" class="form" ref="textRuleForm" label-width="100px" :model="textForm" :rules="textRule">
                <el-form-item label="文本描述：" prop="text">
                    <el-input placeholder="请输入内容" type="textarea" v-model="textForm.text"></el-input>
                </el-form-item>
                <el-row type="flex" justify="space-between">
                    <el-col :span="12">
                        <el-form-item label="语速：" prop="speed">
                            <el-input-number v-model="textForm.speed" controls-position="right" :min="1" :max="100">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="重复次数：" prop="times">
                            <el-input-number v-model="textForm.times" controls-position="right" :min="1" :max="100">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tree" v-if="dialogData.milepost[1].title=='选择音乐'">
                <el-tree check-on-click-node="true" :data="musicData" ref="treeMusic" show-checkbox node-key="audioId" :props="defaultProps" @check="getCheckMusic">
                </el-tree>
            </div>
        </div>
        <div class="h" v-if="dialogData.milepostActive==3">
            <el-form :model="form" :rules="rules" ref="ruleForm" label-width="100px" class="form">
                <!-- <el-form-item label="任务名称：" prop="taskName">
                    <el-input v-model="form.taskName" placeholder="请输入任务名称"></el-input>
                </el-form-item> -->
                <el-row type="flex" justify="space-between">
                    <el-col :span="12">
                        <el-form-item label="优先级：" prop="priority">
                            <el-input-number class="w100" v-model="form.priority" controls-position="right" :min="1" :max="100">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="音量：" prop="volume">
                            <el-input-number v-model="form.volume" controls-position="right" :min="0" :max="100">
                            </el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="任务名称：" prop="taskName">
                    <el-input v-model="form.taskName" placeholder="请输入名称"></el-input>
                </el-form-item>
                <el-form-item label="播放模式：" prop="mode" v-if="dialogData.milepost[1].title == '选择音乐'">
                    <el-select v-model="form.mode" placeholder="请选择">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
    </el-scrollbar>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" v-if="dialogData.milepostActive !== 3" @click="next('textRuleForm')">下一步
            </div>
            <div type="primary" class="searchBtn" size="small" v-if="dialogData.milepostActive== 3" @click="save('ruleForm')">保存
            </div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    
    onMounted,
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            milepostActive: 1,
            milepost: [],
        }
    },
    setup(props) {
        const api = inject('$api')
        const tree = ref(null)
        const treeMusic = ref(null)
        const state = reactive({
            form: {
                taskName: '',
                mode: '',
                volume: 10,
                priority: 500
            },
            rules: {
                taskName: [{
                    required: true,
                    message: '请输入任务名称',
                    trigger: 'blur'
                }],
                priority: [{
                    required: true,
                    message: '请输入优先级',
                    trigger: 'blur'
                }],
                volume: [{
                    required: true,
                    message: '请输入音量',
                    trigger: 'blur'
                }],
                mode: [{
                    required: true,
                    message: '请选择播放模式',
                    trigger: 'change'
                }]
            },
            musicData: [],
            data: [],
            tids: [], // 终端id
            mids: [], //音乐id
            props: {
                label: 'endpointName',
            },
            defaultProps: {
                label: 'name',
                children: 'musics'
            },
            options: [{
                value: 'normal_mode',
                label: '常规播放'
            }, {
                value: 'list_cycle_mode',
                label: '列表循环'
            }, {
                value: 'random_mode',
                label: '随机播放'
            }],
            textForm: {
                text: '',
                speed: 1,
                times: 1
            },
            textRule: {
                text: [{
                    required: true,
                    message: '请输入文本描述',
                    trigger: 'blur'
                }],
                speed: [{
                    required: true,
                    message: '请输入语速',
                    trigger: 'blur'
                }],
                times: [{
                    required: true,
                    message: '请输入次数',
                    trigger: 'blur'
                }],
            }
        })
        onMounted(() => {
            getTerminalPage()
            getMusicPage()
        })
        const next = (formName) => {
            if (props.dialogData.milepostActive == 1) {
                if (state.tids.length == 0) {
                    ElMessage({
                        type: 'warning',
                        message: '请选择终端'
                    })
                    return
                } else {
                    props.dialogData.milepostActive++
                }
            } else
            if (props.dialogData.milepostActive == 2) {
                if (props.dialogData.milepost[1].title == '选择音乐') {
                    if (state.mids.length == 0) {
                        ElMessage({
                            type: 'warning',
                            message: '请选择音乐'
                        })
                        return
                    } else {
                        props.dialogData.milepostActive++
                    }
                } else if (props.dialogData.milepost[1].title == '输入文本') {
                    proxy.$refs[formName].validate(valid => {
                        if (valid) {
                            props.dialogData.milepostActive++
                        }
                    })
                }
            }
            if (props.dialogData.milepostActive > 3) props.dialogData.milepostActive = 1
        }
        const getTerminalPage = () => {
            api.getTerminal().then(res => {
                state.data = res.data
            })
        }
        const getCheck = () => {
            state.tids = tree.value.getCheckedKeys()
        }
        const getCheckMusic = () => {
            state.mids = treeMusic.value.getCheckedKeys()
        }
        const getMusicPage = () => {
            api.getITCMusic().then(res => {
                state.musicData = res.data
            })
        }
        const save = (formName) => {
            proxy.$refs[formName].validate((validate) => {
                if (validate) {
                    if (props.dialogData.milepost[1].title == '选择音乐') {
                        api.postMusicTask({
                            mids: state.mids,
                            mode: state.form.mode,
                            priority: state.form.priority,
                            volume: state.form.volume,
                            tids: state.tids,
                            taskName: state.form.taskName
                        }).then(res => {
                            if (res.success) {
                                props.dialogData.visible = false
                                proxy.$emit('getTaskPage')
                                ElMessage({
                                    type: 'success',
                                    message: '创建成功'
                                })
                            }
                        })
                    } else if (props.dialogData.milepost[1].title == '输入文本') {
                        api.getTaskTTSTask({
                            priority: state.form.priority,
                            speed: state.textForm.speed,
                            taskName: state.form.taskName,
                            text: state.textForm.text,
                            tids: state.tids,
                            times: state.textForm.times,
                            volume: state.form.volume
                        }).then(res => {
                            if (res.success) {
                                props.dialogData.visible = false
                                ElMessage({
                                    type: 'success',
                                    message: '创建成功'
                                })
                            }
                        })
                    }

                } else {
                    return false
                }
            })

        }
        return {
            ...toRefs(state),
            tree,
            treeMusic,
            next,
            getTerminalPage,
            getCheck,
            getMusicPage,
            getCheckMusic,
            save
        }
    }
}
</script>

<style lang="scss" scoped>
.h {
    display: flex;
    flex-direction: column;
    margin: 10px 0;
    height: 240px;

    .form {
        width: 600px;
        margin: 0 auto;
    }

    .tree {
        flex: 1;
    }
}

:deep(.el-input-number) {
    width: 100%;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
    background: transparent !important;
    border-color: #dcdfe626 !important;
}
</style>
