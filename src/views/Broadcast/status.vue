<template>
<div class="h100 gallery">
    <div class="content">
        <el-scrollbar>
            <div class="status_list">
                <div v-for="(item,index) in list" :key="index" class="item">
                    <div class="icon">
                        <img :src="icon" alt="">
                    </div>
                    <div class="info">
                        <div class="name">{{item.endpointName}}</div>
                        <div class="ip">{{item.endpointIP}}</div>
                        <div class="volume">音量：{{item.volume}}</div>
                        <div class="status">
                            <div class="list-title" v-if="item.status==1">
                                <div class="title-text">在线</div>
                                <div>
                                    <img :src="run" alt />
                                </div>
                            </div>
                            <div class="list-title" v-if="item.status==0">
                                <div class="title-text">离线</div>
                                <div>
                                    <img :src="offLine" alt />
                                </div>
                            </div>
                            <div class="list-title" v-if="item.status==2">
                                <div class="title-text">占用</div>
                                <div>
                                    <img :src="zy" alt />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-scrollbar>
    </div>

</div>
</template>

<script>
import {
    reactive,
    ref,
    toRefs,
    
    onMounted
} from "vue";

export default {
    name: 'Bstatus',
    setup() {
        const api = inject('$api');
        const state = reactive({
            icon: new URL('@/assets/images/terminal.png', import.meta.url).href,
            run: new URL("@/assets/images/normal.png", import.meta.url).href,
            offLine: new URL("@/assets/images/offLine.png", import.meta.url).href,
            zy: new URL('@/assets/images/abnormal.png', import.meta.url).href,
            list: [{
                name: '终端1',
                vol: 20,
                status: 1,

            }],
        });
        onMounted(() => {
            getTerminalPage();
        });
        const getTerminalPage = () => {
            api.getTerminal().then(res => {
                state.list = res.data
            })
        }
        return {
            ...toRefs(state),
            getTerminalPage
        };
    },
};
</script>

<style lang="scss" scoped>
.gallery {
    .content {
        height: 100%;
        background: rgba(16, 52, 87, 0.8) !important;
        .status_list {
            display: flex;
            width: 100%;
            flex-wrap: wrap;
            .item {
                width: 250px;
                display: flex;
                align-items: center;
                margin: 0 4px;
                background: rgba(47, 54, 60, 0.3);
                border: 1px solid #4a5966;
                margin-bottom: 8px;
                padding: 10px;

                .icon {
                    width: 108px;
                    height: 108px;
                    background: #ffffff;
                    margin-right: 10px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .info {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .name {
                        font-size: 16px;
                        font-family: "PingFangSC-Medium", "PingFang SC";
                        font-weight: 500;
                        color: #ffffff;
                        margin-bottom: 6px;
                    }

                    .volume,
                    .ip {
                        font-size: 14px;
                        font-family: "Alibaba-PuHuiTi";
                        font-weight: 400;
                        color: #9ca4b7;
                    }

                    .status {
                        display: flex;
                        text-align: center;

                        .list-title {
                            width: 40px;

                            .title-text {
                                font-size: 12px;
                                line-height: 28px;
                                font-family: "Alibaba-PuHuiTi";
                                font-weight: 400;
                                color: #687287;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
