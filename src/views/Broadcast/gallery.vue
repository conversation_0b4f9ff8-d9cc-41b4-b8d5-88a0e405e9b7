<template>
<div class="layout_wrapper gallery">
    <div class="left">
        <sub-title2 title="音乐列表" />
        <div class="btn-group">
            <el-button type="primary" icon="Plus" size="mini" class="addBtn" @click="add">新增</el-button>
        </div>
        <el-scrollbar class="tree">
            <el-tree ref="tree" check-strictly @node-click="handleCheckChange" :data="treeData" :props="defaultProps">
                <template #default="{ data }">
                    <span class="custom-tree-node">
                        <span> {{ data.name }}</span>
                        <span v-if="data.dirName">
                            <el-button icon="el-icon-upload2" type="text" size="mini" class="uploadBtn" @click="uploadFile(data)">上传
                            </el-button>
                        </span>
                    </span>
                </template>
            </el-tree>
        </el-scrollbar>
    </div>
    <div class="right">
        <sub-title2 title="媒体库详情" />
        <div class="info" v-if="details.audioId">
            <div class="tit">音乐名称: <span class="val">{{details.name}}</span></div>
            <div class="tit">音乐时长：<span class="val">{{details.duration}}秒</span></div>
        </div>
        <div class="info" v-else>
            <div class="tit">媒体库名称: <span class="val">{{details.dirName}}</span></div>
            <div class="tit">音乐总时长：<span class="val">{{details.dirAudioTime}}秒</span></div>
        </div>
    </div>
    <el-dialog align-center custom-class="custom_dialog" width="482px" title="上传" v-model="dialogFormVisible">
        <el-upload :action="action" :http-request="fileUploadHandler" :multiple="false" :show-file-list="false">
            <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
    </el-dialog>
    <el-dialog align-center custom-class="custom_dialog" width="482px" title="新增" v-model="visible">
        <el-form :model="addForm">
            <el-form-item label="媒体库名称：">
                <el-input v-model="addForm.name" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="是否公开：">
                <el-radio-group v-model="addForm.is_public">
                    <el-radio :label="1">公开</el-radio>
                    <el-radio :label="0">隐藏</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" class="saveBtn" size="small" @click="saveMusic">确 定</el-button>
            </span>
        </template>
    </el-dialog>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    
    onMounted
} from "vue";
import {
    getCookie
} from '@/utils/cookie';
import {
    ElMessage
} from 'element-plus';
import axios from 'axios';
export default {
    name: 'Bgallery',
    setup() {
        const api = inject('$api');
        const state = reactive({
            action: "",
            treeData: [],
            details: {},
            dialogFormVisible: false,
            visible: false,
            defaultProps: {
                children: "musics",
                label: "name",
            },
            addForm: {
                name: '',
                is_public: 0
            },
            dirID: ''
        });
        onMounted(() => {
            getMusicPage()
        })
        const getMusicPage = () => {
            api.getMusic().then((res) => {
                state.treeData = res.data
            });
        };
        // 上传
        const uploadFile = (data) => {
            state.dialogFormVisible = true
            state.dirID = data.dirID
        }
        const handleCheckChange = (data) => {
            state.details = Object.assign({}, data)
        }
        const fileUploadHandler = (data) => {
            const {
                file
            } = data
            const formData = new FormData()
            formData.append('file', file)
            axios({
                method: "post",
                url: process.env.VUE_APP_DEV_BASE_API + `itc-service/upload/${state.dirID}`,
                data: formData,
                headers: {
                    "Content-Type": "multipart/form-data",
                    Authorization: "bearer " + getCookie('gh_token')
                }
            }).then(res => {
                if (res.data.success) {
                    state.dialogFormVisible = false
                    ElMessage({
                        type: 'success',
                        message: '上传成功'
                    })
                    getMusicPage()
                }
            })
        }
        const add = () => {
            state.visible = true
        }
        const saveMusic = () => {
            api.addMusic({
                name: state.addForm.name,
                is_public: state.addForm.is_public
            }).then(res => {
                if (res.success) {
                    state.visible = false
                    ElMessage({
                        type: 'success',
                        message: '添加成功'
                    })
                    getMusicPage()
                }
            })
        }
        return {
            ...toRefs(state),
            getMusicPage,
            uploadFile,
            handleCheckChange,
            fileUploadHandler,
            add,
            saveMusic
        };
    },
};
</script>

<style lang="scss" scoped>
.gallery {
    display: flex;
    padding: 0 15px;

    .left {
        width: 369px;
        margin-right: 15px;

        .tree {
            padding: 8px;
            border: 1px solid #2b2e32;
            height: calc(100% - 150px);
        }
    }

    .right {
        width: 369px;

        .info {
            font-size: 16px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: 400;
            color: #889cc3;
            padding: 10px 18px;

            .tit {
                line-height: 32px;

                .val {
                    color: #c7dfff;
                }
            }
        }
    }
}
</style>
