<template>
<el-dialog align-center :append-to-body="true" :show-close="false" custom-class="panel_dialog" draggable v-model="panelVisibale" width="800px">
    <template #header>
        <div class="header">
            <div>{{title}}</div>
            <img @click="close" class="cursor" src="../img/panel2.png" />
        </div>
    </template>
    <el-tabs v-model="activeName" @tab-change="handleTabClick">
        <el-tab-pane v-if="panel.includes('real')" label="实时数据" name="real">
            <div class="history">

                <div class="list-item" v-for="(item, index) in sData" :key="index">
                    <div class="text">
                        {{ item.name }}
                    </div>
                    <div>
                        <i :class="icon.icon" :style="{ color: icon.color }" v-show="condition(panelData[item.id], icon.factor, icon.value)" v-for="(icon, i) in item.conditions" :key="i"></i>
                    </div>
                </div>
                <div class="list-item" v-for="(item,index) in sInputData" :key="index">
                    <div class="list-item-icon">{{ item.name }}</div>
                    <div> {{ panelData[item.id] }}{{ getUnit(item.unit) }}</div>
                </div>
                <div class="list-item" v-for="(item, index) in sOutData" :key="index">
                    {{ item.name }}：
                    <el-slider class="yangan" @change="changeSlider($event, item.variable)" :min="item.min" :max="item.max" v-model="value2">
                    </el-slider>
                </div>

                <div class="list-item" v-if="numData.length>0">
                    <div> {{ numData[0].name }}：</div>
                    <div class="btn_center">
                        <div>
                            <i class="iconfont icontingzhi2-copy" @click="writeValue(numData[0].variable, 1)"></i>
                        </div>
                        <div>
                            <i class="iconfont iconbofang2" @click="writeValue(numData[0].variable, 0)"></i>
                        </div>
                    </div>
                </div>

            </div>

        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('history')" label="历史数据" name="history">
            <el-form :inline="true" class="search_sub" size="small">
                <el-form-item label="时间选择">
                    <el-date-picker :popper-class="search_picker" v-model="startEndDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="getHistory"></el-date-picker>
                </el-form-item>
            </el-form>
            <div class="history" v-if="activeName=='history'">
                <historyChart :historyData="historyData" class="h100" />
            </div>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('device_history')" label="历史数据" name="device_history">
            <el-form :inline="true" class="search_sub" size="small">
                <el-form-item label="时间选择">
                    <el-date-picker :popper-class="search_picker" v-model="startEndDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="getDeviceHistory"></el-date-picker>
                </el-form-item>
                <el-form-item label="指标">
                    <el-select size="small" @change="changeStd" v-model="select_std" clearable filterable popper-class="panel_select">
                        <el-option v-for="(item,i) in std" :label="item.name" :value="item.variable" :key="i"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="history" v-if="activeName=='device_history'">
                <historyChart :historyData="historyData" class="h100" />
            </div>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('energy')" label="当日能耗" name="energy">
            <div class="history">
                <bar :chartData="chartData" />
            </div>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('device')" label="设备信息" name="device">
            <device :deviceId="config.deviceId"></device>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('patrol')" label="巡检信息" name="patrol">
            <patrol :deviceId="config.deviceId"></patrol>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('maintenance')" label="维保记录" name="maintenance">
            <maintenance :deviceId="config.deviceId"></maintenance>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('repair')" label="报修信息" name="repair">
            <repair :deviceId="config.deviceId"></repair>
        </el-tab-pane>
        <el-tab-pane v-if="panel.includes('alarm')" label="报警记录" name="alarm">
            <alarm :deviceId="config.deviceId"></alarm>
        </el-tab-pane>
    </el-tabs>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs,
    defineComponent,
    
    inject,
    nextTick,
    computed
} from "vue";
import historyChart from '@/components/echarts/historyChart.vue'
import bar from '@/components/energy/bar.vue'
import {
    getCookie
} from "@/utils/cookie"
import socket from '@/utils/socket'
import dayjs from 'dayjs'
import device from './device.vue'
import repair from './repair.vue'
import maintenance from './maintenance.vue'
import patrol from './patrol.vue'
import alarm from './alarm.vue'
import { useAppStore } from "@/stores/app";

export default defineComponent({
    sockets: {
        live(res) {
            this.process(res)
        },
        onVarsChangedCallback(res) {
            this.process(res)
        },
    },
    components: {
        historyChart,
        bar,
        device,
        repair,
        maintenance,
        patrol,
        alarm
    },
    setup(props) {
        const api = inject('$api');

        const state = reactive({
            panelVisibale: false,
            activeName: "real",
            historyData: {
                time: [],
                data: []
            }, //历史数据
            startEndDate: [],
            iconClickConfig: null, //点击图标时候得config
            chartData: {
                xAxis: [],
                data: []
            },
            units: null,
            config: null,
            title: null,
            sockets: null,
            panelData: {},
            sData: [], //状态数据
            sInputData: [], //模拟输入
            sOutData: [], //模拟输出
            numData: [], //数字输出 控制
            std: [], //设备指标集合
            select_std: null,
        })
        state.sockets = inject('socket')
        const store = useAppStore();
        const panel = computed(() => {
            let menu = getCookie('funMenus');
            let active = store.funMenus || (menu ? JSON.parse(menu) : '')
            if (active.panel) {
                return JSON.parse(active.panel);
            }
            return [];
        });

        state.startEndDate.push(dayjs().format('YYYY-MM-DD 00:00:00'))
        state.startEndDate.push(dayjs().format('YYYY-MM-DD 23:59:59'))
        const show = (config) => {
            if (!panel.value.includes("real") && panel.value.length > 0) {
                state.activeName = panel.value[0];
            }
            state.config = config;
            state.title = config.deviceName || config.name
            state.panelVisibale = true;
            openPanel(config.deviceId, config.deviceCode)
        }
        const openPanel = (deviceId, code) => {
            if (deviceId && panel.value.includes("real")) {
                getStd(deviceId)
            }
        }
        const getHistory = () => {
            let deviceId = state.config.deviceId;
            if (state.config.deviceCode && state.config.deviceCode.startsWith("energy_")) {
                deviceId = state.config.deviceCode.split("_")[1];
            }
            api.getHistoryEnergy({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                deviceId: deviceId,
            }).then((res) => {
                state.historyData = {
                    time: [],
                    data: []
                };
                state.historyData.time = res.data.map((v, i) => {
                    return v.time
                })
                state.historyData.data = res.data.map((e, j) => {
                    return e.value
                })
                state.historyData.name = ""
            })
        }
        const getDeviceEnergyById = async () => {
            state.chartData = {
                xAxis: [],
                data: []
            }
            for (let i = 0; i < 24; i++) {
                state.chartData.xAxis.push(i + '时');
            }
            if (state.config.deviceCode ?.startsWith("energy_")) {
                const {
                    data
                } = await api.getDeviceEnergyById({
                    bt: dayjs().format('YYYY-MM-DD 00:00:00'),
                    et: dayjs().format('YYYY-MM-DD 23:59:59'),
                    id: state.config.deviceCode.split("_")[1],
                })
                state.chartData.data = data;
            }

        }
        const getStd = (deviceId) => {
            api.getDeviceStandard({
                deviceId,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                res.data.forEach((d, i) => {
                    d.standardParams.forEach((p, j) => {
                        if (p.dataType == 'num_input' && p.paramKey == 'Value') {
                            //状态 当前值
                            state.sData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                conditions: p.config ? JSON.parse(p.config) : {},
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_input' && p.paramKey == 'Value') {
                            //模拟输入
                            state.sInputData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                unit: p.unit,
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_out' && p.paramKey == 'Value') {
                            //模拟输出
                            state.sOutData.push({
                                name: d.name,
                                min: parseInt(p.min),
                                max: parseInt(p.max),
                                variable: d.variable,
                            })
                        }
                        if (p.dataType == 'num_out' && p.paramKey == 'Value') {
                            //数字输出
                            state.numData.push({
                                name: d.name,
                                conditions: p.conditions,
                                variable: d.variable,
                            })
                        }
                    })
                })
            })
        }
        const handleTabClick = async (data) => {
            await nextTick()
            if (data == "energy") {
                getDeviceEnergyById();
            } else if (data == "history") {
                getHistory();
            } else if (data == 'device_history') {
                getDeviceParams();
            }
        }
        const close = () => {
            state.panelVisibale = false;
            state.panelData = {}
            state.sData = []
            state.sInputData = []
            state.sOutData = []
            state.numData = []
            socket.unsubscribe(state.sockets, 'panel', 'real')
        }
        const writeValue = (variable, val) => {
            socket.writeValue(state.sockets, variable, val, getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const changeSlider = (val, variable) => {
            socket.writeValue(state.sockets, variable, val, getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const panelSubscribe = (variable, id) => {
            if (variable) {
                let v = variable.split(':')
                let item = {
                    id: id,
                    iosvrKey: v[0],
                    chlKey: v[1],
                    ctrlKey: v[2],
                    varKey: v[3],
                    realTime: false,
                }
                socket.subscribe(state.sockets, 'real', 'panel', [item])
                state.panelData = Object.assign({}, state.panelData, {
                    [id]: 0,
                })
            }
        }
        const process = (res) => {

            if (res) {
                let data = JSON.parse(res)
                if (data.batchDefinitionId == 'real' && data.clientId == 'panel') {
                    data.data.forEach((d) => {
                        if (d.id.startsWith('s_')) {
                            state.panelData[d.id] = d.value
                        }
                    })
                }
            }
        }
        const getUnit = (id) => {
            let name = ''
            if (id) {
                for (let u of state.units) {
                    if (u.tagValue == id) {
                        name = u.tagName
                        break
                    }
                }
            }
            return name
        }
        const condition = (value, factor, v) => {
            if (!value) value = 0
            if (eval(value + factor + v)) return true
            return false
        }
        const getDicUtil = () => {
            api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.units = res.data
            })
        }
        const getDeviceParams = () => {
            api.getDeviceStandard({
                deviceId: state.config.deviceId,
            }).then((res) => {
                state.std = res.data
                if (res.data.length > 0) {
                    state.select_std = res.data[0].variable
                    getDeviceHistory()
                }
            })
        }

        const getDeviceHistory = () => {
            api.getHistoryData({
                bt: dayjs(state.startEndDate[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.startEndDate[1]).format("YYYY-MM-DD HH:mm:ss"),
                keyword: state.select_std,
            }).then((res) => {
                state.historyData = {
                    time: [],
                    data: []
                };
                state.historyData.time = res.data.histories.map((v, i) => {
                    return v.time
                })
                state.historyData.data = res.data.histories.map((e, j) => {
                    return e.value
                })
            })
        }

        const changeStd = (val) => {
            if (val) {
                getDeviceHistory(val);
            } else {
                state.historyData = {
                    time: [],
                    data: []
                };
            }
        }

        getDicUtil();

        return {
            ...toRefs(state),
            show,
            openPanel,
            handleTabClick,
            close,
            writeValue,
            condition,
            changeSlider,
            panelSubscribe,
            process,
            getUnit,
            panel,
            getDeviceParams,
            changeStd

        }
    }
})
</script>

<style lang="scss" scoped>
.panel_dialog {
    .header {
        background: url("../img/panel1.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .history {
        height: 400px;
        width: 100%;
    }
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #889cc3;
    padding: 0 20px;
    border-bottom: 1px solid #3a3b3d;
    margin-bottom: 15px;
}

.btn_center{
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>>
