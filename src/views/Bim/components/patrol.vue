<template>
<div>
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="时间">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="search">
            </el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button @click="search" class="searchBtn" size="small" type="text">查询</el-button>
        </el-form-item>
    </el-form>
    <div class="table">
        <el-table height="400" :data="list" style="width: 100%" fit table-layout="auto">
            <el-table-column prop="startTime" label="计划开始时间" align="center">
            </el-table-column>
            <el-table-column prop="endTime" label="计划结束时间" align="center">
            </el-table-column>
            <!-- <el-table-column prop="times" label="计划次数" align="center">
            </el-table-column> -->
            <el-table-column prop="planName" label="计划名称" align="center">
            </el-table-column>
            <el-table-column prop="logTime" label="巡检时间" align="center">
            </el-table-column>
            <el-table-column prop="events" label="巡检标准" align="center"  >
            </el-table-column>
            <el-table-column prop="username" label="巡检人员" align="center">
            </el-table-column>
            <el-table-column prop="pointName" label="巡检点" align="center">
            </el-table-column>
            <el-table-column prop="num" label="巡检次数" align="center">
            </el-table-column>
            <el-table-column prop="delay" label="巡检结果" align="center">
                <template #default="props">
                    <span style="color:green">{{getState(props.row)}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="page center">
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
</div>
</template>

<script>

import {
    defineComponent,
    
    reactive,
    toRefs
} from "vue";
import dayjs from 'dayjs'
export default defineComponent({
    props:['deviceId'],
    setup(props) {
        const state = reactive({
            date: [],
            list: null,
            total: 0,
            size: 10,
            page: 1,
        })
        const api = inject('$api')
        state.date.push(dayjs(dayjs().format('YYYY-MM-DD 00:00:00')))
        state.date.push(dayjs(dayjs().format('YYYY-MM-DD 23:59:59')))
        const getState = (item) => {
            let name = "已巡检";
            if (item.logTime == null && dayjs().isAfter(item.endTime)) {
                name = "遗漏";
            } else if (item.delay) {
                name = "超时";
            } else if (item.logTime == null && dayjs().isBefore(dayjs(item.endTime))) {
                name = "未巡检";
            }
            return name;
        }
        const getPatrolRecord = () => {
            api.getDeviceSecurityDetailRecord({
                page: state.page,
                size: state.size,
                deviceId: props.deviceId,
                type: 1,
                bt: typeof state.date[0] == 'string' ? state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ? state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then(res => {
                state.list = res.data;
                state.total = res.total;
            });

        }
        const handleCurrentChange = (page) => {
            state.Pagination.page = page
            getPatrolRecord()
        }
        const search = () => {
            state.page = 1
            getPatrolRecord()
        }

        getPatrolRecord()

        return {
            ...toRefs(state),
            getState,
            handleCurrentChange,
            search
        }
    }
})
</script>

<style scoped>

</style>
