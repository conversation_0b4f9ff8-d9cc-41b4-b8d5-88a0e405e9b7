<template>
<div>
    <div class="table">
        <el-form :inline="true" class="search_box form_inline" size="small">
            <el-form-item label="时间">
                <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="search">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button @click="search" class="searchBtn" size="small" type="text">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="list" :height="400"  fit table-layout="auto">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="code" label="报修编号" align="center">
            </el-table-column>
            <el-table-column prop="name" label="工单编号" align="center">
            </el-table-column>
            <el-table-column prop="areaName" label="所属区域" align="center">
            </el-table-column>
            <el-table-column prop="deviceName" label="设备名称" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center">
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span style="color:green">{{getStatus(scope.row.status)}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="repairPerson" label="报修人" align="center">
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" align="center">
            </el-table-column>
        </el-table>
        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="Pagination.size" :current-page="Pagination.page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
            </el-pagination>
        </div>
    </div>
</div>
</template>

<script>
import {
    getCookie
} from "@/utils/cookie";
import dayjs from 'dayjs'
import {
    defineComponent,
    
    reactive,
    toRefs
} from "vue";

export default defineComponent({
    props:['deviceId'],
    setup(props) {
        const state = reactive({
            list: [],
            date: [],
            Pagination: {
                page: 1,
                size: 10,
                total: 0
            },
            status: [{
                    value: 1,
                    name: '待处理'
                },
                {
                    value: 2,
                    name: '处理中'
                },
                {
                    value: 3,
                    name: '已结案'
                }

            ],
        })
        const api = inject('$api')
        state.date.push(dayjs(dayjs().format('YYYY-MM-DD 00:00:00')))
        state.date.push(dayjs(dayjs().format('YYYY-MM-DD 23:59:59')))

        const handleCurrentChange = (page) => {
            state.Pagination.page = page
            getHistroyRecordPage()
        }

        const getFaultPage = () => {
            api.getFault({
                projectId: getCookie('gh_projectId'),
                page: state.page,
                size: state.size,
                deviceId: props.deviceId,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.Pagination.total = res.total
            })
        }
        const search = () => {
            state.Pagination.page = 1
            getFaultPage()
        }
        const getStatus = (val) => {
            let name = "";
            state.status.forEach(t => {
                if (t.value == val) {
                    name = t.name
                }
            });
            return name;
        }
        getFaultPage()

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            getStatus
        }
    }
})
</script>

<style scoped>
    
    </style>
