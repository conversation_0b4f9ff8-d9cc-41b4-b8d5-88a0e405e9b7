<template>
<el-dialog align-center :append-to-body="true" @close="closeDiagram" modal="false" :title="diagramName" v-model="viewVisibale" width="800px" custom-class="cctv-dialog">
    <iframe :src="viewUrl" style="height:500px"></iframe>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs,defineComponent
} from "vue";

export default defineComponent({
    setup(props) {
        const state = reactive({
            viewUrl: null,
            diagramName:null,
            viewVisibale:false,
        });
        const show = (url, name) => {
            state.viewVisibale=true;
            state.viewUrl = url;
            state.diagramName = name;
        }
        const closeDiagram = () => {
            state.viewUrl = "";
            state.viewVisibale = false;
        }
        return {
            ...toRefs(state),
            show,
            closeDiagram
        }
    }
})
</script>

<style>

</style>
