<template>
<div>
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="时间选择">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button @click="getProcessLogPage" size="small" class="searchBtn" type="text">查询</el-button>
        </el-form-item>
    </el-form>
    <div class="table">
        <el-table :data="list" :height="400"  fit table-layout="auto">
            <template #empty>
                <noData />
            </template>
            <el-table-column prop="deviceName" label="维保设备" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="维保时间" align="center">
            </el-table-column>
            <el-table-column prop="creatorName" label="维保人员" align="center">
            </el-table-column>
            <el-table-column prop="description" label="维保内容" align="center">
            </el-table-column>
            <el-table-column prop="nextTime" label="下次维保" align="center">
            </el-table-column>
        </el-table>
    </div>
    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs
} from "vue";
import dayjs from 'dayjs'
import { getCookie } from "@/utils/cookie";
export default defineComponent({
    props:['deviceId'],
    setup(props) {
        const state = reactive({
            total: 0,
            size: 10,
            page: 1,
            list: [],
            date: []
        })
        const api = inject('$api')

        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
        state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))

        const getProcessLogPage = () => {
            api.getProcessLog({
                projectId: getCookie("gh_projectId"),
                page: state.page,
                size: state.size,
                deviceId: props.deviceId,
                type: 3,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD 00:00:00'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD 23:59:59'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getProcessLogPage()
        }

        getProcessLogPage();

        return {
            ...toRefs(state),
            getProcessLogPage,
            handleCurrentChange
        }
    }
})
</script>

<style scoped>
    
    </style>
