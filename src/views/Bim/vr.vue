<template>
<div class="bim-box">
    <iframe :src="iframeUrl" frameborder="no" border="0" style="width:100%;height: 100%;"></iframe>
    <el-cctv ref="camRef" :url="url" :visiable="visiable" @closeView="closeView"></el-cctv>
    <el-dialog align-center :append-to-body="true" draggable @close="closeDiagram" :modal="false" :title="diagramName" v-model="viewVisibale" width="800px" custom-class="cctv-dialog">
        <iframe :src="viewUrl" style="height:500px"></iframe>
    </el-dialog>
    <el-dialog align-center :append-to-body="true" @click="close" :title="title" draggable :modal="false" custom-class="cctv-dialog" v-model="panelVisibale" width="400px">
        <div class="list-item" v-for="(item, index) in sData" :key="index">
            <div class="text">
                {{ item.name }}
            </div>
            <div>
                <i :class="icon.icon" :style="{ color: icon.color }" v-show="condition(panelData[item.id], icon.factor, icon.value)" v-for="(icon, i) in item.conditions" :key="i"></i>
            </div>
        </div>
        <div class="list-item" v-for="(item,index) in sInputData" :key="index">
            <div class="list-item-icon">{{ item.name }}</div>
            <div> {{ panelData[item.id] }}{{ getUnit(item.unit) }}</div>
        </div>
        <div class="list-item" v-for="(item, index) in sOutData" :key="index">
            {{ item.name }}：
            <el-slider class="yangan" @change="changeSlider($event, item.variable)" :min="item.min" :max="item.max" v-model="value2">
            </el-slider>
        </div>

        <div class="list-item" v-if="numData.length>0">
            <div> {{ numData[0].name }}：</div>
            <div class="center">
                <div>
                    <i class="iconfont icontingzhi2-copy" @click="writeValue(numData[0].variable, 1)"></i>
                </div>
                <div>
                    <i class="iconfont iconbofang2" @click="writeValue(numData[0].variable, 0)"></i>
                </div>
            </div>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    inject,
    
    computed,
    watch

} from 'vue'

import {
    getCookie,
} from '@/utils/cookie'
import CountTo from '@/components/vueCountTo/vue-countTo.vue'
import socket from '@/utils/socket'
import cctv from '@/components/cctv/src/main.vue'
import { useAppStore } from '@/stores/app'

export default defineComponent({
    name: "vr",
    sockets: {
        live(res) {
            this.process(res)
        },
        onVarsChangedCallback(res) {
            this.process(res)
        },
    },
    components: {
        CountTo,
        'el-cctv': cctv,
    },

    setup(props) {
        const api = inject('$api');
        const emitter = inject('mitt');
        const store = useAppStore();
        const state = reactive({
            camRef: null,
            url: null,
            visiable: false,
            viewUrl: null,
            diagramName: "视图",
            viewVisibale: false,
            panelVisibale: false,
            title: "",
            units: null,
            sData: [],
            sInputData: [],
            sOutData: [],
            numData: [],
            panelData: {},
            iframeUrl: '',
            vr: '',
            ar: '',
            session: null
        });

        state.sockets = inject('socket')
        onMounted(() => {
            getUrl()
            getDicUtil()
            window.addEventListener('message', async function (e) {
                if (e.data && e.data.vrctrl) {

                    let vrctrl = e.data["vrctrl"]
                    if (vrctrl && vrctrl.startsWith("cam")) {
                        let arr = vrctrl.split("|");
                        if (!state.session) {
                            await getSession(arr[1].split(":")[0], arr[1].split(":")[1]);
                        }
                        // state.url = `109|${arr[1].split(":")[0]}|${arr[1].split(":")[1]}|${arr[2]}|${arr[3]}|${arr[3]}`
                        state.camRef.opened({
                            server: arr[1].split(":")[0], //流媒体ip --h5参数
                            port: arr[1].split(":")[1], //流媒体port ---h5参数
                            token: arr[2], //zlm和h5平台通用
                            name: arr[3], //摄像机名称 --通用
                            serverType: 1, //平台类型 1 h5 2--zlm平台
                            ip: "127.0.0.1", //摄像机ip   zlm onvif
                            username: "admin", //摄像机用户名 zlm onvif
                            password: "admin", //摄像机密码 zlm onvif
                            profileToken: "token", //摄像机 onvif profileToken zlm onvif
                            ptzEnable: true,
                            session: state.session
                        });
                    } else if (vrctrl && vrctrl.startsWith("map")) {
                        let arr = vrctrl.split("|");

                        state.viewUrl = `${ window.PROD_9008_API}/runview.html?diagramId=` + arr[1] +
                            `&token=${getCookie("gh_token")}&projectId=${getCookie( "gh_projectId" )}&userName=${"gh_name"}&userId=${getCookie('gh_id')}`;
                        state.diagramName = arr[2] || '视图';
                        state.viewVisibale = true;
                    } else if (vrctrl && vrctrl.startsWith("dev")) {
                        if (vrctrl.split("|")[1]) {
                            getStd(vrctrl.split("|")[1])
                            state.panelVisibale = true
                            state.title = "实时数据"
                        }
                    } else if (vrctrl && vrctrl.startsWith("bim")) {
                        if (vrctrl.split("|")[1]) {
                            emitter.emit('changeMode', {
                                mode: 1,
                                model: vrctrl.split("|")[1]
                            });

                        }

                    }
                }
            })
        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        watch(activeMenus, (val) => {
            let mode = getCookie("mode");
            if (mode == 0&&!state.iframeUrl) {
               getUrl();
            }
        });
        const closeView = () => {
            state.visiable = false
        }
        const closeDiagram = () => {
            state.viewUrl = null;
            state.diagramName = null;
        }
        const process = (res) => {
            if (res) {
                let data = JSON.parse(res)
                if (data.batchDefinitionId == 'real' && data.clientId == 'vr') {
                    data.data.forEach((d) => {
                        if (d.id.startsWith('p_')) {
                            state.alarm[d.id].real = d.value
                            processAnnotation(d.id, d.value)
                        } else if (d.id.startsWith('s_')) {
                            state.panelData[d.id] = d.value
                        } else {
                            state.realData[d.id] = d.value
                        }
                    })
                }
            }
        }

        const openPanel = (deviceId) => {
            if (deviceId) {
                getStd(deviceId)
                state.panelVisibale = true
            }
        }
        const getStd = (deviceId) => {
            state.sData = [];
            state.sInputData = [];
            state.sOutData = [];
            state.numData = [];
            api.getDeviceStandard({
                deviceId,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                res.data.forEach((d, i) => {
                    d.standardParams.forEach((p, j) => {
                        if (p.dataType == 'num_input' && p.paramKey == 'Value') {
                            //状态 当前值
                            state.sData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                conditions: p.config ? JSON.parse(p.config) : {},
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_input' && p.paramKey == 'Value') {
                            //模拟输入
                            state.sInputData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                unit: p.unit,
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_out' && p.paramKey == 'Value') {
                            //模拟输出
                            state.sOutData.push({
                                name: d.name,
                                min: parseInt(p.min),
                                max: parseInt(p.max),
                                variable: d.variable,
                            })
                        }
                        if (p.dataType == 'num_out' && p.paramKey == 'Value') {
                            //数字输出
                            state.numData.push({
                                name: d.name,
                                // conditions: p.conditions,
                                variable: d.variable,
                            })
                        }
                    })
                })
            })
        }
        const panelSubscribe = (variable, id) => {
            if (variable) {
                let v = variable.split(':')
                let item = {
                    id: id,
                    iosvrKey: v[0],
                    chlKey: v[1],
                    ctrlKey: v[2],
                    varKey: v[3],
                    realTime: false,
                }
                socket.subscribe(state.sockets, 'real', 'vr', [item])
                state.panelData = Object.assign({}, state.panelData, {
                    [id]: 0,
                })
            }
        }

        const writeValue = (variable, val) => {
            socket.writeValue(state.sockets, variable, val, getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const changeSlider = (val, variable) => {
            socket.writeValue(state.sockets, variable, val, getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const iconCondition = (value, condition, v) => {
            if (!value) value = 0
            if (condition && eval(value + condition + v)) return true
            return false
        }
        const condition = (value, factor, v) => {
            if (!value) value = 0
            if (eval(value + factor + v)) return true
            return false
        }
        const getUnit = (id) => {
            let name = ''
            for (let u of state.units) {
                if (u.tagValue == id) {
                    name = u.tagName
                    break
                }
            }
            return name
        }
        const getDicUtil = () => {
            api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.units = res.data
            })
        }
        const close = () => {
            state.panelVisibale = false;
            state.panelData = {}
            state.sData = []
            state.sInputData = []
            state.sOutData = []
            state.numData = []
            socket.unsubscribe(state.sockets, 'vr', 'real')
        }

        const getUrl = () => {
            api.getProjectById(
                getCookie("gh_projectId")
            ).then(res => {
                state.vr = res.data.vrUrl;
                state.ar = res.data.arUrl;
                // let mode = getCookie('mode');
                // if (mode == 0) {
                    state.iframeUrl = state.vr;
                // } else if (mode == 3) {
                //     state.iframeUrl = state.ar;
                // }

            })
        }

        const getSession = async (ip, port) => {
            const {
                data
            } = await api.getSession({
                ip,
                port
            })
            state.session = data;
        }
        return {
            ...toRefs(state),
            closeView,
            closeDiagram,
            process,
            panelSubscribe,
            getStd,
            openPanel,
            writeValue,
            changeSlider,
            iconCondition,
            condition,
            getUnit,
            getDicUtil,
            close,

        }
    },
})
</script>

<style lang="scss" scoped>
.bim-box {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #889cc3;
    padding: 0 20px;
    border-bottom: 1px solid #3a3b3d;
    margin-bottom: 15px;
}
</style>
