<template>
<div class="bim-box">
    <div id="player">
    </div>

    <el-cctv :url="url" :visiable="visiable" @closeView="closeView"></el-cctv>
    <el-dialog align-center :append-to-body="true" @close="closeDiagram" modal="false" :title="diagramName" v-model="viewVisibale" width="800px" custom-class="cctv-dialog">
        <iframe :src="viewUrl" style="height:500px"></iframe>
    </el-dialog>
    <el-dialog align-center :append-to-body="true" :show-close="false" custom-class="panel" v-model="panelVisibale" width="400px">
        <template #header>
            <div class="header">
                <div>{{title}}</div>
                <img @click="close" class="cursor" src="./img/panel2.png" />
            </div>
        </template>
        <div class="status-row center">
            <div class="status-row-icon flex" v-for="(item, index) in sData" :key="index">
                <div class="w100 center">
                    <div>
                        <i :class="icon.icon" :style="{ color: icon.color }" v-show="condition(panelData[item.id], icon.factor, icon.value)" v-for="(icon, i) in item.conditions" :key="i"></i>
                    </div>
                    <div class="text">
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
        <div class="status-row center">
            <div class="status-row-icon flex" v-for="(item, index) in sInputData" :key="index">
                <div class="w100 center">
                    <div class="num">
                        {{ panelData[item.id] }}{{ getUnit(item.unit) }}
                    </div>
                    <div class="text">
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
        <div class="status-row" v-for="(item, index) in sOutData" :key="index">
            {{ item.name }}：
            <el-slider class="yangan" @change="changeSlider($event, item.variable)" :min="item.min" :max="item.max" v-model="value2">
            </el-slider>
        </div>
        <div class="status-row btn">
            <div class="btn-group" v-for="(item, index) in numData" :key="index">
                <el-button :style="{ background: d.color }" @click="writeValue(item.variable, d.value)" type="primary" size="mini" v-for="(d, i) in item.conditions" :key="i">
                    {{ d.text }}
                </el-button>
            </div>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    ElMessage
} from 'element-plus';
import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    inject,
    

} from 'vue'
import {
    ManageCommand
} from "./connect";
import socket from '@/utils/socket'
import cctv from '@/components/cctv/src/main.vue'
import {
    getCookie,
} from '@/utils/cookie'
export default defineComponent({
    name:"player",
    components: {
        'el-cctv': cctv,
    },
    sockets: {
        live(res) {
            this.process(res)
        },
        onVarsChangedCallback(res) {
            this.process(res)
        },
    },
    setup() {
        const api = inject('$api')
        const state = reactive({
            ws: null,
            authorization: null,
            Version: null,
            api: null,
            colorActor: null,

            url: null,
            visiable: false,
            viewUrl: null,
            diagramName: "视图",
            viewVisibale: false,
            panelVisibale: false,
            units: null,
            sData: [],
            sInputData: [],
            sOutData: [],
            numData: [],
            panelData: {},
        });
        const process = (res) => {
            if (res) {
                let data = JSON.parse(res)
                if (data.batchDefinitionId == 'real' && data.clientId == 'vr') {
                    data.data.forEach((d) => {
                        if (d.id.startsWith('p_')) {
                            state.alarm[d.id].real = d.value
                            processAnnotation(d.id, d.value)
                        } else if (d.id.startsWith('s_')) {
                            state.panelData[d.id] = d.value
                        } else {
                            state.realData[d.id] = d.value
                        }
                    })
                }
            }
        }
        const onReady = (e) => {
            reset();
            setCamera([-132.656406, 281.734375, 133.573018, -22.178463, 56.131676, 0.000003])
            state.api.marker.clear();
            addMarker();

            addMarker1("mm1", HostConfig.Path + '/samples/images/marker/marker_bg_11.png', '打开视图', JSON.stringify({
                data: "map|1485"
            }), [-72.52902221679688, 19.447969436645508, 11.500009536743164])
            // 
            addMarker1("mm2", HostConfig.Path + '/samples/images/marker/marker_bg_11.png', '打开视频', JSON.stringify({
                data: "cam|*************:2600|servertoken1-43|办公大厅南"
            }), [-69.41703033447266, -33.71421813964844, 11.499990463256836])

            addMarker1("mm3", HostConfig.Path + '/samples/images/marker/marker_bg_11.png', '打开面板', JSON.stringify({
                data: "dev|199"
            }), [-31.277109146118164, 20.56640625, 11.500009536743164])
            // [64.7382583618164, 67.87905883789062, 0.21330077946186066]
            addMarker1("mm6", HostConfig.Path + '/samples/images/marker/marker_bg_11.png', '楼层拆解', JSON.stringify({
                type: 1
            }), [64.7382583618164, 67.87905883789062, 0.21330077946186066])

            state.api.infoTree.get().then(res => {

                console.log(res)
            })
        }
        const setColor = (obj) => {
            state.api.customObject.clear();
            if (state.colorActor) {
                state.api.tileLayer.showActor(state.colorActor.Id, state.colorActor.ObjectID);
                if (state.colorActor.ObjectID == obj.ObjectID && state.colorActor.Id == obj.Id) {
                    state.colorActor = null;
                    return;
                }
                state.colorActor = null;
            }
            //当前单击图层和模型id
            var layerId = obj.Id;
            var objId = obj.ObjectID;
            //获取当前点击的Actor信息
            state.api.tileLayer.getActorInfo({
                id: layerId,
                objectIds: [objId]
            }).then(result => {
                //获取点击选中的Actor的位置坐标
                var actorLocation = result.data[0].location;
                //获取点击选中的Actor的缩放大小
                var actorScale = result.data[0].scale;
                //构造新customObject对象id
                var newActorId = objId + '_copy_' + new Date().getTime();
                //复制一份 实现平移旋转缩放
                var copyActor = {
                    id: newActorId,
                    tileLayerId: layerId,
                    objectId: objId,
                    location: actorLocation,
                    scale: actorScale, //物体缩放尺寸
                    rotation: [0, 0, 0], // 旋转角度
                    smoothMotion: 1 //1: 平滑插值，0: 跳跃
                };
                //添加复制的自定义对象
                state.api.customObject.addByTileLayer(copyActor);
                //设置Actor颜色
                state.api.customObject.setTintColor(newActorId, Color.Red);
                //隐藏选中的Actor
                state.api.tileLayer.hideActor(layerId, objId);
                //聚焦定位到新的Actor
                state.api.customObject.focus(newActorId, 30);
            });
            state.colorActor = {
                Id: obj.Id,
                ObjectID: obj.ObjectID
            };
        }

        const getCamera = () => {
            state.api.camera.get().then(result => {
                console.log("green", '相机位置信息：[' + result.camera + ']');

            });
        }

        const setCamera = (data) => {
            state.api.camera.set(data[0], data[1], data[2], data[3], data[4]);
        }

        const reset = () => {
            if (state.api) {
                state.api.reset();
            }
        }
        const onEvent = (e) => {
            if (e.eventtype == "LeftMouseButtonClick") {
                if (e.UserData) {
                    let data = JSON.parse(e.UserData)
                    if (data.data) {
                        let vrctrl = data.data;
                        if (vrctrl && vrctrl.startsWith("cam")) {
                            let arr = vrctrl.split("|");
                            state.visiable = true

                            state.url = `109|${arr[1].split(":")[0]}|${arr[1].split(":")[1]}|${arr[2]}|${arr[3]}|${arr[3]}`
                        } else if (vrctrl && vrctrl.startsWith("map")) {
                            let arr = vrctrl.split("|");

                            state.viewUrl = `${ window.PROD_9008_API}/runview.html?diagramId=` + arr[1] +
                                `&token=${getCookie("gh_token")}&projectId=${getCookie( "gh_projectId" )}&userName=${"gh_name"}&userId=${getCookie('gh_id')}`;
                            state.diagramName = arr[2] || '视图';
                            state.viewVisibale = true;
                        } else if (vrctrl && vrctrl.startsWith("dev")) {
                            if (vrctrl.split("|")[1]) {
                                getStd(vrctrl.split("|")[1])
                                state.panelVisibale = true
                            }
                        } else if (vrctrl && vrctrl.startsWith("bim")) {
                            if (vrctrl.split("|")[1]) {
                                emitter.emit('changeMode', {
                                    mode: 1,
                                    model: vrctrl.split("|")[1]
                                });

                            }

                        }
                    }
                }
            }
        }

        const addMarker3D = async () => {
            await state.api.marker3d.clear();
            let o = {
                id: 'm1',
                coordinate: [-5.828593730926514, -0.5942187309265137, 23.82146453857422], //坐标位置
                // textLocation:[-73.61746215820312, 29.617639541625977, 14.500000953674316],
                coordinateType: 0, //支持经纬度坐标和普通投影坐标两种类型，默认0是投影坐标系，也可以设置为空间坐标系值为1 
                // anchors: [0, 0], //锚点 控制标注整体的偏移
                range: [1, 1000], //可视范围
                // imagePath: "", //显示图片路径
                // hoverImagePath: "", // 鼠标悬停时显示的图片路径
                // imageSize: [213, 56], //图片的尺寸
                // fixedSize: true, //图片固定尺寸，取值范围：false 自适应，近大远小，true 固定尺寸，默认值：false 
                text: '123', //显示的文字
                textFixed: false,
                textVisible: true,
                // text: document.getElementById("test"), //显示的文字
                // useTextAnimation: false, //打开文字展开动画效果
                // textRange: [1, 10000], //文本可视范围[近裁距离, 远裁距离]
                // textOffset: [0, 0], // 文本偏移
                //textBackgroundColor: [0, 0, 0, 0.5], //文本背景颜色
                textSize: 50, //字体大小
                textOutlineSize: 1, //字体轮廓线大小
                textColor: Color.White, //字体颜色
                textOutlineColor: [0, 0, 0, 0], //字体轮廓线颜色

                pointName: "Point_B_0",
                //  autoHidePopupWindow: true, //失去焦点后是否自动关闭弹出窗口
                autoHeight: false, // 自动判断下方是否有物体
                // displayMode: 2, //显示模式 
                // priority: 0, //避让优先级
                //   occlusionCull: false //是否参与遮挡剔除
            };
            state.api.marker3d.add(o);
        }
        const addCustomerMarker = async () => {
            await state.api.customTag.clear();
            await state.api.customTag.add({
                id: "ct1",
                coordinate: [-73.61746215820312, 29.617639541625977, 14.500000953674316],
                contentURL: HostConfig.Path + "/popup/buildingArea.html?value=188.88", // 网页URL
                // contentURL: 'http://************:3000/home1', // 网页URL
                contentSize: [400, 400], //网页窗口宽高 [width, height]
                popupURL: "", //弹窗地址url
                popupSize: [0, 0], //弹窗尺寸
                popupPos: [100, 200], //弹窗位置: [x, y]
                pivot: [0.5, 1], // 中心点
                range: [1, 100], //显示范围：[min, max]
                autoHidePopupWindow: true, //失去焦点后是否自动关闭弹出窗口
            });
            // await state.api.customTag.focus("ct1", 200);
            setInterval(() => {
                let randomValue = (Math.random() * 100 + 100).toFixed(2);
                state.api.customTag.update({
                        id: "ct1",
                        contentURL: HostConfig.Path + `/popup/buildingArea.html?value=${randomValue}`,
                    },
                    null
                );
            }, 100000);
        }
        const addMarker = () => {

            let o = {
                id: 'm1',
                coordinate: [-5.828593730926514, -0.5942187309265137, 23.82146453857422], //坐标位置
                coordinateType: 0, //支持经纬度坐标和普通投影坐标两种类型，默认0是投影坐标系，也可以设置为空间坐标系值为1 
                anchors: [0, 0], //锚点 控制标注整体的偏移
                range: [1, 10000], //可视范围
                imagePath: "", //显示图片路径
                hoverImagePath: "", // 鼠标悬停时显示的图片路径
                imageSize: [213, 56], //图片的尺寸
                fixedSize: true, //图片固定尺寸，取值范围：false 自适应，近大远小，true 固定尺寸，默认值：false 
                text: ' 剩余电量： 0% \n 室内VOC： 0um/m³', //显示的文字
                // text: document.getElementById("test"), //显示的文字
                useTextAnimation: false, //打开文字展开动画效果
                textRange: [1, 10000], //文本可视范围[近裁距离, 远裁距离]
                textOffset: [0, 0], // 文本偏移
                textBackgroundColor: [0, 0, 0, 0.5], //文本背景颜色
                fontSize: 18, //字体大小
                fontOutlineSize: 1, //字体轮廓线大小
                fontColor: Color.White, //字体颜色
                fontOutlineColor: [0, 0, 0, 0], //字体轮廓线颜色

                showLine: false, //标注点下方是否显示垂直牵引线
                lineSize: [2, 100], //垂直牵引线宽度和高度[width, height]
                lineColor: "#3AFB8C", //垂直牵引线颜色
                lineOffset: [116, 0], //垂直牵引线偏移

                autoHidePopupWindow: true, //失去焦点后是否自动关闭弹出窗口
                autoHeight: false, // 自动判断下方是否有物体
                displayMode: 2, //显示模式 
                priority: 0, //避让优先级
                occlusionCull: false //是否参与遮挡剔除
            };
            state.api.marker.add(o);
            // state.api.marker.focus("m1", 200, 1);
            setInterval(() => {
                let randomValue = (Math.random() * 5 + 4).toFixed(2);
                let randomValue2 = (Math.random() * 10 + 10).toFixed(2);
                state.api.marker.update({
                        id: "m1",
                        text: ` 剩余电量： ${randomValue}% \n 室内VOC： ${randomValue2}um/m³`, //显示的文字
                    },
                    null
                );
            }, 2000);
            //自定义背景图片来显示模拟垂直牵引线Marker
            // o.id = 'm2';
            // o.text = "多行文字\r简单标注二";
            // o.textOffset = [-250, -50], // 文本偏移
            //     o.coordinate = [495282.6875, 2491073.25, 25.399999618530273];
            // o.imagePath = HostConfig.Path + '/samples/images/marker/marker_bg_3.png', //显示图片路径
            //     o.hoverImagePath = HostConfig.Path + '/samples/images/marker/marker_bg_3.png', // 鼠标悬停时显示的图片路径
            //     o.anchors = [0, 158], //锚点 控制标注整体的偏移 Y值保持和图片高度一致
            //     o.imageSize = [465, 158], //设置图片尺寸 注意图片设置的高度就是锚点Y偏移量158
            //     o.showLine = false; //隐藏垂直牵引线
            // state.api.marker.add(o);

            //切相机视角
            // state.api.camera.set(495467.8125, 2490935.5, 98.447578, -18.99688, -143.388855, 0);
        }
        const addMarker1 = (id, img, text, data, coordinate) => {

            let o = {
                id: id,
                coordinate: coordinate, //坐标位置
                coordinateType: 0, //支持经纬度坐标和普通投影坐标两种类型，默认0是投影坐标系，也可以设置为空间坐标系值为1 
                anchors: [0, 0], //锚点 控制标注整体的偏移
                range: [1, 10000], //可视范围
                imagePath: img, //显示图片路径
                hoverImagePath: "", // 鼠标悬停时显示的图片路径
                imageSize: [213, 56], //图片的尺寸
                fixedSize: true, //图片固定尺寸，取值范围：false 自适应，近大远小，true 固定尺寸，默认值：false 
                text: text, //显示的文字
                // text: document.getElementById("test"), //显示的文字
                useTextAnimation: false, //打开文字展开动画效果
                textRange: [1, 10000], //文本可视范围[近裁距离, 远裁距离]
                textOffset: [-160, -1], // 文本偏移
                textBackgroundColor: [0, 0, 0, 0.5], //文本背景颜色
                fontSize: 18, //字体大小
                fontOutlineSize: 1, //字体轮廓线大小
                fontColor: Color.White, //字体颜色
                fontOutlineColor: [0, 0, 0, 0], //字体轮廓线颜色

                showLine: false, //标注点下方是否显示垂直牵引线
                lineSize: [2, 100], //垂直牵引线宽度和高度[width, height]
                lineColor: "#3AFB8C", //垂直牵引线颜色
                lineOffset: [116, 0], //垂直牵引线偏移

                autoHidePopupWindow: true, //失去焦点后是否自动关闭弹出窗口
                autoHeight: false, // 自动判断下方是否有物体
                displayMode: 2, //显示模式 
                priority: 0, //避让优先级
                userData: data,
                occlusionCull: false //是否参与遮挡剔除
            };
            state.api.marker.add(o);
            // state.api.marker.focus("m1", 200, 1);

            //自定义背景图片来显示模拟垂直牵引线Marker
            // o.id = 'm2';
            // o.text = "多行文字\r简单标注二";
            // o.textOffset = [-250, -50], // 文本偏移
            //     o.coordinate = [495282.6875, 2491073.25, 25.399999618530273];
            // o.imagePath = HostConfig.Path + '/samples/images/marker/marker_bg_3.png', //显示图片路径
            //     o.hoverImagePath = HostConfig.Path + '/samples/images/marker/marker_bg_3.png', // 鼠标悬停时显示的图片路径
            //     o.anchors = [0, 158], //锚点 控制标注整体的偏移 Y值保持和图片高度一致
            //     o.imageSize = [465, 158], //设置图片尺寸 注意图片设置的高度就是锚点Y偏移量158
            //     o.showLine = false; //隐藏垂直牵引线
            // state.api.marker.add(o);

            //切相机视角
            // state.api.camera.set(495467.8125, 2490935.5, 98.447578, -18.99688, -143.388855, 0);
        }
        const connect = () => {
            state.ws = new WebSocket(`ws://${window.HostConfig.Manager}`);
            state.ws.onopen = () => {
                // 连接成功时进行登录
                sendData({
                    command: ManageCommand.Login,
                    userName: window.userInfo ? window.userInfo.UserName : "admin",
                    password: window.userInfo ? window.userInfo.PassWord : "admin",
                });
            };
            state.ws.onmessage = (event) => {
                let o = JSON.parse(event.data);
                console.log(o);

                //检查登录是否过期
                if (o.result == 1 && state.Version === 5.0) {

                    ElMessage.warning("登录失败或者登录信息已过期，请重新登录!");
                    return;
                }
                switch (o.command) {
                    case -1: {
                        if (o.result == 0) {
                            state.authorization = o.authorization; //保存
                            ElMessage.success("登录成功");

                            sendData({
                                command: ManageCommand.GetOneFreeInstance,
                                details: true,
                                connections: true,
                            });
                        }
                    }
                    break;
                case 1:
                    break;
                case 6:
                    break;
                case 100:
                    break;
                case 102:
                    /**
                     * @5.0版本的实例连接:
                     * @param {id}
                     * @return {initInterface()}
                     */
                    console.log(o, "oooooooooooooooooooo");
                    // 这里的返回是获取到空闲实例（或者正在运行的实例）
                    // 在这里进行实例的连接
                    initInterface_5(true, o.id);
                    window.addEventListener("resize", onResize);

                    break;
                }
            };
            state.ws.onclose = () => {
                state.ws = null;
                // this.connect();
            };

            state.ws.onerror = function () {
                state.ws = null;
            };
        }
        // 发送WebSocket指令
        const sendData = (o) => {
            //5.0集群版cloud需要在发送之前需要附加上授权信息以进行权限验证
            if (state.authorization) {
                o.authorization = state.authorization;
            }
            state.ws.send(JSON.stringify(o));
        }
        // 5.0连接实例
        const initInterface_5 = (iscloud, IID) => {
            let log = () => {};

            let _onClose = (e) => {
                console.log(e);
                //如果没有指定工程文件，则加上pid参数，pid=-1让服务器随便指定一个工程文件
                //如果想访问指定的工程文件，则明确指定pid, 例如pid=3
                if (e && e.code == 4009) {
                    if (location.href.indexOf("?") == -1) location.href += "?pid=-1";
                    else location.href += "&pid=-1";
                }
            };
            // _onApiVersion
            let _onApiVersion = (e) => {
                console.log(e, "_onApiVersion");
            };
            //AirCityAPI初始化选项
            let apiOptions = {
                onReady: onReady,
                onApiVersion: _onApiVersion,
                onEvent: onEvent,
                onLog: log,
                useColorLog: false, //仅用于SDK测试页面，二次开发请设置为false
            };
            //2021.07.30 因为支持了端口映射，所以这个地方要处理一下内外网host的问题
            //因为HostConfig里自动生成的是内网地址，所以这个地方要根据当前访问的地址替换一下
            // if (location.protocol != "file:") {
            //   HostConfig.Player =
            //     location.hostname + ":" + HostConfig.Player.split(":")[1];
            // }
            //Cloud需要同时初始化AirCityAPI和AirCityPlayer
            if (iscloud) {
                //state.
                let options;
                if (document.getElementById("player")) {
                    //需要显示视频流
                    options = {
                        iid: "2491904259328", //如果想连接指定的云渲染实例，可以指定这个参数
                        pid: 2,
                        domId: "player",
                        apiOptions: apiOptions,
                        showMarker: false,
                        showStartupInfo: false,
                        onclose: _onClose,
                    };
                } else {
                    options = {
                        iid: "2491904259328", //不带视频流的连接必须指定云渲染实例
                        pid: null,
                        apiOptions: apiOptions,
                    };
                }
                let aircityPlayer = new window.AirCityPlayer(
                    window.HostConfig.Player,
                    options
                );

                console.log(aircityPlayer, "aircityPlayer");
                // 提交仓库
                // this.setAirCityPlayer_act(aircityPlayer);
                //对于Cloud应用可以不用显式的创建AirCityAPI对象，只需要在AirCityPlayer创建参数里指定apiOptions，就会自动创建。
                let aircityApi = aircityPlayer.getAPI();
                state.api = aircityApi;

            }
        }

        const onResize = () => {
            console.log("onResize");

        }

        const getStd = (deviceId) => {
            api.getDeviceStandard({
                deviceId,
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                res.data.forEach((d, i) => {
                    d.standardParams.forEach((p, j) => {
                        if (p.dataType == 'num_input' && p.paramKey == 'Value') {
                            //状态 当前值
                            state.sData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                conditions: p.config ? JSON.parse(p.config) : {},
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_input' && p.paramKey == 'Value') {
                            //模拟输出
                            state.sInputData.push({
                                id: 's_d_' + i + '_' + j,
                                name: d.name,
                                unit: p.unit,
                            })
                            panelSubscribe(d.variable, 's_d_' + i + '_' + j)
                        }
                        if (p.dataType == 'string_out' && p.paramKey == 'Value') {
                            //模拟输出
                            state.sOutData.push({
                                name: d.name,
                                min: parseInt(p.min),
                                max: parseInt(p.max),
                                variable: d.variable,
                            })
                        }
                        if (p.dataType == 'num_out' && p.paramKey == 'Value') {
                            //数字输出
                            state.numData.push({
                                name: d.name,
                                conditions: p.conditions,
                                variable: d.variable,
                            })
                        }
                    })
                })
            })
        }
        const panelSubscribe = (variable, id) => {
            if (variable) {
                let v = variable.split(':')
                let item = {
                    id: id,
                    iosvrKey: v[0],
                    chlKey: v[1],
                    ctrlKey: v[2],
                    varKey: v[3],
                    realTime: false,
                }
                socket.subscribe(state.sockets, 'real', 'vr', [item])
                state.panelData = Object.assign({}, state.panelData, {
                    [id]: 0,
                })
            }
        }

        const writeValue = (variable, val) => {
            socket.writeValue(state.sockets, variable, val, getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const changeSlider = (val, variable) => {
            socket.writeValue(state.sockets, variable, val, getCookie("gh_projectId"),
                getCookie("gh_id"))
        }
        const iconCondition = (value, condition, v) => {
            if (!value) value = 0
            if (condition && eval(value + condition + v)) return true
            return false
        }
        const condition = (value, factor, v) => {
            if (!value) value = 0
            if (eval(value + factor + v)) return true
            return false
        }
        const getUnit = (id) => {
            let name = ''
            for (let u of state.units) {
                if (u.tagValue == id) {
                    name = u.tagName
                    break
                }
            }
            return name
        }
        const getDicUtil = () => {
            api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.units = res.data
            })
        }
        const close = () => {
            state.panelVisibale = false;
            state.panelData = {}
            state.sData = []
            state.sInputData = []
            state.sOutData = []
            state.numData = []
            socket.unsubscribe(state.sockets, 'vr', 'real')
        }

        const closeDiagram = () => {
            state.viewUrl = null;
            state.diagramName = null;
        }
        const closeView = () => {
            state.visiable = false
        }
        state.sockets = inject('socket')
        getDicUtil()
        connect();

        return {
            ...toRefs(state),
            close,
            closeDiagram,
            getDicUtil,
            getUnit,
            closeView,
            condition,
            iconCondition,
            changeSlider,
            writeValue,
            panelSubscribe,
            getStd,
            process

        }
    }
})
</script>

<style lang="scss">
.bim-box {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 7;

    #player {
        height: 100%;
        width: 100%;
    }
}

.panel {
    .header {
        background: url("./img/panel1.png");
        height: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .flex {
        flex: 1;
    }
}
</style>
