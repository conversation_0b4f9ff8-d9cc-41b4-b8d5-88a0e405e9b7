<template>
<div class="electric-box layout_wrapper">
    <div class="wrapper">
        <el-form :inline="true" class="search_box form_inline" size="small">
            <el-form-item label="名称">
                <el-input v-model="name" placeholder="请输入名称查询"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button size="small" class="searchBtn" type="text" @click="search">查询</el-button>
            </el-form-item>
        </el-form>
        <div class="btn-group">
            <el-button icon="Plus" type="primary" size="mini" class="addBtn" @click="addCustomer(null, 'add')">新增
            </el-button>
            <el-button icon="Minus" class="deleteBtn" type="primary" size="mini" @click="del">删除</el-button>
        </div>
        <div class="table">
            <el-table :height="tableHeight" :data="list" fit @select="select" @select-all="select" table-layout="auto">
                <template #empty>
                    <no-data />
                </template>
                <el-table-column type="selection" width="55" align="center">
                </el-table-column>
                <el-table-column prop="name" label="名称" align="center">
                </el-table-column>
                <el-table-column prop="phone" label="联系方式" align="center">
                </el-table-column>
                <el-table-column prop="address" label="地址" align="center">
                </el-table-column>

                <el-table-column prop="customerType" label="类型" align="center">
                    <template #default="scope">
                        {{scope.row.customerType==1?'公司':'个人'}}
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" align="center">
                </el-table-column>

                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button type="text" class="editBtn" @click="edit(scope.row, 'edit')">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="page center">
            <el-pagination :total="total" :page="page" :size="size" @pagination="handleCurrentChange" />
        </div>
    </div>

    <el-dialog align-center :append-to-body="true" custom-class="custom_dialog" v-model="dialogData.visible" width="940px" title="空间管理">
        <el-form class="form" ref="form" :model="dialogData.customer" :rules="dialogData.rule">
            <el-row type="flex" :gutter="30">
                <el-col :span="12">
                    <el-form-item label="名称:" prop="name">
                        <el-input v-model="dialogData.customer.name" placeholder="请输入名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系方式:">
                        <el-input v-model="dialogData.customer.phone" placeholder="请输入名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="联系地址:" >
                        <el-input v-model="dialogData.customer.address" placeholder="请输入名称"></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-form-item label="分类:">
                        <el-select clearable v-model="dialogData.customer.customerType">
                            <el-option label="公司" :value="1"></el-option>
                            <el-option label="个人" :value="0"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

            </el-row>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" class="saveBtn" size="mini" @click="saveCustomer('form')">保存</el-button>
            </div>
        </template>
    </el-dialog>
</div>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'

import {
    onMounted,
    reactive,
    toRefs,
    ref,
    
    computed,
    watch
} from 'vue'
import { useAppStore } from '@/stores/app'


export default {
    name:"customer",
    props: ['nav'],
    setup() {
        const title = ref(null)
        const form = ref(null)
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            props: {
                label: 'name',
            },
            name: "",
            floor: "",
            data: null,
            items: [], //表格select
            dialogData: {
                visible: false,
                title: '',
                edit: false,
                customer: {
                    id: null,
                    name: '',
                    projectId: '',
                    phone: null,
                    address: null,
                    customerType: null
                },
                rule: {
                    name: [{
                        required: true,
                        message: '名称不能空',
                        trigger: 'blur'
                    }],
                    

                },
            },
            categories: [],
            depts: []
        })
        onMounted(() => {
            // getDicUtil()
            getCustomer()
            // getDept();
        })
        watch(projectId, (val) => {
            if (val) {
                // getDicUtil()
                getCustomer()
                // getDept();
            }
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        const getDicUtil = () => {
            api.getDicUtil({
                dicCode: 'space_type',
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.categories = res.data
            })
        }
        const getDept = () => {
            api.getDept({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.depts = res.data
            })
        }
        const getCustomer = () => {
            api.getCustomer({
                name: state.name,
                size: state.size,
                page: state.page,
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getCustomer()
        }
        const del = () => {
            if (state.items.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择要删除的数据',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.delCustomer({
                    ids: state.items,
                }).then((res) => {
                    getCustomer()
                    state.items = []
                })
            })
        }
        const edit = (row, type) => {
            if (type === 'edit') {
                state.dialogData.visible = true
                state.dialogData.edit = true;
                if (row.rent) {
                    row.rent = 1;
                } else {
                    row.rent = 0;
                }
                state.dialogData.customer = Object.assign({}, row);
            }
        }

        const addCustomer = (row, type) => {
            if (type === 'add') {
                state.dialogData.visible = true
                state.dialogData.title = "新增"
                state.dialogData.edit = false
                state.dialogData.customer = {
                    id: null,
                    name: '',
                    projectId: '',
                    phone: null,
                    address: null,
                    customerType: null
                }
            }
        }

        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }

        const search = () => {
            state.page = 1
            getCustomer()
        }
        const saveCustomer = () => {
            proxy.$refs['form'].validate(valid => {
                if (valid) {
                    state.dialogData.customer.projectId = getCookie("gh_projectId")

                    if (state.dialogData.edit) {
                        api.editCustomer(state.dialogData.customer).then(res => {
                            state.dialogData.visible = false;
                            getCustomer();
                        });

                    } else {
                        api.addCustomer(state.dialogData.customer).then(res => {
                            state.dialogData.visible = false;
                            getCustomer();
                        });
                    }
                }
            })

        }
        return {
            ...toRefs(state),
            getCustomer,
            title,
            saveCustomer,
            search,
            handleCurrentChange,
            del,
            edit,
            addCustomer,
            select,
            projectId,
            form,
            getDicUtil
        }
    },
}
</script>

<style lang="scss" scoped>
.electric-box {
    .qrcode {
        background: #091822;
    }
}
</style>
