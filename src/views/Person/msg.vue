<template>
  <div class="message-box">
    <div class="content" v-if="activeName == 'first'">
      <el-tabs v-model="messageType" @tab-click="handleMesType" class="h100">
        <el-tab-pane label="私信" name="privateMes" @click="messageType == 'privateMes'">
          <div v-for="(item, i) in messageData" :key="i">
            <div class="message-tab">
              <div class="tab-box">
                <el-avatar :size="60" :class="avatarText(item.type).class">{{
                  avatarText(item.type).text
                }}</el-avatar>
                <div class="message-info">
                  <el-badge type="danger" is-dot class="item">{{
                    item.inviteName || item.name
                  }}</el-badge>
                  <div class="text" v-if="item.type == 1">
                    <span>{{
                      item.inviteName + '邀请您加入' + item.projectName + '。'
                    }}</span><span class="label" style="color: #d7be60"
                      @click="agreeIvite(item, true)">确认加入</span><span class="label" style="color: #fa8072"
                      @click="agreeIvite(item, false)">拒绝加入</span>
                  </div>
                  <div class="text" v-if="item.type == 2">
                    <span>{{
                      '您已被管理员从' + item.projectName + '中移除。'
                    }}</span>
                    <span class="label" @click="markRead(item)">标记已读</span>
                  </div>
                  <div class="text" v-if="item.type == 3">
                    <span>{{
                      item.projectName + '已被管理员删除，将无法正常访问。'
                    }}</span>
                    <span class="label" @click="markRead(item)">标记已读</span>
                  </div>
                </div>
              </div>
              <div class="remark">{{ item.date }}</div>
            </div>
          </div>
          <div class="tab-contnet" v-if="messageData.length == 0">暂无消息</div>
        </el-tab-pane>
        <el-tab-pane label="通知" name="notice" @click="messageType == 'notice'">
          <div class="tab-contnet">暂无通知消息</div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>


import { useAppStore } from "@/stores/app";
import {
  getCookie
} from "@/utils/cookie";

export default defineComponent({
  setup () {
    const store = useAppStore();
    const api = inject('$api')
    const state = reactive({
      userName: '',
      activeName: 'first',
      messageType: 'privateMes',
      socket: null,
      Visible: true,
      userMenus: [],

    });
    state.socket = inject('socket');
    const messageData = computed(() => {
      return store.mesData;
    });


    onMounted(() => {

    });

    const changeTab = (key) => {
      state.tab = key;
    };

    const handleSet = (option) => {
      state.activeName = option;
    };
    const handleMesType = (tab) => {
      state.messageType = tab.paneName
    };
    const avatarText = (type) => {
      switch (type) {
        case 1:
          return {
            class: 'invite', text: '邀请'
          }
          break
        case 2:
          return {
            class: 'remove', text: '移除'
          }
          break
        case 3:
          return {
            class: 'delete', text: '删除'
          }
          break
      }
    };
    const markRead = (item) => {
      item = Object.assign(item, {
        userId: parseInt(getCookie("gh_id"))
      });
      state.socket.emit('ConfirmMsg', JSON.stringify(item));
      store.SET_MESSAGE_DATA([]);

      state.socket.emit('GetAdviceMes', getCookie('gh_id'));
    };
    const agreeIvite = (item, bool) => {
      api.agreeIvite({
        agree: bool,
        projectId: item.projectId,
        userId: getCookie("gh_id")
      }).then(res => {
        markRead(item)
      })
    };


    return {
      ...toRefs(state),
      changeTab,
      handleSet,
      handleMesType,
      avatarText,
      markRead,
      agreeIvite,
      messageData,
    }
  },
})
</script>

<style lang="scss" scoped>
.el-avatar {
  background: #3f83d4;

  &.invite {
    background-color: rgba(0, 255, 127, 0.8);
  }

  &.remove {
    background-color: #ff8c00;
  }

  &.delete {
    background-color: #ff0000;
  }
}

.message-box,
.content,
.el-tabs {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.message-tab {
  padding: 16px 24px 8px 24px;
  margin-bottom: 26px;
  border-bottom: solid 2px rgba(62, 131, 212, 0.3);

  .tab-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .message-info {
      margin-left: 16px;

      div {
        line-height: 30px;
      }

      .item {
        color: white;
      }

      .text {
        .label {
          color: #3e83d4;
          letter-spacing: 0;
          margin-left: 30px;
          text-decoration: underline;
          cursor: pointer;
        }

        span {
          letter-spacing: 2px;
          color: white;
        }
      }
    }
  }

  .remark {
    width: 100%;
    text-align: right;
  }

  .el-avatar {
    font-size: 20px;
  }
}

.tab-contnet {
  height: 100%;
  width: 100%;
  color: #9ca4b7;
  padding: 16px;
  text-align: center;
}
</style>
