<template>
  <div class="layout_wrapper">
    <div class="wrapper p1">
      <allocate-table :tableData="list" />
    </div>
  </div>
</template>
<script lang="ts">
import allocateTable from "./table/allocateTable.vue";
import { reactive, toRefs } from "vue";
export default {
  components: { allocateTable },
  setup() {
    const state = reactive({
      list: [
        {
          id: 1,
          code: "LY000101",
          updateName: "赵琳",
          updateTime: "2021-05-28 14:58:57",
          organizeName: "汇彩园店",
          status: "3",
          name: "定制",
          remark: "定制",
          createName: "管理员",
          createTime: "2021-01-12 14:06:01",
        },
      ],
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>