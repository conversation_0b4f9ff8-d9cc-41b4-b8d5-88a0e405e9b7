<template>
<scrap-table :tableData="list" />
</template>

<script lang="ts">
import {
    reactive,
    toRefs
} from "vue";
import scrapTable from "./table/scrapTable.vue";
export default {
    name: "capscrap",
    components: {
        scrapTable
    },
    setup() {
        const state = reactive({
            list: [{
                assetsCheckInstanceList: null,
                createBy: "3992516245809307638",
                createId: "zhaolin",
                createName: "赵琳",
                createTime: "2021-05-28 15:13:56",
                department: "3985884259092050934",
                departmentName: "市场管理与拓展部一组",
                deptJson: null,
                finishDate: "2021-06-30 00:00:00",
                name: "第三季度资产盘点",
                oid: "4006828354003055604",
                organizationJson: null,
                remark: "",
                status: "0",
                updateBy: "3992516245809307638",
                updateId: "zhaolin",
                updateName: "赵琳",
                updateTime: "2021-05-28 15:13:56",
                version: 1,
                warehouseJson: null,
            }, ],
        });

        return {
            ...toRefs(state),
        };
    },
};
</script>
