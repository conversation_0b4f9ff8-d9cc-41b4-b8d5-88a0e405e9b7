<template>
<account-table :tableData="list" />
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import accountTable from './table/accountTable.vue'
export default {
    name: "capaccount",
    components: {
        accountTable
    },
    setup() {
        const state = reactive({
            list: [{
                id: '1',
                code: 'ZCHGJI002745',
                assetsName: "端头货架",
                typeName: "端头货架"
            }],

        })

        return {
            ...toRefs(state),

        }
    }
}
</script>
