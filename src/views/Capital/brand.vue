<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="分类名称">
            <el-input v-model="keyword" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="getBrandList">查询</div>
        </el-form-item>

        <div class="btn-group">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="operating(null, 'add')">新增品牌
            </div>
        </div>
    </el-form>

    <el-table class="table" height="530" :data="list" fit>
        <template #empty>
            <noData />
        </template>
        <el-table-column prop="name" label="品牌名称" align="center">
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center">
        </el-table-column>
        <el-table-column prop="createName" label="操作人" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="operating(scope.row, 'edit')">编辑</el-button>
                <el-button @click="deleteType(scope.row)" type="text" class="del">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="Pagination.size" :current-page="Pagination.page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
        </el-pagination>
    </div>
    <brand-dialog v-if="dialogData.visible" @load="getListByCondition" :dialogData="dialogData" />
</div>
</template>

<script lang="ts">
import brandDialog from "./dialog/brandDialog.vue";
import {
    reactive,
    toRefs
} from "vue";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    ElMessageBox
} from "element-plus";
import {
    getCookie
} from '@/utils/cookie';
export default {
    name: "capbrand",
    components: {
        brandDialog,
    },
    setup() {
        const {
            proxy
        }: any = getCurrentInstance();
        const state = reactive({
            Pagination: {
                total: 10,
                page: 1,
                size: 10,
            },
            list: [{
                id: 1,
                name: "定制",
                remark: "定制",
                createName: "管理员",
                createTime: "2021-01-12 14:06:01",
            }, ],
            dialogData: {
                visible: false,
                edit: false,
                title: "",
                form: {},
                rules: {
                    name: [{
                        required: true,
                        message: "请输入分类名称",
                        trigger: "blur"
                    }, ],
                },
            },
            keyword: null,
        });
        onMounted(() => {
            getListByCondition();
        });
        const getListByCondition = () => {
            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                keyword: state.keyword,
                projectId: getCookie("gh_projectId")
            };
            api.getBrandList(data).then((res: any) => {
                state.list = res.data;
                state.Pagination.total = res.total;
            });
        };
        const operating = (row: any, type: string) => {
            if (type == "add") {
                state.dialogData.visible = true;
                state.dialogData.edit = false;
                state.dialogData.title = "新增";
                state.dialogData.form = {
                    name: "",
                    remark: "",
                };
            } else if (type == "edit") {
                state.dialogData.visible = true;
                state.dialogData.edit = true;
                state.dialogData.title = "编辑";
                state.dialogData.form = Object.assign({}, row);
            }
        };
        const handleCurrentChange = (page: number) => {
            state.Pagination.page = page;
            getListByCondition();
        };
        const deleteType = (row: any) => {
            ElMessageBox.confirm("是否确认要删除该数据？", "提示", {
                confirmButtonClass: "confirmBtn",
                cancelButtonClass: "cancelBtn",
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                api.delBrand({
                    ids: [row.id]
                }).then((res: any) => {
                    getListByCondition();
                });
            });
        };
        return {
            ...toRefs(state),
            operating,
            getListByCondition,
            handleCurrentChange,
            deleteType,
        };
    },
};
</script>

<style lang="scss" scoped>

</style>
