<template>
<el-dialog align-center append-to-body   custom-class="addDiagram" width="400px" v-model="dialogData.visible" :title="dialogData.title">
    <el-upload class="upload-demo" drag action="https://jsonplaceholder.typicode.com/posts/" multiple>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
            <div class="el-upload__tip">
                只能上传Excel文件
            </div>
        </template>
    </el-upload>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="save('form')">保存</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
export default {
    props: {
        dialogData: {
            visible: false,
            title: ''
        }
    },
    setup() {

    }
}
</script>
