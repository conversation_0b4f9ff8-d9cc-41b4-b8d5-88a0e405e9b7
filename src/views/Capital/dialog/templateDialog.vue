<template>
  <el-dialog align-center append-to-body   custom-class="addDiagram" width="940px" v-model="dialogData.visible" :title="dialogData.title">
    <el-table :data="tableData">
      <el-table-column label="模板名称" prop="name"></el-table-column>
      <el-table-column label="文件类型" prop="fileTypeName"></el-table-column>
      <el-table-column label="上传时间" prop="updateTime"></el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="text" class="editBtn" @click="operating(scope.row, 'edit')">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="page center">
      <el-pagination :total="pagination.total" :page="pagination.page" :size="pagination.size" @pagination="handleCurrentChange" />
    </div>
  </el-dialog>
</template>
<script>
import { reactive, toRefs } from 'vue'
import { getCurrentInstance } from 'vue'
export default {
  props: {
    dialogData: {
      visible: false,
      title: '',
    }
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const state = reactive({
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      tableData: [{
        name: "编辑资产导入模板",
        fileTypeName: "资产台账",
        updateTime: "2020-12-24 20:01:26"
      }]
    })
    const save = (formName) => {
      proxy.$refs[formName].validate(valid => {
        if (valid) {

        } else {
          return false
        }
      })
    }
    return {
      ...toRefs(state),
      save
    }
  }
}
</script>