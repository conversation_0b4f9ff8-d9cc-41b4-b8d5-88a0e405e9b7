<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram border0" width="540px" v-model="dialogData.visible" :title="dialogData.title">
    <el-form ref="form" class="form" :model="dialogData.form" :rules="dialogData.rules" label-width="100px">
        <el-form-item label="品牌名称:" prop="name">
            <el-input v-model="dialogData.form.name" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        <el-form-item label="备注:">
            <el-input v-model="dialogData.form.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit>
            </el-input>
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" class="searchBtn" size="small" @click="save('form')">保存</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    getCurrentInstance
} from "vue";
import { getCookie } from '@/utils/cookie';
export default {
    props: {
        dialogData: {
            visible: false,
            title: "",
            edit: false,
            form: {},
            rules: {},
        },
    },
    setup(props, context) {
        const api = inject('$api');
        const state = reactive({});
        const save = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    props.dialogData.form.projectId=getCookie("gh_projectId")
                    if (props.dialogData.title == "新增") {
                        api.addBrand(props.dialogData.form).then((res) => {
                            props.dialogData.visible = false;
                            context.emit("load");
                        });
                    } else if (props.dialogData.title == "编辑") {
                        api.editBrand(props.dialogData.form).then((res) => {
                            props.dialogData.visible = false;
                            context.emit("load");
                        });
                    }
                } else {
                    return false;
                }
            });
        };
        return {
            ...toRefs(state),
            save,
        };
    },
};
</script>
