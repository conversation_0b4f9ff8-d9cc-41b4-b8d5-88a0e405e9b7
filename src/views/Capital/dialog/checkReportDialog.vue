<template>
<el-dialog align-center append-to-body   custom-class="addDiagram" width="940px" v-model="dialogData.visible" :title="dialogData.title">

    <el-table :data="assets">
        <el-table-column label="资产编号" prop="assetsName"></el-table-column>
        <el-table-column label="资产名称" prop="assetsCode"></el-table-column>
        <el-table-column label="盘点结果" prop="updateTime">
            <template #default="scope">
                <el-select v-model="scope.row.result" clearable @change="change($event,scope.row)">
                    <el-option label="通过" value="0">通过</el-option>
                    <el-option value="1" label="未通过">未通过</el-option>
                </el-select>
            </template>
        </el-table-column>
    </el-table>
    <div class="page center">
        <el-pagination :total="pagination.total" :page="pagination.page" :size="pagination.size" @pagination="handleCurrentChange" />
    </div>
    <template #footer>
        <div class="dialog-footer search_box">
            <div type="primary" size="mini" class="searchBtn" @click="saveOrder('form')">确 定</div>
        </div>
    </template>
</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    Base64
} from 'js-base64'
export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            deptId: null,
        }
    },
    setup(props) {
        const api = inject('$api')

        const state = reactive({
            pagination: {
                page: 1,
                size: 10,
                total: 0,
            },
            tableData: [{
                name: "编辑资产导入模板",
                fileTypeName: "资产台账",
                updateTime: "2020-12-24 20:01:26"
            }],
            assets: [],
            checks: []
        })
        onMounted(() => {
            getAssets();

        });
        const getListByCondition = () => {
            let param = "checkId = " + props.dialogData.oid;
            let data = {
                pageNum: state.pagination.page,
                pageSize: state.pagination.size,
                condition: Base64.encode(param),
            };
            api.getCheck(data).then((res) => {
                state.assets.forEach(d => {
                    let asset = res.data.list.find(v => v.assetsCode == d.assetsCode);
                    if (asset) {
                        d.result = asset.result.toString();
                        d.tag = true; //已经配置过
                    }
                });

            });
        };
        const getAssets = (deptId) => {
            let condtion = "ORGANIZE=" + props.dialogData.deptId;
            api.getInRecord({
                pageNum: state.pagination.page,
                pageSize: state.pagination.size,
                condition: Base64.encode(condtion)
            }).then(res => {
                getListByCondition();
                state.assets = res.data.list;
                state.pagination.total = parseInt(res.data.total)
            });
        }
        const handleCurrentChange = (page) => {
            state.pagination.page = page
            getAssets()
        }
        const change = (val, row) => {
            if (val == 0 || val == 1) {
                if (!row.tag) {
                    api.addCheckRecord({
                        assetsCode: row.assetsCode,
                        result: val,
                        checkId: props.dialogData.oid
                    }).then(res => {

                    })
                } else {
                    api.updateCheck({
                        assetsCode: row.assetsCode,
                        result: val,
                        checkId: props.dialogData.oid
                    }).then(res => {

                    })
                }

            }
        }
        return {
            ...toRefs(state),
            handleCurrentChange,
            getListByCondition,
            change
        }
    }
}
</script>

<style scoped>
.row {
    margin: 20px 0px;
    color: #fff;
}

.center {
    height: auto;
}
</style>
