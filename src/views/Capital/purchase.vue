<template>
<purchase-table :tableData="list" />
</template>

<script lang="ts">
import {
    reactive,
    toRefs
} from "vue";
import purchaseTable from "./table/purchaseTable.vue";
import {
    getCurrentInstance,
    onMounted
} from "vue";

export default {
    name: "cappurchase",
    components: {
        purchaseTable,
    },
    setup() {
        const {
            proxy
        }: any = getCurrentInstance();
        const state = reactive({
            list: [{
                auditDesc: "同意",
                auditorDate: "2021-05-28 15:05:37",
                auditorDeptId: "3985884259092050934",
                auditorDeptName: "市场管理与拓展部一组",
                auditorId: "3992516245809307638",
                auditorName: "赵琳",
                code: "CG000041",
                createBy: "30002",
                createId: "ymuser2",
                createName: "管理员",
                createTime: "2021-03-25 15:55:54",
                dateTime: null,
                department: "3985884299912070134",
                departmentName: "资产管理部",
                itemList: null,
                nowStatus: null,
                oid: "4001032788991066100",
                organize: "1",
                organizeId: "0000",
                organizeName: "石羊场店",
                organizeType: 0,
                purchaseCondition: null,
                reason: null,
                remark: null,
                status: "1",
                totelMoney: null,
                updateBy: "3992516245809307638",
                updateId: "zhaolin",
                updateName: "赵琳",
                updateTime: "2021-05-28 15:05:37",
                userId: "30002",
                userName: "管理员",
            }, ],
            Pagination: {
                total: 0,
                size: 10,
                page: 1
            }
        });

        return {
            ...toRefs(state),
        };
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 0px !important;
}
</style>
