<template>
<receive-table :tableData="list" />
</template>

<script lang="ts">
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import receiveTable from "./table/receiveTable.vue";
export default {
    name: "capreceiving",
    components: {
        receiveTable,
    },
    setup() {
        const router = useRouter();
        const state = reactive({
            moreShow: false,
            list: [{
                id: 1,
                code: "LY000101",
                updateName: "赵琳",
                updateTime: "2021-05-28 14:58:57",
                organizeName: "汇彩园店",
                status: "3",
                name: "定制",
                remark: "定制",
                createName: "管理员",
                createTime: "2021-01-12 14:06:01",
            }, ],
        });

        return {
            ...toRefs(state),
        };
    },
};
</script>
