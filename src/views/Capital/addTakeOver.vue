<template>
  <div class="layout_wrapper content">
    <div class="info">
      <div class="left">
        <div class="header">资产领用</div>
        <div class="list_content">
          <el-form :model="formInline" class="form" label-width="85px">
            <el-form-item label="选择门店：">
              <el-select placeholder="请选择" v-model="formInline.status">
                <el-option label="其他" :value="true"></el-option>
                <el-option label="使用中" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="资产分类：">
              <el-cascader v-model="formInline.value" popper-class="cascader" :options="options" @change="handleChange">
              </el-cascader>
            </el-form-item>
          </el-form>
          <div class="prompt">请拖动你资产到右边的地图上</div>
          <div class="table-list">
            <div class="row tit">
              <div class="column">编号</div>
              <div class="column">名称</div>
              <div class="column">绑定状态</div>
            </div>
            <div class="column-list">
              <div>
                暂无数据
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="header">地图</div>
        <div class="map"></div>
      </div>
    </div>
    <div class="footer btn">
      <el-button type="primary" class="saveBtn" size="small" @click="save('form')">生成需求单</el-button>
      <el-button type="text" size="small" class="backBtn" @click="back">返回</el-button>
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
export default {
  setup () {
    const router = useRouter()
    const state = reactive({
      formInline: {

      },
      tableData: []
    })
    const back = () => {
      router.go(-1)
    }
    return {
      ...toRefs(state),
      back
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  .info {
    display: flex;
    margin: 0 15px;
    height: calc(100% - 88px);
    color: #fff;
    .header {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
    }
    .left {
      display: flex;
      flex-direction: column;
      width: 360px;

      .list_content {
        flex: 1;
        padding: 16px;
        border: 1px solid #33383b;

        .prompt {
          line-height: 30px;
          color: #337d58;
          font-size: 14px;
        }
        .table-list {
          flex: 1;
          font-size: 14px;
          height: calc(100% - 120px);
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .row {
            display: flex;
            border: 1px solid #33383b;
            .column {
              height: 40px;
              line-height: 40px;
              padding: 0 6px;
              flex: 1;
              text-align: center;
              border-left: 1px solid #33383b;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .tit {
            font-family: "PingFangSC-Medium", "PingFang SC";
            font-size: 16px;
          }
          .column-list {
            flex: 1;
            height: 100%;
            overflow: auto;
            > div {
              text-align: center;
              line-height: 50px;
            }
          }
        }
      }
    }
    .right {
      flex: 1;
      margin-left: 20px;
      .map {
        flex: 1;
        height: calc(100% - 40px);
        overflow: auto;
        border: 1px solid #33383b;
      }
    }
  }
  .footer {
    height: 68px;
    line-height: 68px;
    text-align: center;
  }
}
</style>