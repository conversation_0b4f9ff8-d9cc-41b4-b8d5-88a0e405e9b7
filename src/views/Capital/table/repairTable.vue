<template>
  <div>
    <el-form :inline="true" class="search_box" size="small" label-width="100px">
      <!-- <el-form-item label="位置">
        <el-cascader />
      </el-form-item> -->
      <el-form-item label="资产编号">
        <el-input v-model="code" placeholder="请输入资产编号" />
      </el-form-item>
      <el-form-item label="资产名称">
        <el-input v-model="name" placeholder="请输入资产名称" />
      </el-form-item>
      <div v-if="moreShow">
        <el-form-item label="状态">
          <el-select v-model="region" placeholder="请选择">
            <el-option label="区域一" value="shanghai"></el-option>
            <el-option label="区域二" value="beijing"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select v-model="region" placeholder="请选择">
            <el-option label="区域一" value="shanghai"></el-option>
            <el-option label="区域二" value="beijing"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
      </div>
      <!-- <el-form-item>
        <el-button type="text" class="searchBtn" @click="moreShow=!moreShow">更多搜索
          <i v-if="!moreShow" class="el-icon-d-arrow-right el-icon--right" />
          <i v-else class="el-icon-d-arrow-left el-icon--right" />
        </el-button>
      </el-form-item> -->
      <el-form-item>
        <el-button size="small" class="searchBtn" type="text" @click="getListByCondition"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
    <div class="table">
      <el-table
        :data="list"
        :height="tableHeight"
        
        fit
        @select="select"
        @select-all="select"
      >
        <template #empty>
          <noData />
        </template>
        <el-table-column prop="code" label="任务编号" align="center">
        </el-table-column>
        <el-table-column prop="assetsCode" label="资产编号" align="center">
        </el-table-column>
        <el-table-column prop="assetsName" label="资产名称" align="center">
        </el-table-column>
        <el-table-column prop="level" label="紧急程度" align="center">
          <template #default="scope">
            <span>{{
              scope.row.level == 0
                ? "一般"
                : scope.row.level == 1
                ? "紧急"
                : scope.row.level == 2
                ? ""
                : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="审核状态" align="center">
          <template #default="scope">
            <span>{{
              scope.row.status == 0
                ? "待处理"
                : scope.row.status == 3
                ? "已完成"
                : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="organizeName" label="存放位置" align="center">
        </el-table-column>
        <el-table-column prop="createName" label="操作人" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" class="editBtn" @click="check(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="center page">
      <el-pagination
        :page-size="Pagination.size"
        :current-page="Pagination.page"
        prev-icon="CaretLeft" next-icon="CaretRight" 
        layout="total,prev, pager, next"
        @current-change="handleCurrentChange"
        :total="Pagination.total"
      >
      </el-pagination>
    </div>
    <check-dialog v-if="dialogData.visible" :dialogData="dialogData" />
    <check-report-dialog
      v-if="reportDialog.visible"
      :dialogData="reportDialog"
    />
  </div>
</template>
<script>
import { reactive, toRefs } from "vue";
import CheckDialog from "../dialog/checkDialog.vue";
import CheckReportDialog from "../dialog/checkReportDialog.vue";
import { useRouter } from "vue-router";
import { getCurrentInstance,onMounted } from "vue";
import { getCookie } from '@/utils/cookie';
export default {
  props: ["tableData"],
  components: {
    CheckDialog,
    CheckReportDialog,
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const router = useRouter();
    const state = reactive({
      code:"",
      name:"",
      moreShow: false,
      dialogData: {
        visible: false,
        title: "",
        form: {},
        rules: {
          name: [
            { required: true, message: "请输入盘点名称", trigger: "blur" },
          ],
          finishDate: [
            { required: true, message: "选择预计完成时间", trigger: "change" },
          ],
        },
      },
      list:[],
      reportDialog: {
        visible: false,
        title: "",
      },
      Pagination: {
        total: 0,
        page: 1,
        size: 10,
      },
    });
    onMounted(() => {
      getListByCondition();
    });
    const getListByCondition = () => {
      let data = {
        pageNum: state.Pagination.page,
        pageSize: state.Pagination.size,
        assetsName: state.name,
        assetsCode: state.code,
        projectId:getCookie("gh_projectId")
      };
      api.getAssetsRepair(data).then((res) => {
        state.list = res.data.list;
        state.Pagination.total = Number(res.data.total);
      });
    };
    const check = (row) => {
      router.push({
        path: `/capital-repairDetail`,
        query: {
          oid: row.oid,
        },
      });
    };
    const operating = () => {
      state.dialogData.visible = true;
      state.dialogData.title = "新增";
    };
    const report = () => {
      state.reportDialog.visible = true;
      state.reportDialog.title = "盘点报告";
    };
    const handleCurrentChange = (page) => {
      state.Pagination.page = page;
      getListByCondition();
    };
    return {
      ...toRefs(state),
      check,
      operating,
      report,
      handleCurrentChange,
      getListByCondition
    };
  },
};
</script>
