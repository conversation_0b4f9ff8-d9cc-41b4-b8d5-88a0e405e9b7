<template>
    <div class="h100">
        <el-form :inline="true" class="search_box" size="small">

            <el-form-item label="日期范围">
                <el-date-picker v-model="value1" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="状态">
                <el-select clearable v-model="status" placeholder="请选择">
                    <el-option label="待审核" value="0"></el-option>
                    <el-option label="审核完成" value="1"></el-option>
                    <el-option label="未通过" value="2"></el-option>
                    <el-option label="采购中" value="3"></el-option>
                    <el-option label="待入库" value="4"></el-option>
                    <el-option label="已入库" value="5"></el-option>
                </el-select>
            </el-form-item>



            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
            </el-form-item>

            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="open">申请</div>
            </el-form-item>


        </el-form>

        <el-table class="table" :data="list" height="calc(100% - 60px)" fit @select="select" @select-all="select">
            <template #empty>
                <noData />
            </template>
            <el-table-column prop="code" label="采购单号" align="center">
            </el-table-column>
            <el-table-column prop="deptName" label="需求部门" align="center">
            </el-table-column>
            <el-table-column prop="createName" label="需求人" align="center">
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span style="color:green" v-if="scope.row.status == '0'">待审核</span>
                    <span style="color:green" v-if="scope.row.status == '1'">审核完成</span>
                    <span style="color:green" v-if="scope.row.status == '2'">未通过</span>
                    <span style="color:green" v-if="scope.row.status == '3'">采购中</span>
                    <span style="color:green" v-if="scope.row.status == '4'">待入库</span>
                    <span style="color:green" v-if="scope.row.status == '5'">已入库</span>
                </template>
            </el-table-column>
            <el-table-column prop="createName" label="操作人" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" v-if="scope.row.status == 0" class="editBtn"
                        @click="audit(scope.row)">审核</el-button>
                    <el-button type="text" v-if="scope.row.status == '1'" class="editBtn"
                        @click="changeState(scope.row, 3)">采购</el-button>
                    <el-button type="text" v-if="scope.row.status == '3'" class="editBtn"
                        @click="changeState(scope.row, 4)">采购完成</el-button>
                    <el-button type="text" v-if="scope.row.status == '4'" class="editBtn"
                        @click="changeState(scope.row, 5)">入库</el-button>
                    <el-button type="text" class="editBtn" @click="checkDetail(scope.row)">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="pagination.size"
                :current-page="pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange"
                :total="pagination.total">
            </el-pagination>
        </div>
        <el-dialog align-center append-to-body custom-class="addDiagram border0" width="540px" draggable
            v-model="auditDialog" title="采购审核">
            <el-form ref="form1" class="form" label-width="100px">
                <el-form-item label="审核意见:">
                    <el-input v-model="auditDesc" type="textarea" placeholder="请输入" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn margin10" size="small" @click="updatePurchase(1)">通过</div>
                    <div type="primary" class="searchBtn" size="small" @click="updatePurchase(2)">拒绝</div>
                </div>
            </template>
        </el-dialog>

        <el-dialog align-center append-to-body custom-class="addDiagram border0" width="540px" draggable
            v-model="auditDialog" title="采购审核">
            <el-form ref="form1" class="form" label-width="100px">
                <el-form-item label="审核意见:">
                    <el-input v-model="auditDesc" type="textarea" placeholder="请输入" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn margin10" size="small" @click="updatePurchase(1)">通过</div>
                    <div type="primary" class="searchBtn" size="small" @click="updatePurchase(2)">拒绝</div>
                </div>
            </template>
        </el-dialog>

        <detail :id="id" v-model:showDialog="showDialog"></detail>

        <el-dialog align-center append-to-body custom-class="addDiagram border0 " width="500px" v-model="purchaseDialog"
            title="采购申请">
            <el-form ref="formRef" class="form" label-width="100px" :model="list2" :rules="rules">

                <el-form-item label="采购名称:" prop="name">
                    <el-input v-model="list2.name" placeholder="请输入采购名称"></el-input>
                </el-form-item>
                <el-form-item label="资产名称:" prop="assetsName">
                    <el-input v-model="list2.assetsName" placeholder="请输入资产名称"></el-input>
                </el-form-item>


                <el-form-item label="资产品牌" prop="brandId">
                    <el-select clearable v-model="list2.brandId">
                        <el-option v-for="item in brands" :key="item.id" :value="item.id"
                            :label="item.name"></el-option>
                    </el-select>
                </el-form-item>


                <el-form-item label="资产类型" prop="assetsType">
                    <el-select clearable v-model="list2.assetsType">
                        <el-option v-for="item in types" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="需求部门" prop="deptId">
                    <el-select clearable v-model="list2.deptId">
                        <el-option v-for="item in depts" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="规格型号:" prop="model">
                    <el-input v-model="list2.model" placeholder="请输入规格型号"></el-input>
                </el-form-item>

                <el-form-item label="备注:">
                    <el-input v-model="list2.remark" type="textarea" placeholder="请输入备注" maxlength="200"
                        show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn" size="small" @click="addPurchase">申请</div>
                </div>
            </template>

        </el-dialog>


    </div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie';
import detail from '../details/purchaseDetail.vue'
import purchase from "../purchase.vue";
import { ElMessageBox,ElMessage } from "element-plus";
import status from "@/views/Broadcast/status.vue";

export default {
    props: ["tableData"],
    components: { detail },
    setup(proxys, context) {
        const api = inject('$api');
        const router = useRouter();
        const state = reactive({
            formRef: null,
            auditDialog: false,
            pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            status: "",
            value1: [],
            list: [],
            dosageTab: [{
                name: "未审核",
                id: "1",
            },
            {
                name: "已审核",
                id: "2",
            },
            ],
            dosageType: "1",
            item: null,
            auditDesc: "",
            id: null,
            showDialog: false,
            purchaseDialog: false,
            list2: {
                name: "",
                assetsName: "",
                assetsType: "",
                qty: "",
                brandId: "",
                deptId: "",
                model: "",
                remark: "",
            },
            brands: [],
            depts: [],
            types: [],
            rules: {
                name: [{
                    required: true,
                    message: "请输入名称",
                    trigger: ["change"],
                },],
                model: [{
                    required: true,
                    message: "请输入规格型号",
                    trigger: ["change"],
                },],
                brandId: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                },],
                assetsType: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                },],

                assetsName: [{
                    required: true,
                    message: "请输入名称",
                    trigger: ["change"],
                },],
                deptId: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                },],

            },
        });
        onMounted(() => {
            state.value1[0] = dayjs().startOf('day');
            state.value1[1] = dayjs().endOf('day');
            getListByCondition();
            getBrand();
            getDepts();
            getTypes();
        });

        const getBrand = () => {
            api.getBrandList({}).then(res => {
                state.brands = res.data;
            });
        }
        const getDepts = () => {
            api.getDept({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.depts = res.data;
            });
        }
        const getTypes = () => {
            api.getAssetsTypeList({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.types = res.data;
            });
        }
        const getListByCondition = () => {

            let data = {
                page: state.pagination.page,
                size: state.pagination.size,
                status: state.status,
                bt: dayjs(state.value1[0]).format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs(state.value1[1]).format("YYYY-MM-DD HH:mm:ss"),
                projectId: getCookie("gh_projectId")
            };
            api.getPurchase(data).then((res) => {
                state.list = res.data;
                state.pagination.total = res.total;
            });
        };
        const changeTab = (id) => {
            state.dosageType = id;
            getListByCondition();
        };
        const checkDetail = (row) => {
            state.showDialog = true;
            state.id = row.id;
        };
        const handleCurrentChange = (page) => {
            state.pagination.page = page;
            getListByCondition();
        };
        const updatePurchase = (status) => {
            api.auditPurchase({
                purchaseId: state.item.id,
                status: status,
                desc: state.auditDesc,
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.auditDialog = false;
                getListByCondition();
            });

        }
        const changeState = (row, status) => {
            ElMessageBox.confirm('是否确认操作？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                api.changePurchaseState({
                    purchaseId: row.id,
                    status: status,
                    projectId: getCookie("gh_projectId")
                }).then(res => {
                    getListByCondition();
                    if (status == 5) {
                        api.assetInRecord({
                            purchaseId: row.id,
                            projectId: getCookie("gh_projectId")
                        }).then(res => {
                            if (res.success) {
                                ElMessage.success('入库成功')
                            }

                        });

                    }
                });
            }).catch(() => {
                console.log('取消');
            });

        }

        const audit = (row) => {
            state.auditDialog = true;
            state.item = row;
        }

        const addPurchase = () => {
            state.formRef.validate(validate => {
                if (validate) {
                    let brandName = "";
                    let deptName = "";
                    state.brands.forEach(d => {
                        if (d.id == state.list2.brandId) {
                            brandName = d.name;
                        }
                    })
                    state.depts.forEach(d => {
                        if (d.id == state.list2.deptId) {
                            deptName = d.name;
                        }
                    })
                    let data = {
                        remark: state.list2.remark,
                        deptId: state.list2.deptId,
                        deptName: deptName,
                        name: state.list2.name,
                        items: [{
                            assetsName: state.list2.assetsName,
                            brandId: state.list2.brandId,
                            brandName: brandName,
                            qty: 1,
                            model: state.list2.model,
                            deptId: state.list2.deptId,
                            deptName: deptName,
                            assetsType: state.list2.assetsType,
                            // receiveId: props.id,
                        }],
                        // receiveId: props.id,
                        projectId: getCookie("gh_projectId")
                    }
                    api.addPurchase(data).then(res => {
                        state.purchaseDialog = false;
                        getListByCondition();
                    });
                }
            });

        }

        const open = () => {
            state.purchaseDialog = true;
        }


        return {
            ...toRefs(state),
            checkDetail,
            changeTab,
            handleCurrentChange,
            getListByCondition,
            updatePurchase,
            audit,
            open,
            addPurchase,
            changeState,
        };
    },
};
</script>

<style scoped>
.margin10 {
    margin-right: 10px;
}
</style>
