<template>
    <div class="h100">
        <el-form :inline="true" class="search_box " size="small" label-width="100px">
            <el-form-item label="资产名称">
                <el-input v-model="name" placeholder="请输入资产名称" />
            </el-form-item>

            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
            </el-form-item>

            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="openScrap">申请</div>
            </el-form-item>
        </el-form>

        <el-table class="table" :data="list" height="calc(100% - 60px)" fit @select="select" @select-all="select">
            <template #empty>
                <noData />
            </template>
            <el-table-column prop="assetsCode" label="资产编号" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="assetsName" label="资产名称" align="center">
            </el-table-column>
            <el-table-column prop="assetsName" label="报废说明" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span>
                        <span style="color:green" v-if="scope.row.status == '0'">待审核</span>
                        <span style="color:green" v-if="scope.row.status == '1'">已通过</span>
                        <span style="color:green" v-if="scope.row.status == '2'">未通过</span>
                        <span v-if="scope.row.status == 3"></span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="address" label="存放位置" align="center">
            </el-table-column>
            <el-table-column prop="createName" label="操作人" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="180px">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" v-if="scope.row.status == '0'" class="editBtn"
                        @click="dispose(scope.row)">审核</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="Pagination.size"
                :current-page="Pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange"
                :total="Pagination.total">
            </el-pagination>
        </div>

        <el-dialog align-center append-to-body custom-class="addDiagram border0" width="560px" v-model="dialogVisible"
            title="处置">
            <el-form ref="form1" class="form" label-width="100px">
                <el-form-item label="审核意见:">
                    <el-input v-model="auditDesc" type="textarea" placeholder="请输入" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" class="saveBtn" size="small" @click="updateScrap(1)">通过</el-button>
                    <el-button type="primary" class="saveBtn" size="small" @click="updateScrap(2)">拒绝</el-button>
                </div>
            </template>
        </el-dialog>

        <el-dialog align-center append-to-body draggable custom-class="addDiagram border0" width="540px"
            v-model="scrapDialog" title="报废申请">
            <el-form ref="form2" class="form" label-width="100px" :model="scrapForm" :rules="rules">

                <el-form-item label="所属部门" prop="deptId">
                    <el-select v-model="scrapForm.deptId" placeholder="请选择部门" class="w100" @change="getAssets">
                        <el-option v-for="item in depts" :label="item.name" :key="item.id" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="资产类型:">
                    <el-select v-model="scrapForm.type" clearable filterable class="w100" @change="getAssets">
                        <el-option v-for="item in types" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="资产名称:" prop="assetsCode">
                    <el-select v-model="scrapForm.assetsCode" clearable filterable class="w100">
                        <el-option v-for="item in assets" :key="item.assetsCode" :value="item.assetsCode"
                            :label="item.assetsName"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="报废说明:">
                    <el-input v-model="scrapForm.remark" type="textarea" placeholder="请输入" maxlength="200"
                        show-word-limit>
                    </el-input>
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn" size="small" @click="saveScrap">确定</div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    getCookie
} from '@/utils/cookie';
export default {
    props: ["tableData"],
    setup() {
        const api = inject('$api');
        const router = useRouter();
        const state = reactive({
            form2: null,
            moreShow: false,
            scrapTypeArr: ["售卖", "退租", "报废清理", "毁灭清理", "其他"],
            list: [],
            scrapDialog: false,
            Pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            depts: [],
            assets: [],
            code: "",
            name: "",
            types: [],
            scrapForm: {
                assetsCode: "",
                assetsName: "",
                deptId: "",
                type: null,
                remark: "",
                projectId: getCookie("gh_projectId"),
            },
            dialogVisible: false,
            auditDesc: "",
            form: {
                type: null,
                remark: "",
            },
            row: {},
            rules: {
                assetsCode: [{
                    required: true,
                    message: "请选择",
                    trigger: "change"
                },],
                deptId: [{
                    required: true,
                    message: "请选择",
                    trigger: "change"
                },],
            },
        });
        onMounted(() => {
            getListByCondition();
            getTypes();
            getDepts();
        });
        const getListByCondition = () => {
            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                keyword: state.name,
                projectId: getCookie("gh_projectId")
            };
            api.getScraps(data).then((res) => {
                state.list = res.data;
                state.Pagination.total = res.total;
            });
        };
        const check = (row) => {
            router.push({
                path: `/capital-scrapDetail`,
                query: {
                    oid: row.oid,
                },
            });
        };
        const getTypes = () => {
            api.getAssetsTypeList({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.types = res.data;
            });
        }
        const getDepts = () => {
            api.getDept({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.depts = res.data;
            });
        }
        const openScrap = () => {
            state.scrapForm = {
                assetsCode: "",
                assetsName: "",
                deptId: "",
                type: null,
                remark: "",
                projectId: getCookie("gh_projectId"),
            }
            state.scrapDialog = true;
            if (state.form2) {
                state.form2.resetFields();
            }
        };
        const dispose = (row) => {
            state.row = row;
            state.dialogVisible = true;
        };
        const handleCurrentChange = (page) => {
            state.Pagination.page = page;
            getListByCondition();
        };
        const save = () => {
            api
                .postScrap({
                    oid: state.row.oid,
                    reason: state.form.remark,
                    type: state.form.type,
                    status: "1",
                })
                .then((res) => {
                    getListByCondition();
                    state.dialogVisible = false;
                });
        };
        const getAssets = () => {

            let data = {
                deptId: state.scrapForm.deptId,
                projectId: getCookie("gh_projectId"),
                typeId: state.scrapForm.type
            };
            api.getInRecord(data).then((res) => {
                state.assets = res.data;

            });
        };
        const updateScrap = (status) => {
            api.auditAssetsScrap({
                id: state.row.id,
                desc: state.auditDesc,
                status: status,
                assetsCode: state.row.assetsCode
            }).then(res => {
                state.dialogVisible = false;
                getListByCondition();
            });

        }
        const saveScrap = (row) => {
            state.form2.validate((valid) => {
                if (valid) {
                    let data = {
                        assetsCode: state.scrapForm.assetsCode,
                        assetsName: state.assets.find(v => v.assetsCode == state.scrapForm.assetsCode).assetsName,
                        deptId: state.scrapForm.deptId,
                        remark: state.scrapForm.remark,
                        projectId: getCookie("gh_projectId")

                    }
                    api.saveScrap(data).then(res => {
                        state.scrapDialog = false;
                        getListByCondition();
                    });
                }
            });

        }
        return {
            ...toRefs(state),
            check,
            dispose,
            save,
            getListByCondition,
            handleCurrentChange,
            updateScrap,
            openScrap,
            saveScrap,
            getAssets
        };
    },
};
</script>

<style>
.form_inline {
    margin-bottom: 0px;
    box-shadow: unset !important;
}
</style>
