<template>
    <div class="h100">
        <el-form :inline="true" class="search_box" size="small" label-width="90px">
            <el-form-item label="资产名称">
                <el-input v-model="keyword" placeholder="请输入资产名称"></el-input>
            </el-form-item>

            <el-form-item label="状态">
                <el-select clearable v-model="status" placeholder="请选择">
                    <el-option label="闲置中" value="0"></el-option>
                    <el-option label="使用中" value="1"></el-option>
                    <el-option label="已报废" value="2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
            </el-form-item>
            <el-form-item>
                <div type="primary" size="mini" icon="Plus" class="searchBtn" @click="add('')">入库登记
                </div>
            </el-form-item>
            <el-form-item>

                <el-upload class="upload" :action="url + '/asset-service/info/upload'" :on-success="importAssets"
                   name="file" :show-file-list="false" :data="param" :headers="headers">
                    <!-- <el-button class="searchBtn" size="small"  >导入</el-button> -->
                    <div type="primary" size="mini" icon="Plus" class="searchBtn">导入
                </div>
                </el-upload>

            </el-form-item>
        </el-form>
        <el-table class="table" :data="list" height="calc(100% - 60px)" @select="select" @select-all="select">
            <template #empty>
                <noData />
            </template>
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column prop="assetsCode" label="资产编号" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="assetsName" label="资产名称" align="center">
            </el-table-column>
            <el-table-column prop="typeName" label="分类" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="brandName" label="品牌" align="center">
            </el-table-column>
            <el-table-column prop="address" label="存放位置" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="actionStatus" label="状态" align="center">
                <template #default="scope">
                    <span style="color:green" v-if="scope.row.actionStatus == 0">闲置中</span>
                    <span style="color:green" v-if="scope.row.actionStatus == 1">使用中</span>
                    <span style="color:red" v-if="scope.row.actionStatus == 2">已报废</span>
                    <span style="color:wheat" v-if="scope.row.actionStatus == 3">待处置</span>
                </template>
            </el-table-column>
            <el-table-column prop="guaranteeDate" label="维保到期" align="center">
            </el-table-column>
            <el-table-column prop="deptName" label="部门" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="200px">
            </el-table-column>
            <el-table-column prop="username" label="领用人" align="center" width="200px">
            </el-table-column>
            <el-table-column label="操作" align="center" width="180px">
                <template #default="scope">
                    <el-button type="text" class="editBtn" @click="showCode(scope.row)">二维码</el-button>
                    <el-button type="text" class="editBtn" @click="add(scope.row)">编辑</el-button>
                    <!-- <el-button type="text" class="editBtn" v-if="scope.row.actionStatus=='1'" @click="addScrap(scope.row)">报废</el-button> -->
                </template>
            </el-table-column>
        </el-table>

        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="Pagination.size"
                :current-page="Pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange"
                :total="Pagination.total">
            </el-pagination>
        </div>



        <el-dialog align-center append-to-body draggable custom-class="addDiagram" width="540px" v-model="qrDialog"
            title="二维码">
            <div>
                <div class="qrcode">
                    <img id="qrImg">
                </div>
            </div>
        </el-dialog>

        <accountEdit v-model:showAccount="showAccount" :id="id" @getListByCondition="getListByCondition"></accountEdit>
    </div>
</template>

<script>
import {
    reactive,
    toRefs
} from 'vue'

import {
    useRouter
} from 'vue-router'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import QRCode from "qrcode";
import {
    getCookie
} from '@/utils/cookie'
import accountEdit from '../details/accountEdit.vue'
import status from '@/views/Broadcast/status.vue';
import { ElMessage } from 'element-plus';

export default {
    components: {
        "accountEdit": accountEdit
    },
    props: ['tableData'],
    setup() {
        const api = inject('$api');
        const router = useRouter()
        const state = reactive({
            list: [],
            scrapDesc: null,
            showAccount: false,
            assetsCode: '',
            keyword: '',
            status: '',
            url:'',
            param: {
                projectId: getCookie('gh_projectId'),
            },
            headers: {
                Authorization: 'bearer ' + getCookie('gh_token'),
            },
            Pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            scrapDialog: false,
            item: null,
            qrDialog: false,
            id: 0,
            tableLayout: 'fixed'

        });
        onMounted(() => {
            getListByCondition();
         
            state.url = window.PROD_BASE_API;
        });
        const getListByCondition = () => {

            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                keyword: state.keyword,
                status: state.status,
                projectId: getCookie("gh_projectId")
            };
            api.getInRecord(data).then((res) => {
                state.list = res.data;
                state.Pagination.total = res.total;
            });
        };
        const handleCurrentChange = (page) => {
            state.Pagination.page = page;
            getListByCondition();
        };
        const location = (row) => {
            if (row) {
                router.push({
                    path: `/capital-batchLocation/${row.id}`
                })
            } else {
                router.push({
                    path: `/capital-batchLocation/0`
                })
            }
        }
        const add = (row) => {
            if (row.id) {
                state.id = row.id
            } else {
                state.id = 0;
            }
            state.showAccount = true;
        }

        const saveScrap = (row) => {
            let data = {
                assetsCode: state.item.assetsCode,
                assetsName: state.item.assetsName,
                deptId: state.item.deptId,
                remark: state.scrapDesc,
                projectId: getCookie("gh_projectId")

            }
            api.saveScrap(data).then(res => {
                state.scrapDialog = false;
                getListByCondition();
            });
        }
        const addScrap = (row) => {
            state.scrapDialog = true;
            state.item = row;
        }
        const showCode = (row) => {
            state.qrDialog = true;
            QRCode.toDataURL(row.assetsCode, {
                width: 400,
                margin: 0
            })
                .then(url => {
                    console.log(url);
                    document.getElementById("qrImg").src = url;
                })
                .catch(err => {
                    console.error(err);
                });

        }
        const importAssets = () => {
            ElMessage.success('导入成功');
            getListByCondition();
        }
        return {
            ...toRefs(state),
            location,
            add,
            handleCurrentChange,
            getListByCondition,
            addScrap,
            saveScrap,
            showCode,
            importAssets
        }
    }
}
</script>

<style scoped>
.qrcode {
    display: flex;
    justify-content: center;
}
</style>
