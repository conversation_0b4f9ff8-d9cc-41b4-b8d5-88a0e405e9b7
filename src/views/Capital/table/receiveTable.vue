<template>
    <div class="h100">

        <el-form :inline="true" class="search_box " size="small">

            <el-form-item label="状态">
                <el-select clearable v-model="status" placeholder="请选择">
                    <el-option label="待审核" value="0"></el-option>
                    <el-option label="已通过" value="1"></el-option>
                    <el-option label="未通过" value="2"></el-option>
                    <el-option label="采购中" value="3"></el-option>
                    <el-option label="采购完成" value="4"></el-option>
                    <el-option label="已处理" value="5"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="领用人">
                <el-input v-model="userName" placeholder="领用人" />
            </el-form-item>
            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
            </el-form-item>

            <div class="btn-group">
                <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="operating">新增领用
                </div>
            </div>

        </el-form>

        <el-table class="table" :data="list" height="calc(100% - 60px)" fit @select="select" @select-all="select">
            <template #empty>
                <noData />
            </template>
            <el-table-column prop="code" label="编号" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="createName" label="申请人" align="center">
            </el-table-column>
            <el-table-column prop="userName" label="领用人" align="center">
            </el-table-column>
            <el-table-column prop="updateTime" label="领用日期" align="center" width="180px">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="180px">
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span style="color:green" v-if="scope.row.status == 0">待审核</span>
                    <span style="color:green" v-if="scope.row.status == 1">已通过</span>
                    <span style="color:green" v-if="scope.row.status == 2">未通过</span>
                    <span style="color:green" v-if="scope.row.status == 3">采购中</span>
                    <span style="color:green" v-if="scope.row.status == 4">采购完成</span>
                    <span style="color:green" v-if="scope.row.status == 5">已处理</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" v-if="scope.row.status == '0'" class="editBtn"
                        @click="audit(scope.row)">审核</el-button>
                    <el-button type="text" class="editBtn" @click="checkDetail(scope.row)">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="Pagination.size"
                :current-page="Pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange"
                :total="Pagination.total">
            </el-pagination>
        </div>

        <el-dialog align-center append-to-body custom-class="addDiagram border0" draggable width="540px"
            v-model="visiable" title="新增领用">
            <el-form ref="form" class="form" :model="item" :rules="rules" label-width="100px">
                <el-form-item label="资产类型:" prop="assetsType">
                    <el-select v-model="item.assetsType" clearable filterable class="w100" @change="getAssets">
                        <el-option v-for="item in types" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </el-form-item>


                <!-- <el-form-item label="领用资产:" prop="assetsCode">
                    <el-select v-model="item.assetsCode" clearable filterable class="w100">
                        <el-option v-for="item in assets" :key="item.assetsCode" :value="item.assetsCode"
                            :label="item.assetsName"></el-option>
                    </el-select>
                </el-form-item> -->

                <el-form-item label="所属部门" prop="deptId">
                    <el-select v-model="item.deptId" placeholder="请选择部门" class="w100">
                        <el-option v-for="item in depts" :label="item.name" :key="item.id" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

              

                <el-form-item label="备注:">
                    <el-input v-model="item.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <el-button type="primary" class="searchBtn" size="small" @click="addReceive">保存</el-button>
                </div>
            </template>
        </el-dialog>
        <el-dialog align-center append-to-body custom-class="addDiagram border0" width="540px" v-model="auditDialog"
            title="领用审核">
            <el-form ref="form1" class="form" label-width="100px">
                <el-form-item label="审核意见:">
                    <el-input v-model="auditDesc" type="textarea" placeholder="请输入" maxlength="200" show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" class="searchBtn" style="margin-right: 10px;" size="small"
                        @click="updateReceive(1)">通过</div>
                    <div type="primary" class="searchBtn" size="small" @click="updateReceive(2)">拒绝</div>
                </div>
            </template>
        </el-dialog>

        <detail v-model:detailDialog="detailDialog" :id="id" @getListByCondition="getListByCondition"></detail>

    </div>
</template>

<script>
import {
    reactive,
    ref,
    toRefs
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    getCurrentInstance,
    onMounted
} from 'vue'

import {
    getCookie
} from '@/utils/cookie'

import detail from '../details/receiveDetail.vue'
export default {
    props: {
        tableData: {
            type: Array,
            default: []
        },
        // 总条数
        total: {
            type: Number,
            default: 0
        },
        // 当前页
        page: {
            type: Number,
            default: 1
        },
        // 每页显示条数
        size: {
            type: Number,
            default: 10
        },
    },
    components: {
        detail
    },
    setup() {
        const api = inject('$api');
        const router = useRouter();
        const form = ref(null);
        const state = reactive({
            Pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            visiable: false,
            list: [],
            status: null,
            userName: null,
            tableHeight: window.innerHeight * 0.50,
            assets: [],

            item: {
                qty: 1,
                assetsCode: "",
                assetsType: '',
                deptId: null,
                remark: "",
            },
            depts: [],
            types: [],
            rules: {
                // qty: [{
                //     required: true,
                //     message: "请输入数量",
                //     trigger: ["blur", "change"],
                // },],

                assetsType: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                },],
                assetsCode: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                },],
                deptId: [{
                    required: true,
                    message: "请选择",
                    trigger: ["change"],
                },],

            },
            detailDialog: false,
            auditDialog: false,
            auditDesc: "",
            id: null,
        })
        onMounted(() => {
            // getAssets()
            getListByCondition();
            getDepts();
            getTypes();
        });
        const getDepts = () => {
            api.getDept({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.depts = res.data;
            });
        }
        const getTypes = () => {
            api.getAssetsTypeList({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.types = res.data;
            });
        }

        const getDept = () => {
            api.getDept({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.depts = res.data;
            });
        }

        const getAssets = () => {

            let data = {
                // deptId: state.item.deptId,
                projectId: getCookie("gh_projectId"),
                typeId: state.item.type
            };
            api.getInRecord(data).then((res) => {
                state.assets = res.data;

            });
        };


        const getListByCondition = () => {
            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                keyword: state.userName,
                status: state.status,
                projectId: getCookie("gh_projectId")
            };
            api.getReciveList(data).then((res) => {
                state.list = res.data;
                state.Pagination.total = res.total;
            });
        };

        const checkDetail = (row) => {
            // router.push({
            //     path: `/receiveDetail/${row.id}`,
            // });
            state.detailDialog = true;
            state.id = row.id;
        }
        const operating = () => {
            state.visiable = true;
        }
        const audit = (row) => {
            state.auditDialog = true;
            state.item = row;
        }
        const handleCurrentChange = (page) => {
            state.Pagination.page = page;
            getListByCondition();
        }
        const addReceive = () => {
            proxy.$refs.form.validate((validate) => {
                if (validate) {
                    state.item.projectId = getCookie("gh_projectId");
                    api.addReceive(state.item).then(res => {
                        state.visiable = false;
                        getListByCondition();
                    });
                }
            })

        }
        const updateReceive = (status) => {
            state.item.status = status;
            state.item.auditDesc = state.auditDesc;
            state.item.projectId = getCookie("gh_projectId");
            api.updateReceive(state.item).then(res => {
                state.auditDialog = false;
                getListByCondition();
            });

        }

        return {
            ...toRefs(state),
            checkDetail,
            operating,
            addReceive,
            handleCurrentChange,
            getListByCondition,
            form,
            audit,
            updateReceive,
            getAssets
        }
    }
}
</script>
