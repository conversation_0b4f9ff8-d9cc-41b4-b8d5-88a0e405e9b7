<template>
<div class="h100">
    <el-form :inline="true" class="search_box" size="small">

        <el-form-item label="任务名称">
            <el-input v-model="keyword"></el-input>
        </el-form-item>

        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
        </el-form-item>

        <div class="btn-group">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="operating">新增盘点
            </div>
        </div>
    </el-form>

    <el-table class="table" :data="list" height="calc(100% - 60px)" fit>
        <el-table-column prop="code" label="任务编号" align="center" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="name" label="盘点名称" align="center">
        </el-table-column>
        <el-table-column prop="status" label="盘点状态" align="center">
            <template #default="scope">
                <span style="color:green" v-if="scope.row.status==0">待处理</span>
                <span style="color:green" v-if="scope.row.status==1">处理中</span>
                <span style="color:green" v-if="scope.row.status==2">已完成</span>
            </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="盘点时间" align="center" width="180px">
        </el-table-column>
        <el-table-column prop="createName" label="操作人" align="center">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="180px">
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="check(scope.row)">查看详情</el-button>
                <el-button type="text" class="del" v-if="scope.row.status=='0'" @click="del(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="pagination.size" :current-page="pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="pagination.total">
        </el-pagination>
    </div>
    <check-dialog v-if="dialogData.visible" :dialogData="dialogData" @callback="callback" />
    <checkTaskDetail v-model:showDialog="showDialog" :id="id" @getListByCondition="getListByCondition"></checkTaskDetail>
</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import CheckDialog from "../dialog/checkDialog.vue";

import checkTaskDetail from '../details/checkTaskDetail.vue'
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    ElMessageBox
} from 'element-plus';
import {
    getCookie
} from '@/utils/cookie';
export default {
    components: {
        CheckDialog,
        "checkTaskDetail":checkTaskDetail
    },
    setup() {
        const api = inject('$api');
        const router = useRouter();
        const state = reactive({
            tableHeight: window.innerHeight * 0.6,
            status: null,
            value1: [],
            list: [],
            keyword: "",
            tableData: [{
                oid: "111",
            }, ],
            pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            dialogData: {
                visible: false,
                title: "",
                form: {},
                rules: {
                    name: [{
                        required: true,
                        message: "请输入盘点名称",
                        trigger: "blur",
                    }, ],
                    finishDate: [{
                        required: true,
                        message: "选择预计完成时间",
                        trigger: "change",
                    }, ],
                },
            },
            id:0,
            showDialog:false,


        });
        onMounted(() => {
            getListByCondition();
        });
        const callback = () => {
            state.dialogData.visible = false;
            getListByCondition();
        }
        const getListByCondition = () => {

            let data = {
                page: state.pagination.page,
                size: state.pagination.size,
                keyword: state.keyword,
                projectId: getCookie("gh_projectId")
            };
            api.getCheckTask(data).then((res) => {
                state.list = res.data;
                state.pagination.total = res.total;
            });
        };
        const check = (row) => {
            state.showDialog=true;
            state.id=row.id
        };
        const operating = () => {
            state.dialogData.visible = true;
            state.dialogData.title = "新增";
        };

        const handleCurrentChange = (page) => {
            state.pagination.page = page;
            getListByCondition();
        };
        const del = (row) => {
            ElMessageBox.confirm("是否确认要删除该数据？", "提示", {
                confirmButtonClass: "confirmBtn",
                cancelButtonClass: "cancelBtn",
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                api.delCheckTask({
                    ids: [row.id]
                }).then((res) => {
                    getListByCondition();
                });
            });
        };
        return {
            ...toRefs(state),
            operating,
            callback,
            check,
            del,
            getListByCondition,
            handleCurrentChange,
        };
    },
};
</script>

<style scoped>

</style>
