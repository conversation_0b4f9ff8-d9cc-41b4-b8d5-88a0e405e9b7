<template>
  <div class="layout_wrapper ">
    <div class="warn p1">
      <div>设置资产预警提醒，可以很好的帮助你监控所有资产情况。您可以设置提前多少天收到预警提醒，设置后可以在资产台账列表呈现预警状态。</div>
      <el-row class="row">
        <el-col :span="8" class="auto">
          <el-checkbox v-model="checked1" label="资产维保到期"></el-checkbox>
          <div style="margin-top: 16px;" class="form">
            <span>提前：</span>
            <el-input placeholder="请输入" v-model.number="input2" type="number" size="small"
              style="width: 220px; margin-right: 4px;">
              <template #append>天预警</template>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="btn center">
      <el-button type="primary" size="small" class="saveBtn" @click="update">保存</el-button>
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue'
import { getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
export default {
  setup () {
    const state = reactive({
      checked1: true,
      input2: 7
    })
    const {proxy}=getCurrentInstance();
    const update=()=>{
      api.warningUpdate({
        check:state.checked1,
        oid:'3987809569612018676',
        dayNum:state.input2,
        version:10
      }).then(res=>{
        ElMessage.success("设置成功")
      });
    }
    return {
      ...toRefs(state),
      update,
    }
  }
}
</script>
<style lang="scss" scoped>
.warn {
  display: flex;
  flex-direction: column;
  height: calc(100% - 110px);
  color: #fff;
  .row {
    margin-top: 20px;
    .auto {
      padding: 24px;
      background: rgba(68, 114, 141, 0.1);
      border-radius: 2px;
      border: 1px solid rgba(199, 223, 255, 0.5);
    }
  }
}
</style>