<template>
<div class="layout_wrapper">
    <div class="wrapper p1">
        <el-form :inline="true" class="search_box form_inline" size="small">
            <el-form-item label="位置">
                <el-cascader popper-class="cascader" />
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="condition" placeholder="请选择">
                    <el-option label="待处理" value="0"></el-option>
                    <el-option label="处理中" value="1"></el-option>
                    <el-option label="已完成" value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button size="small" class="searchBtn" type="text" @click="getListByCondition">查询</el-button>
            </el-form-item>
        </el-form>
        <div class="table">
            <el-table :data="list" :height="tableHeight"  fit @select="select" @select-all="select">
                <template #empty>
                    <noData />
                </template>
                <el-table-column prop="updateTime" label="任务时间" align="center">
                </el-table-column>
                <el-table-column prop="organizeName" label="门店名称" align="center">
                </el-table-column>
                <el-table-column prop="updateName" label="操作人" align="center">
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center">
                    <template #default="scope">
                        <span v-if="scope.row.status == 0">待处理</span>
                        <span v-if="scope.row.status == 1">处理中</span>
                        <span v-if="scope.row.status == 2">已完成</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button type="text" class="editBtn" @click="check(scope.row)">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="center page">
            <el-pagination :total="pagination.total" :page="pagination.page" :size="pagination.size" @pagination="handleCurrentChange" />
        </div>
    </div>
</div>
</template>

<script lang="ts">
import {
    reactive,
    toRefs
} from "vue";
import {
    useRouter
} from "vue-router";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    Base64
} from 'js-base64';
export default {
    components: {},
    setup() {
        const {
            proxy
        }: any = getCurrentInstance();
        const router = useRouter();
        const state = reactive({
            pagination: {
                total: 0,
                page: 1,
                size: 10,
            },
            condition: '',
            list: [{
                oid: "4006828489407771636",
                code: "LY000101",
                updateName: "赵琳",
                updateTime: "2021-05-28 14:58:57",
                organizeName: "汇彩园店",
                status: "3",
                name: "定制",
                remark: "定制",
                createName: "管理员",
                createTime: "2021-01-12 14:06:01",
            }, ],
        });
        onMounted(() => {
            getListByCondition();
        });
        const getListByCondition = () => {
            let data = {
                condition: state.condition ? Base64.encode("status=" + state.condition) : "",
                pageNum: state.pagination.page,
                pageSize: state.pagination.size,
            };
            api.getCheckTaskList(data).then((res: any) => {
                state.list = res.data.list;
                state.pagination.total = parseInt(res.data.total);
            });
        };
        const search = () => {};
        const check = (row: any) => {
            router.push({
                path: `/capital-checkTaskDetail`,
                query: {
                    oid: row.oid,
                },
            });
        };
        const handleCurrentChange = (page: number) => {
            state.pagination.page = page;
            getListByCondition();
        };
        return {
            ...toRefs(state),
            search,
            check,
            getListByCondition,
            handleCurrentChange
        };
    },
};
</script>
