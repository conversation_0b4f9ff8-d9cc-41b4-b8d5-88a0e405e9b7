<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="分类名称">
            <el-input v-model="keyword" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item>
            <div size="small" class="searchBtn" type="text" @click="getListByCondition">查询</div>
        </el-form-item>

        <div class="btn-group">
            <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="operating(null, 'add')">新增分类
            </div>
        </div>
    </el-form>


        <el-table class="table"  :data="list" row-key="id" :height="530" default-expand-all fit @select="select" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" @select-all="select">
            <template #empty>
                <noData />
            </template>
            <el-table-column label="#" type="index"> </el-table-column>
            <el-table-column prop="code" label="分类编号" align="center">
            </el-table-column>
            <el-table-column prop="name" label="分类名称" align="center">
            </el-table-column>
            <el-table-column prop="remark" label="备注" align="center">
            </el-table-column>
            <el-table-column prop="createName" label="操作人" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" class="editBtn" @click="operating(scope.row, 'edit')">编辑</el-button>
                    <el-button type="text" class="del" @click="deleteType(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

    <div class="center page">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight"  :page-size="Pagination.size" :current-page="Pagination.page"  layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
        </el-pagination>
    </div>
    <type-dialog v-if="dialogData.visible" @load="getListByCondition" :dialogData="dialogData" :data="list" />
</div>
</template>

<script lang="ts">
import typeDialog from "./dialog/typeDialog.vue";
import {
    reactive,
    toRefs
} from "vue";
import {
    ElMessageBox
} from "element-plus";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    getCookie
} from '@/utils/cookie';
export default {
    name: "captype",
    components: {
        typeDialog,
    },
    setup() {
        const {
            proxy
        }: any = getCurrentInstance();
        const state = reactive({
            Pagination: {
                total: 10,
                page: 1,
                size: 10,
            },
            keyword: "",
            list: [{
                id: 1,
                code: "JK",
                updateName: "管理员",
                children: [{
                    id: 2,
                    code: "SX",
                    updateName: "管理员",
                }, ],
            }, ],
            dialogData: {
                visible: false,
                edit: false,
                title: "",
                form: {},
                rules: {
                    name: [{
                        required: true,
                        message: "请输入分类名称",
                        trigger: "blur"
                    }, ],
                    code: [{
                        required: true,
                        message: "请输入分类编号",
                        trigger: "blur"
                    }, ],
                },
            },
        });
        onMounted(() => {
            getListByCondition();
        });
        const getListByCondition = () => {
            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                keyword: state.keyword,
                projectId: getCookie("gh_projectId")
            };
            api.getAssetsTypeList(data).then((res: any) => {
                state.list = res.data;
                state.Pagination.total = res.total;
            });
        };
        const operating = (row: any, type: string) => {
            if (type == "add") {
                state.dialogData.visible = true;
                state.dialogData.edit = false;
                state.dialogData.title = "新增";
                state.dialogData.form = {
                    name: "",
                    code:'',
                    remark:''
                }
            } else if (type == "edit") {
                state.dialogData.visible = true;
                state.dialogData.edit = true;
                state.dialogData.title = "编辑";
                state.dialogData.form = Object.assign({}, row);
            }
        };
        const deleteType = (row: any) => {
            ElMessageBox.confirm("是否确认要删除该数据？", "提示", {
                confirmButtonClass: "confirmBtn",
                cancelButtonClass: "cancelBtn",
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                api.delAssetsType({
                    ids: [row.id]
                }).then((res: any) => {
                    getListByCondition();
                });

            });
        };
        const handleCurrentChange = (page: number) => {
            state.Pagination.page = page;
            getListByCondition();
        }
        return {
            ...toRefs(state),
            operating,
            deleteType,
            handleCurrentChange,
            getListByCondition
        };
    },
};
</script>


