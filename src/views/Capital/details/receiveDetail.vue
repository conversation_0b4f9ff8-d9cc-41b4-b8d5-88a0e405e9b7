<template>
<el-dialog align-center append-to-body   draggable custom-class="addDiagram" @open="open" @closed="close" width="900px" :model-value="show" @update:model-value="updateShow" title="领用详情">
    <div class="receive">
        <div class="wrapper">
            <el-row type="flex" class="row" :gutter="10">
                <el-col :span="8">申请人：{{ details.createName }}</el-col>
                <el-col :span="8">领用日期：{{ details.dateTime }}</el-col>
                <el-col :span="8">创建时间：{{ details.createTime }}</el-col>
                <el-col :span="8">状态：{{ states[details.status] }}</el-col>
                <el-col :span="8">审核意见：{{ details.auditDesc }}</el-col>
            </el-row>
            <div class="box">
                <panel title="需求清单" />
                <div class="table">
                    <el-table :data="list">
                        <el-table-column label="#" type="index" width="50"> </el-table-column>
                        <el-table-column label="资产类型" prop="assetsTypeName"></el-table-column>
                        <el-table-column label="数量" prop="qty"></el-table-column>
                        <el-table-column label="备注" prop="remark"></el-table-column>
                    </el-table>
                </div>

            </div>
            <div class="box" v-if="(details.status == '1'||details.status=='4') && records.length == 0">
                <panel title="库存匹配" />
                <div class="btn audit-btn search_box">
                    <div type="primary" size="small" class="searchBtn" @click="receive" v-if="list1.length >= list[0].qty && (details.status == '1'||details.status=='4')">领用</div>
                    <!-- <div type="primary" size="small" v-if="list1.length < list[0].qty && details.status == '1'" class="searchBtn" @click="purchase">生成采购</div> -->
                </div>
                <div class="table">
                    <el-table :data="list1" height="300" @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="#" type="index" width="50"> </el-table-column>
                        <el-table-column label="资产名称" prop="assetsName"></el-table-column>
                        <el-table-column label="资产类型" prop="typeName"></el-table-column>
                        <el-table-column label="资产编号" prop="assetsCode"></el-table-column>
                        <el-table-column label="备注" prop="remark"></el-table-column>
                    </el-table>
                </div>
                <div class="center page">
                    <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="Pagination.size" :current-page="Pagination.page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="Pagination.total">
                    </el-pagination>
                </div>

            </div>
            <div class="box" v-if="records.length > 0">
                <panel title="已领用列表" />
                <div class="table">
                    <el-table :data="records" height="300">
                        <el-table-column label="#" type="index" width="50"> </el-table-column>
                        <el-table-column label="资产名称" prop="assetName"></el-table-column>
                        <el-table-column label="资产类型" prop="typeName"></el-table-column>
                        <el-table-column label="部门" prop="deptName"></el-table-column>
                        <el-table-column label="品牌" prop="brandName"></el-table-column>
                    </el-table>
                </div>

            </div>
        </div>
    </div>

    <el-dialog align-center append-to-body   custom-class="addDiagram border0 " width="500px" v-model="purchaseDialog" title="采购订单">
        <el-form ref="formRef" class="form" label-width="100px" :model="list2" :rules="rules">
            <el-form-item label="采购名称:" prop="name">
                <el-input v-model="list2.name"  placeholder="请输入采购名称"></el-input>
            </el-form-item>
            <el-form-item label="资产名称:" prop="assetsType">
                <el-input v-model="list2.assetsTypeName" readonly placeholder="请输入资产名称"></el-input>
            </el-form-item>
            <el-form-item label="资产类型:" prop="assetsType">
                <el-input v-model="list2.assetsTypeName" readonly placeholder="请输入资产类型"></el-input>
            </el-form-item>
            <el-form-item label="领用数量:" prop="qty">
                <el-input v-model="list2.qty" readonly placeholder="请输入领用数量"></el-input>
            </el-form-item>

            <el-form-item label="资产品牌" prop="deptId">
                <el-select clearable v-model="list2.brandId">
                    <el-option v-for="item in brands" :key="item.id" :value="item.id" :label="item.name"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="需求部门" prop="deptId">
                <el-select clearable v-model="list2.deptId">
                    <el-option v-for="item in depts" :key="item.id" :value="item.id" :label="item.name"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="规格型号:" prop="qty">
                <el-input v-model="list2.model" placeholder="请输入规格型号"></el-input>
            </el-form-item>

            <el-form-item label="备注:">
                <el-input v-model="list2.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit>
                </el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer search_box">
                <div type="primary" class="searchBtn" size="small" @click="addPurchase">采购</div>
            </div>
        </template>

    </el-dialog>

</el-dialog>
</template>

<script>
import {
    reactive,
    toRefs,
    ref,
    computed
} from 'vue'
import {
    getCurrentInstance,
    onMounted
} from 'vue'
import {
    useRouter
} from 'vue-router'
import {
    getCookie
} from '@/utils/cookie'
export default {
    props: ['id', 'detailDialog'],
    setup(props, {
        emit
    }) {
        const api = inject('$api');
        const allmap = ref()
        const router = useRouter()
        const state = reactive({
            formRef: null,
            activeName: 'first',
            dosageTab: [{
                name: '列表',
                id: 'list',
            }, ],
            dosageType: 'list',
            states: ['待审核', '已通过', '未通过', '采购中', '采购完成', '已处理'],
            details: {
                organizeName: "汇彩园店",
                auditDesc: "通过",
                dateTime: "2021-06-10 11:07:31",
                createName: "管理员",
                status: '0',
                qty: 0

            },
            list: [], //领用列表
            list1: [], //匹配的资产
            list2: [], //采购列表
            purchaseDialog: false,
            Pagination: {
                page: 1,
                size: 10,
                total: 0
            },
            brands: [],
            model1: "",
            brandId: null,
            depts: [],
            multipleSelection: [],
            records: [],
            rules: {
                name: [{
                    required: true,
                    message: '请输入采购名称',
                    trigger: 'blur'
                }],

            },
        })
        const show = computed(() => {
            return props.detailDialog
        })
        onMounted(() => {
            //后台无接口
            // getReciveDetail();
            // getReciveList(null, null, router.currentRoute.value.params.id);
            // getBrand();
            // getDepts();
        })
        const open = () => {
            getReciveDetail(props.id)
            getReciveList(null, null, props.id)
            getBrand()
            getDepts()
            getReceiveRecord()

        }
        const close = () => {
            state.multipleSelection = [];
            state.records = [];
            emit('update:detailDialog', false)
            emit('getListByCondition')
        }
        const updateShow = (value) => {
            emit('update:detailDialog', value)
        }
        const getBrand = () => {
            api.getBrandList({}).then(res => {
                state.brands = res.data;
            });
        }
        const getDepts = () => {
            api.getDept({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.depts = res.data;
            });
        }
        const getReciveList = (page, size, id) => {
            let data = {
                page: page,
                size: size,
                id: id,
            };
            api.getReciveList(data).then((res) => {
                state.list = res.data;
                if (res.data) {
                    getInRecord(res.data[0].assetsType);
                }
            });
        };
        const getInRecord = (type) => {
            let data = {
                page: state.Pagination.page,
                size: state.Pagination.size,
                type: type,
                status: 0
            };
            api.getInRecord(data).then((res) => {
                state.list1 = res.data;
                state.Pagination.total = res.total;
            });
        };
        const getReciveDetail = (id) => {
            api.getReceiveDetail({
                id
            }).then(res => {
                state.details = res.data;
            });
        };
        const changeTab = (tab) => {
            state.dosageType = tab
        }
        const purchase = () => {
            state.list2 = state.list[0];
            state.list2.qty = state.list[0].qty - state.list1.length;
            state.purchaseDialog = true;
        }
        const handleClick = (tab) => {
            state.activeName = tab.props.name
        }

        const addPurchase = () => {
            state.formRef.validate(validate => {
                if (validate) {
                    let ids = [];
                    let brandName = "";
                    let deptName = "";
                    state.brands.forEach(d => {
                        if (d.id == state.list2.brandId) {
                            brandName = d.name;
                        }
                    })
                    state.depts.forEach(d => {
                        if (d.id == state.list2.deptId) {
                            deptName = d.name;
                        }
                    })
                    let data = {
                        deptId: state.details.deptId,
                        remark: state.list2.remark,
                        deptId: state.list2.deptId,
                        deptName: deptName,
                        name:state.list2.name,
                        items: [{
                            assetsName: state.list2.assetsTypeName,
                            brandId: state.list2.brandId,
                            brandName: brandName,
                            qty: state.list2.qty,
                            model: state.list2.model,
                            deptId: state.list2.deptId,
                            deptName: deptName,
                            assetsType: state.details.assetsType,
                            receiveId: props.id,
                        }],
                        receiveId: props.id,
                        projectId: getCookie("gh_projectId")
                    }
                    api.addPurchase(data).then(res => {
                        state.purchaseDialog = false;
                        close();
                    });
                }
            });

        }

        const receive = () => {
            if (state.multipleSelection.length == 0) {
                proxy.$message({
                    message: '请选择资产',
                    type: 'warning'
                });
                return;
            }
            if(state.multipleSelection.length != state.list[0].qty){
                proxy.$message({
                    message: '资产数量不匹配',
                    type: 'warning'
                });
                return;
            }
            let data = JSON.parse(JSON.stringify(state.multipleSelection));
            data = data.map(d => {
                d.projectId = getCookie("gh_projectId");
                d.receiveId = props.id;
                return d;
            })
            api.addReceiveRecord(data).then(res => {
                proxy.$message({
                    message: '领用成功',
                    type: 'success'
                });
                close();
            });
        }
        const handleSelectionChange = (val) => {
            state.multipleSelection = val
        }
        const getReceiveRecord = () => {
            api.getReceiveRecord({
                receiveId: props.id
            }).then(res => {
                state.records = res.data;
            });
        }
        return {
            ...toRefs(state),
            allmap,
            changeTab,
            handleSelectionChange,
            handleClick,
            getInRecord,
            purchase,
            addPurchase,
            show,
            open,
            close,
            receive,
            updateShow
        }
    }
}
</script>

<style lang="scss" scoped>
.receive {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 80px);
    overflow: auto;

    .wrapper {
        padding: 0 20px;
        color: #fff;

        .box {
            margin: 18px 0;
        }

        .tabs,
        .table {
            margin-top: 15px;
        }

        .row {
            font-size: 15px;
            line-height: 30px;
        }
    }

    .audit-btn {
        padding: 10px;
    }
}
</style>
