<template>
<div class="analysis_container">
    <div class="wrapper">
        <div class="content_tab">
            <type-btn :typeData="tabs" @clickDeviceType="search" />
        </div>
        <div class="container_content">
            <div class="content_t">
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconruqin"></i>
                        <div class="name rq">入侵报警</div>
                    </div>
                    <div class="total">{{ source.t3 ? source.t3 : 0 }}</div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconshexiangji"></i>
                        <div class="name sp">视频报警</div>
                    </div>
                    <div class="total">{{ source.t1 ? source.t1 : 0 }}</div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconmenjin"></i>
                        <div class="name mj">门禁报警</div>
                    </div>
                    <div class="total">{{ source.t2 ? source.t2 : 0 }}</div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconhuozaibaojing"></i>
                        <div class="name hz">火灾报警</div>
                    </div>
                    <div class="total">
                        {{ source.t4 ? source.t4 : 0 }}
                    </div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconshebeiguzhang"></i>
                        <div class="name sb">设备故障</div>
                    </div>
                    <div class="total">
                        {{ source.t5 ? source.t5 : 0 }}
                    </div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont icondianqi"></i>
                        <div class="name dq">电气报警</div>
                    </div>
                    <div class="total">
                        {{ source.t6 ? source.t6 : 0 }}
                    </div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconlx"></i>
                        <div class="name lx">离线报警</div>
                    </div>
                    <div class="total">
                        {{ source.t7 ? source.t7 : 0 }}
                    </div>
                </div>
                <div class="item">
                    <div class="box">
                        <i class="iconfont iconqita"></i>
                        <div class="name qt">其他报警</div>
                    </div>
                    <div class="total">
                        {{ source.t8 ? source.t8 : 0 }}
                    </div>
                </div>
            </div>
            <div class="content_b">
                <alarm-echart :xAxisData="xData" :seriesData="data"></alarm-echart>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import typeBtn from '@/components/energy/typeBtn.vue'
import AlarmEchart from '@/components/echarts/alarmEchart.vue'
import {
    
    onMounted,
    reactive,
    toRefs
} from 'vue'

export default {
    name: "alarmanalysis",
    components: {
        typeBtn,
        AlarmEchart,
    },
    setup(props) {
        const api = inject('$api')
        const state = reactive({
            option: null,
            chart: null,
            source: {
                t1: 0,
                t2: 0,
                t3: 0,
                t4: 0,
                t5: 0,
                t6: 0,
                t7: 0,
                t8: 0,
                t9: 0
            },
            tabs: [{
                    name: '今年',
                    id: 'year',
                },
                {
                    name: '本月',
                    id: 'month',
                },
                {
                    name: '本周',
                    id: 'week',
                },
                {
                    name: '今日',
                    id: 'day',
                },
            ],
            xData: [],
            data: [
                [],
                [],
                []
            ]
        })
        onMounted(() => {
            search('year')
        })

        const search = (tag) => {
            let bt
            let et
            if (tag == 'day') {
                bt = dayjs()
                    .startOf('day')
                    .format('YYYY-MM-DD HH:mm:ss')
                et = dayjs()
                    .endOf('day')
                    .format('YYYY-MM-DD HH:mm:ss')
            } else if (tag == 'week') {
                bt = dayjs()
                    .startOf('week')
                    .format('YYYY-MM-DD HH:mm:ss')
                et = dayjs()
                    .endOf('week')
                    .format('YYYY-MM-DD HH:mm:ss')
            } else if (tag == 'month') {
                bt = dayjs()
                    .startOf('month')
                    .format('YYYY-MM-DD HH:mm:ss')
                et = dayjs()
                    .endOf('month')
                    .format('YYYY-MM-DD HH:mm:ss')
            } else if (tag == 'year') {
                bt = dayjs()
                    .startOf('year')
                    .format('YYYY-MM-DD HH:mm:ss')
                et = dayjs()
                    .endOf('year')
                    .format('YYYY-MM-DD HH:mm:ss')
            }
            api.getHistoryAnalysis({
                tag: tag,
                bt: bt,
                et: et,
            }).then((res) => {
                if (res.data.sourceCounts) {

                    res.data.sourceCounts.forEach((s) => {
                        state.source = Object.assign(
                            state.source, {
                                ['t' + s.sourceId]: s.count
                            }
                        )
                    });

                    drawEchart(res.data.levelCounts, tag)
                }
            })
        }

        const drawEchart = (levelData, tag) => {
            state.xData = []
            state.seriesData = []
            state.data = [
                [],
                [],
                []
            ]
            if (tag == 'day') {
                for (let i = 0; i < 24; i++) {
                    state.xData.push(i + '时')
                    state.data[0][i] = 0
                    state.data[1][i] = 0
                    state.data[2][i] = 0
                }
            } else if (tag == 'week') {
                state.xData = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
                for (let i = 0; i < 7; i++) {
                    state.data[0][i] = 0
                    state.data[1][i] = 0
                    state.data[2][i] = 0
                }
            } else if (tag == 'month') {
                for (let i = 1; i <= dayjs().daysInMonth(); i++) {
                    state.xData.push(i + '日')
                    state.data[0][i - 1] = 0
                    state.data[1][i - 1] = 0
                    state.data[2][i - 1] = 0
                }
            } else if (tag == 'year') {
                for (let i = 1; i <= 12; i++) {
                    state.xData.push(i + '月')
                    state.data[0][i - 1] = 0
                    state.data[1][i - 1] = 0
                    state.data[2][i - 1] = 0
                }
            }
            if (levelData) {
                levelData.forEach((d) => {
                    if (d.alarmLevel >= 0) {
                        let level = state.data[d.alarmLevel]
                        if (d.levelTimeCounts) {
                            d.levelTimeCounts.forEach((dl) => {
                                if (tag == 'year') {
                                    level[parseInt(dl.date) - 1] = dl.count
                                } else if (tag == 'month') {
                                    level[parseInt(dl.date) - 1] = dl.count
                                } else if (tag == 'week') {
                                    level[parseInt(dl.date)] = dl.count
                                } else if ((tag = 'day')) {
                                    level[parseInt(dl.date)] = dl.count
                                }
                            })
                        }
                    }
                })
            }

            // if (state.chart) {
            //   let option = this.chart.getOption()
            //   option.xAxis[0].data = state.xData
            //   option.series[0].data = state.data[0]
            //   option.series[1].data = state.data[1]
            //   option.series[2].data = state.data[2]
            //   state.chart.setOption(option, true)
            // }
        }

        return {
            ...toRefs(state),
            search,
            drawEchart,
        }
    },
}
</script>

<style lang="scss" scoped>
.analysis_container {
    .wrapper {
        flex: 1;
        height: 600px;

        .content_tab {
            height: 40px;
            line-height: 40px;
        }

        .container_content {
            display: flex;
            flex: 1;
            flex-direction: column;
            height: calc(100% - 50px);

            .content_b,
            .content_t {
                display: flex;
                flex: 1;
                height: calc(50% - 20px);
            }

            .content_t {
                flex-direction: row;
                flex-wrap: wrap;
                color: #cdcdcd;
                margin-top: 20px;

                .item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    width: calc(25% - 20px);
                    height: calc(50% - 15px);
                    border: 1px solid #363b3d;
                    margin: 10px 9px;

                    &:nth-child(4n + 1) {
                        margin-left: 0;
                    }

                    &:nth-child(4n + 4) {
                        margin-right: 0;
                    }

                    .box {
                        display: flex;
                        margin-bottom: 20px;

                        .iconfont {
                            margin: 0 5px;
                        }

                        .name {
                            font-size: 18px;
                            font-family: "PingFangSC-Medium", "PingFang SC";
                            font-weight: 500;
                            color: #ffffff;
                        }

                        .rq {
                            background: linear-gradient(180deg, #ffffff 0%, #ed7327 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .sp {
                            background: linear-gradient(180deg, #ffffff 0%, #e5950d 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .mj {
                            background: linear-gradient(180deg, #ffffff 0%, #ed7327 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .hz {
                            background: linear-gradient(180deg, #ffffff 0%, #e5950d 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .sb {
                            background: linear-gradient(180deg, #ffffff 0%, #27edbb 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .dq {
                            background: linear-gradient(180deg, #ffffff 0%, #27a6ed 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .lx {
                            background: linear-gradient(180deg, #ffffff 0%, #27edbb 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .qt {
                            background: linear-gradient(180deg, #ffffff 0%, #27a6ed 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }
                    }

                    .total {
                        width: 78%;
                        height: 50px;
                        line-height: 50px;
                        background: rgba(255, 255, 255, 0.05);
                        text-align: center;
                        font-size: 25px;
                        font-family: "DINAlternate-Bold", "DINAlternate";
                        font-weight: bold;
                        color: #ffffff;
                    }
                }
            }
        }
    }
}
</style>
