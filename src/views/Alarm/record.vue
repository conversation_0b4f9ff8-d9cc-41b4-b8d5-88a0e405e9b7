<template>
<div class="record_container">
    <el-form :inline="true" class="search_box">
        <el-form-item size="small" label="时间选择">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="search">
            </el-date-picker>
        </el-form-item>
    </el-form>

    <el-table class="table" height="530" :data="list" style="width: 100%" fit table-layout="auto">
        <template #empty>
            <noData />
        </template>
        <el-table-column prop="createTime" label="报警时间" align="center">
        </el-table-column>
        <el-table-column prop="alarmSource" label="报警类型" align="center">
            <template #default="scope">
                <span>{{ getSourceName(scope.row.alarmSource) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="alarmLevel" label="报警级别" align="center">
            <template #default="scope">
                <span>{{ getLevelName(scope.row.alarmLevel) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="alarmDesc" label="处理内容" align="center">
        </el-table-column>
        <el-table-column prop="deviceName" label="报警设备" align="center">
        </el-table-column>
        <el-table-column prop="updateTime" label="处理时间" align="center">
        </el-table-column>
        <el-table-column prop="alarmVideoUrl" label="报警录像" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="download(scope.row.alarmVideoUrl, 1)">查看
                </el-button>
            </template>
        </el-table-column>
        <el-table-column prop="alarmSnapUrl" label="报警抓拍" align="center">
            <template #default="scope">
                <el-button type="text" class="editBtn" @click="download(scope.row.alarmSnapUrl, 2)">查看
                </el-button>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>

</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs
} from 'vue'
import {
    computed,
    
    onMounted,
    watch
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import { useAppStore } from '@/stores/app'


export default {
    name: "alarmrecord",
    setup() {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            date: [],
            page: 1,
            size: 10,
            total: 0,
            status: '',
            list: [],
            sources: [],
            levels: [],
        })

        onMounted(() => {
            state.date.push(dayjs(dayjs().format('YYYY-MM-DD 00:00:00')))
            state.date.push(dayjs(dayjs().format('YYYY-MM-DD 23:59:59')))
            getSources()
            getLevels()
            getHistroyRecordPage()
        })

        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getSources()
                getLevels()
                getHistroyRecordPage()
            }
        })
        const getSources = () => {
            api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'alarm_type'
            }).then(res => {
                state.sources = res.data
            })
        }
        const getLevels = () => {
            api.getDicUtil({
                projectId: getCookie("gh_projectId"),
                dicCode: 'alarm_level'
            }).then(res => {
                state.levels = res.data
            })
        }
        const getHistroyRecordPage = () => {
            api.getHistroyRecord({
                projectId: getCookie("gh_projectId"),
                status: true,
                page: state.page,
                size: state.size,
                bt: dayjs(state.date[0]).format('YYYY-MM-DD 00:00:00'),
                et: dayjs(state.date[1]).format('YYYY-MM-DD 23:59:59'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getHistroyRecordPage()
        }
        const getSourceName = (data) => {
            let name = ''
            state.sources.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const getLevelName = (data) => {
            let name = ''
            state.levels.forEach((d) => {
                if (d.tagValue == data) {
                    name = d.tagName
                    return false
                }
            })
            return name
        }
        const search = () => {
            state.page = 1
            getHistroyRecordPage()
        }
        const download = (url, type) => {
            if (!url) {
                if (type == 1) {
                    ElMessage({
                        type: 'error',
                        message: '无录像查看',
                    })
                    return
                } else if (type == 2) {
                    ElMessage({
                        type: 'error',
                        message: '无图片查看',
                    })
                }
                return
            }
            window.open(url, '_blank') // 新开窗口下载
        }
        return {
            ...toRefs(state),
            projectId,
            getHistroyRecordPage,
            handleCurrentChange,
            getSourceName,
            getLevelName,
            search,
            download,
            getSources,
            getLevels
        }
    },

}
</script>

<style lang="scss" scoped>
.record_container {
    padding: 0 15px;
}
</style>
