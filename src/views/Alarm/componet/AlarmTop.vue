<template>
<div class="energyTop">
    <div class="box">
        <top-echarts :yAxisData="yAxisData" :echartData="echartData"></top-echarts>
    </div>
</div>
</template>

<script lang="ts">
import {
    reactive,
    defineComponent,
    toRefs
} from "vue";
import topEcharts from "@/components/echarts/topEchart.vue";
export default defineComponent({
    components: {
        topEcharts,
    },
    props:['yAxisData','echartData'],
    setup() {
        const state = reactive({
        
          
        });

        return {
            ...toRefs(state),
        };
    },
});
</script>

<style lang="scss" scoped>
.energyTop {
    width: 100%;
    height: calc(100% - 50px);

    .box {
        height: 100%;
    }
}
</style>
