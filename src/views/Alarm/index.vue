<template>
<div class="">
    <div class="left">
        <div class="header flex-start">
            <img src="./img/head.png">
            <div> 防区列表</div>
        </div>
        <div class="input">
            <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按防区名称搜索"></el-input>
        </div>
        <div class="device">
            <el-scrollbar v-if="list.length>0">
                <div class="list space-between" v-for="item in list" :key="item.id">
                    <div class="center cursor">
                        <img src="./img/feng.png" />
                        <div class="name" @click="showDetail(item)">{{item.name}}</div>
                    </div>
                    <div class="center state">
                        <!-- <div v-for="(p,j) in item.state" :key="j">11</div> -->
                    </div>
                    <div class="position cursor">
                        <img src="./img/position.png" />
                    </div>
                </div>
            </el-scrollbar>
            <noData  v-else/>
        </div>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>

    </div>

    <pop :show="activeMenus.popName?true:false" :title="activeMenus.name||''">
        <Transition name="fade" mode="out-in" appear>
        <component :is="activeMenus.popName"></component>
        </Transition>
    </pop>

    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='运行统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">防区总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>10</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">在线数量:</span><span class="num">20</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">离线数量:</span><span class="num">10</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='报警统计' />
            <div class="item-body kong">
                <Alarm :xAxisData="xAxisData" :echartData="echartData" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='事件列表' />
            <div class="item-body event">
                <el-scrollbar>
                    <div class="list " v-for="i in 10" :key="i">
                        <div class="name">
                            <div>地点1</div>
                        </div>
                        <div>
                            <div>张三</div>
                        </div>
                        <div class="time">
                            2023-02-27 10:00:00
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>

</div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    onMounted,
    watch,
} from 'vue';


import {
    getCookie
} from "@/utils/cookie";

import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop/index.vue'
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "alarm",
    components: {
        Alarm,
        pop
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [],
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],

        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
        onMounted(() => {
            getDeviceList();
        });
        const getDeviceList = async () => {
            let {
                data,
                total
            } = await api.getPatrolPoint({
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            state.list = data;

        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const search = () => {
            state.page = 1;
            getDeviceList();
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>
.left {
    .header {
        font-size: 16px;
        font-family: "DOUYU";
        font-weight: 400;
        color: #E6F4FF;
    }

    .input {
        margin-bottom: 12px;
    }

    .device {
        height: calc(100% - 130px);

        .list {
            background: rgba(16, 52, 87, 0.25);
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #FFFFFF;
            margin-bottom: 8px;
            border-left: 1px solid #4274A3;
            height: 52px;

            .state {
                div {
                    margin-right: 48px;
                }

                &>:last-child {
                    margin-right: unset;
                }
            }
        }

    }
}

.right {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .item {
        height: calc(100% / 3);
        // flex: 1;

        &-body {
            display: flex;
            justify-content: center;
            padding: 10px;
        }
    }

    .order {
        display: flex;
        justify-content: center;
        align-items: center;

        .text {
            font-size: 16px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #EAEBEC;
            margin-right: 20px;
        }

        .num {

            font-size: 26px;
            font-family: "BEBAS";
            font-weight: 400;
            color: #F3F3F3;
        }

        &-left {
            background: url("./img/d6.png") no-repeat center/100%;
            width: 155px;
            height: 120px;

            display: flex;
            justify-content: center;
            align-items: flex-start;

            .center {

                font-size: 32px;
                font-family: "BEBAS";
                font-weight: 400;
                color: #FFFFFF;
                background: linear-gradient(0deg, #FFFFFF 0%, #1196FC 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }

        &-right {
            width: 214px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        &-text {
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .dot {
                width: 5px;
                height: 5px;
                margin-right: 7px;
            }
        }

        .total {
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #E9EAEB;

        }
    }

    .kong {
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
        color: #E6F0F6;
        flex-direction: column;
        height: calc(100% - 60px);

        .name {
            margin-right: 7px;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .btn {
            width: 85px;
            height: 44px;
            background: url('./img/d5.png') no-repeat;
            background-size: 100%;
            margin-left: 7px;

            &>div {
                flex: 1
            }

            &>div>div {
                height: 32px;
                width: 32px;
                background: url("./img/d2.png") no-repeat;
                background-size: 100%;
            }
        }

        .list {
            width: 100%;
            background: rgba(16, 52, 87, 0.25);
            margin-bottom: 6px;
        }

        .dot {
            height: 1px;
            border-bottom: 1px dashed #6E94BA;
            flex: 1
        }
    }

    .event {
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
        color: #E6F0F6;

        flex-direction: column;

        height: calc(100% - 60px);

        .name {

            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: "Alibaba-PuHuiTi";
            font-weight: normal;
            color: #F0F9FF;
        }

        .time {

            font-size: 18px;
            font-family: "BEBAS";
            font-weight: 400;
            color: #C3D2E0;
        }

        .list {
            width: 100%;
            background: rgba(16, 52, 87, 0.25);
            margin-bottom: 6px;
            height: 40px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .bar {
            width: 7px;
            height: 1px;
            background: #466582;
            position: absolute;
            top: 0;
            right: 0;
        }

    }

}

.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("./img/tab.png") no-repeat;
            width: 104px;
            height: 40px;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("./img/tab_active.png") no-repeat !important;
    }

}
</style>
