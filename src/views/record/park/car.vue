<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box">
        <el-form-item label="车牌号:">
            <el-input placeholder="请输入车牌号" v-model="keyword"></el-input>
        </el-form-item>

        <el-form-item>
            <div class="searchBtn" type="text" @click="search">查询</div>
        </el-form-item>
    </el-form>

    <el-table :data="list" height="calc(100% - 80px)" fit table-layout="auto">
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="plateNo" label="车牌号" align="center">
        </el-table-column>
        <el-table-column prop="personName" label="车主姓名" align="center">
        </el-table-column>
        <el-table-column prop="phoneNo" label="车主电话" align="center">
        </el-table-column>
        <el-table-column prop="phoneNo" label="车辆类型" align="center">
            <!-- <template #default="scope">
                <span>{{ vtype[scope.row.vehicleType]}}</span>
            </template> -->
        </el-table-column>
        <el-table-column prop="mark" label="备注" align="center"> </el-table-column>

    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'

export default {
    name: 'parkcar',
    setup() {
        const api = inject('$api')
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            keyword: null,
            vtype: ["其他车", "小型车", "大型车", "摩托车"]
        })
        onMounted(() => {
            getCar()
        })
        const getCar = () => {
            api.vehicleList({
                page: state.page,
                size: state.size,
                plateNo: state.keyword
            }).then((res) => {
                state.total = res.data.total
                state.list = res.data.list
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getCar();
        }
        const search = (page) => {
            state.page = 1
            getCar();
        }
        return {
            ...toRefs(state),
            handleCurrentChange,
            search
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
