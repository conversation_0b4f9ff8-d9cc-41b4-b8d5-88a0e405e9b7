<template>
    <div class="wrapper">
        <el-form :inline="true" class="search_box">
            <el-form-item label="设备ip:">
                <el-input placeholder="设备ip查询" v-model="keyword"></el-input>
            </el-form-item>

            <el-form-item>
                <div class="searchBtn" @click="search" type="text">查询</div>
            </el-form-item>
        </el-form>

        <el-table :data="list"  fit table-layout="auto" height="calc(100% - 90px)">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="cn" label="设备名称" align="center"> </el-table-column>
            <el-table-column prop="ip" label="设备ip" align="center">
            </el-table-column>
            <el-table-column prop="port" label="设备端口" align="center">
            </el-table-column>
            <el-table-column prop="regionName" label="区域" align="center">
            </el-table-column>
            <el-table-column prop="deviceType" label="设备类型" align="center">
            </el-table-column>
            <el-table-column prop="date" label="状态" align="center">
                <template #default="scope">
                    <span style="color:green" v-if="scope.row.online == 1">在线</span>
                    <span style="color:red" v-if="scope.row.online == 0">离线</span>
                    <span style="color:red" v-if="scope.row.online == -1">未知</span>
                </template>
            </el-table-column>
        </el-table>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
</template>

    
<script>
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'

export default {
    name: 'AcsUser',
    setup() {
        const api = inject('$api')
        const state = reactive({
            page: 1,
            size: 20,
            total: 0,
            list: [],
            keyword: null,
        })

        onMounted(() => {
            getDoorLog()
        })
        const getDoorLog = () => {
            api.getHKController({
                page: state.page,
                size: state.size,
                ip: state.keyword
            }).then((res) => {
                state.list = res.data.data.list;
                state.total = res.data.data.total;
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getDoorLog()

        }

        const search = () => {
            state.page = 1
            getDoorLog()

        }
        return {
            ...toRefs(state),
            getDoorLog,
            handleCurrentChange,
            search
        }
    },
}
</script>

    
<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
