<template>
    <div class="wrapper">
        <el-form :inline="true" class="search-form">
            <el-form-item label="时间选择:">
                <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" @change="getEvent">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="名称:">
                <el-input v-model="keyword" placeholder="门禁名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button class="search-btn enhanced-search-btn" @click="search" type="primary">查询</el-button>
            </el-form-item>
        </el-form>

        <el-table :data="list"  fit table-layout="auto" height="calc(100% - 90px)">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="personName" label="姓名" align="center">
            </el-table-column>
            <el-table-column prop="doorName" label="门禁" align="center">
            </el-table-column>
            <el-table-column prop="devName" label="控制器" align="center">
            </el-table-column>
            <el-table-column prop="eventTime" label="时间" align="center">
                <template #default="scope">
                    <span>{{ scope.row.eventTime ? scope.row.eventTime.replace("T", " ").replace("+08:00", "") : "" }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="inAndOutType" label="方向" align="center">
                <template #default="scope">
                    <span v-if="scope.row.inAndOutType == 1">进</span>
                    <span v-if="scope.row.inAndOutType == 0">出</span>
                    <span v-if="scope.row.inAndOutType == -1">未知</span>
                </template>
            </el-table-column>
        </el-table>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import {
    onMounted,
    reactive,
    toRefs,
    inject
} from 'vue'
export default {
    name: 'AcsRecord',
    setup() {
        const api = inject('$api')
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            date: [],
            page: 1,
            size: 20,
            total: 0,
            list: [],
            keyword: "",
        })
        dayjs.extend(utc)
        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));
        state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'));

        onMounted(() => {
            console.log(dayjs().format())
            getDoorLog()
        })
        const getDoorLog = () => {
            api.getHKDoorEvent({
                page: state.page,
                size: state.size,
                bt: dayjs(state.date[0]).format("YYYY-MM-DDTHH:mm:ss") + "+08:00",
                et: dayjs(state.date[1]).format("YYYY-MM-DDTHH:mm:ss") + "+08:00",
                doorName: state.keyword
            }).then((res) => {
                state.list = res.data;
                state.total = res.total;
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getDoorLog()
        }
        const search = () => {
            state.page=1;
            getDoorLog();
        }
        return {
            ...toRefs(state),
            getDoorLog,
            handleCurrentChange,
            search
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
