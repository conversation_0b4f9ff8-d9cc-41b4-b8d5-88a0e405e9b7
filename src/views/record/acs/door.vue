<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box">
        <el-form-item label="名称:">
            <el-input placeholder="按名称查询" v-model="keyword"></el-input>
        </el-form-item>

        <el-form-item>
            <div class="searchBtn" @click="search" type="text">查询</div>
        </el-form-item>
    </el-form>

    <el-table :data="list"  fit table-layout="auto" height="calc(100% - 90px)">
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="name" label="门禁名称" align="center">
        </el-table-column>
        <el-table-column prop="doorNo" label="门禁通道" align="center">
        </el-table-column>
        <el-table-column prop="channelNo" label="控制器通道" align="center">
        </el-table-column>
        <el-table-column prop="doorState" label="门禁状态" align="center">
            <template #default="scope">
                <span style="color:green" v-if="scope.row.doorState==1">{{ status[scope.row.doorState] }}</span>
                <span style="color:red" v-if="scope.row.doorState==2">{{ status[scope.row.doorState] }}</span>
                <span style="color:orange" v-if="scope.row.doorState==3">{{ status[scope.row.doorState] }}</span>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'

export default {
    name: 'AcsDoor',
    setup() {
        const api = inject('$api')
        const state = reactive({
            date: [],
            page: 1,
            size: 20,
            total: 0,
            list: [],
            status:['初始状态' ,'开门状态','关门状态','离线状态'],
            keyword: null,
        })

        onMounted(() => {
            getDoorLog()
        })
        const getDoorLog = () => {
            api.getHKDoor({
                page: state.page,
                size: state.size,
                ip: state.keyword
            }).then((res) => {
                state.list = res.data;
                state.total = res.total;
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getDoorLog()

        }

        const search = () => {
            state.page = 1
            getDoorLog()

        }
        return {
            ...toRefs(state),
            getDoorLog,
            handleCurrentChange,
            search
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
