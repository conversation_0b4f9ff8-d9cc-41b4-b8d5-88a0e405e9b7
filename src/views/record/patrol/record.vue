<template>
<div class="wrapper">
    <el-form :inline="true" class="search_box">
        <el-form-item label="时间选择:">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="getEvent">
            </el-date-picker>
        </el-form-item>

        <el-form-item>
            <div class="searchBtn" type="text">查询</div>
        </el-form-item>
    </el-form>

    <el-table :data="list" height="calc(100% - 80px)" fit table-layout="auto">
        <template #empty>
            <no-data />
        </template>
        <el-table-column prop="cardNumber" label="卡号" align="center">
        </el-table-column>
        <el-table-column prop="personName" label="姓名" align="center">
        </el-table-column>
        <el-table-column prop="deviceName" label="门禁" align="center">
        </el-table-column>
        <el-table-column prop="channelName" label="控制器" align="center">
        </el-table-column>
        <el-table-column prop="swingTime" label="时间" align="center">
        </el-table-column>
        <el-table-column prop="enterOrExit" label="方向" align="center">
            <template #default="scope">
                <span v-if="scope.row.enterOrExit==1">进</span>
                <span v-if="scope.row.enterOrExit==2">出</span>
            </template>
        </el-table-column>
    </el-table>

    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCurrentInstance,
    onMounted,
    reactive,
    toRefs
} from 'vue'

export default {
    name: 'dzxgrecord',
    setup() {
        const api = inject('$api')
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            date: [],
            page: 1,
            size: 10,
            total: 0,
            list: [],
        })
        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));
        state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'));
        onMounted(() => {
            // getDoorLog()
            // getDoorLogCount()
        })
        const getDoorLog = () => {
            api.getDoorLog({
                page: state.page,
                size: state.size,
                bt: typeof state.date[0] == 'string' ? state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ? state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                // state.total = res.data.totalRows
                state.list = res.data.pageData
            })
        }
        const getDoorLogCount = () => {
            api.getDoorLogCount({
                page: state.page,
                size: state.size,
                bt: typeof state.date[0] == 'string' ? state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ? state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then(res => {
                state.total = res.data;
            });

        }
        const handleCurrentChange = (page) => {
            state.page = page
            getDoorLog()
            getDoorLogCount()
           
            
        }
        return {
            ...toRefs(state),
            getDoorLog,
            getDoorLogCount,
            handleCurrentChange,
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}
</style>
