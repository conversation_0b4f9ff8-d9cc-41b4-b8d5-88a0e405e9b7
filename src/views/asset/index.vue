<template>
    <div class="">
        <div class="left">
            <div class="header flex-start">
                <img src="@/assets/images/common/head.png">
                <div> 资产列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="资产名称搜索"></el-input>
            </div>
            <div class="device">

                <el-scrollbar>
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <span class="iconfont iconkucunfenxi"></span>
                            </div>
                            <div class="name">{{ item.assetsName }}</div>
                        </div>
                        <div class="center state">
                            <!-- <div v-for="(p,j) in item.state" :key="j">11</div> -->
                        </div>
                        <div class="position cursor">
                            <img src="@/assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>
        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>
        <div class="right">
            <div class="item" style="flex:1">
                <sub-title title='资产统计' />
                <div class="item-body order">
                    <div>
                        <div class="total center">设备总数</div>
                        <div class="order-left">
                            <div class="center">
                                <div>{{ all }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text">
                            <div class="dot" style="background:#1AAC1A"></div><span class="text">在用数量:</span><span
                                class="num">{{ on }}</span>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C47F13"></div><span class="text">报废数量:</span><span
                                class="num">{{ off }}</span>
                        </div>

                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title title='资产分类' />
                <div class="item-body kong">
                    <pie :data="echartData"></pie>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title title='报废资产' />
                <div class="item-body event">
                    <el-scrollbar>
                        <div class="list " v-for="i in fault" :key="i.id">
                            <div class="name">
                                <div>{{ i.assetsName }}</div>
                            </div>
                            <div>
                                <div>{{ i.createName }}</div>
                            </div>
                            <div class="time">
                                {{ i.updateTime }}
                            </div>

                            <div class="bar"></div>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import {
    defineComponent,
    getCurrentInstance,
    reactive,
    toRefs,
    computed,
    onMounted,
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import Alarm from '@/components/echarts/weekEventEchart.vue'
import pop from '@/components/pop/index.vue'
import pie from './pie.vue'
import allocate from '../Capital/allocate.vue';
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "asset",
    components: {
        Alarm,
        pop,
        pie
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            on: 0,
            off: 0,
            all: 0,
            list: [],
            echartData: [],
            fault: []

        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        onMounted(() => {
            getListByCondition();
            getAssetType()
            getAssetState()
            getFault();
        });
        const getAssetType = () => {
            api.getAssetType({
            }).then((res) => {
                state.echartData = res.data;
            })
        }


        const getAssetState = () => {
            api.getAssetState({
            }).then((res) => {
                res.data.forEach(item => {
                    if (item.name == 1) {
                        state.on = item.value
                    } else if (item.name == 2) {
                        state.off = item.value
                    }
                    state.all += item.value
                });
            })
        }

        const getListByCondition = () => {
            let data = {
                page: state.page,
                size: state.size,
                keyword: state.keyword,
                projectId: getCookie("gh_projectId")
            };
            api.getInRecord(data).then((res) => {
                state.list = res.data;
                state.total = res.total;
                state.fault = [];
                res.data.forEach(item => {
                    if (item.actionStatus == 1) {
                        // state.on++
                    } else if (item.actionStatus == 2) {
                        // state.off++
                        state.fault.push(item)//报废资产
                    } else if (item.actionStatus == 3) {//待处理资产

                    }
                });
            });
        };

        const getFault = () => {
            let data = {
                projectId: getCookie("gh_projectId"),
                status:2
            };
            api.getInRecord(data).then((res) => {

                state.fault = [];
                res.data.forEach(item => {
                    if (item.actionStatus == 1) {
                        // state.on++
                    } else if (item.actionStatus == 2) {
                        // state.off++
                        state.fault.push(item)//报废资产
                    } else if (item.actionStatus == 3) {//待处理资产

                    }
                });
            });
        };






        const handleCurrentChange = (page) => {
            state.page = page;
            getListByCondition();
        }

        const search = () => {
            state.page = 1;
            getListByCondition();
        }

        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped></style>
