<template>
    <div class="h100">
        <el-form :inline="true" class="search_box " size="small">
            <el-form-item label="报修时间:">
                <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="报修人:">
                <el-select placeholder="请选择" v-model="userId" clearable>
                    <el-option v-for="item in users" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <div size="small" class="searchBtn" type="text" @click="search">查询</div>
            </el-form-item>
            <div class="btn-group">
                <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addRepair">发起报修</div>
            </div>
        </el-form>

        <el-table class="table" :data="list" height="calc(100% - 65px)" fit table-layout="auto">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="code" label="编号" align="center" width="170px">
            </el-table-column>
            <el-table-column prop="areaName" label="所属区域" align="center">
            </el-table-column>
            <el-table-column prop="deviceName" label="设备名称" align="center">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="170px">
            </el-table-column>
            <el-table-column prop="repairPerson" label="报修人" align="center">
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" align="center" width="150px">
            </el-table-column>
            <el-table-column prop="status" label="状态" align="center">
                <template #default="scope">
                    <span :class="scope.row.status == 1 ? 'djd' : 'gq'">{{
            scope.row.status == 1 ? '待处理' : scope.row.status == 2 ? '处理中' : scope.row.status == 3 ? '已结案' :
                '--'
        }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="orderCode" label="工单编号" align="center" show-overflow-tooltip width="160px">
                <template #default="scope">
                    <el-tag @click="showProcess(scope.row)" class="cursor" v-if="scope.row.orderCode">{{
            scope.row.orderCode
        }}</el-tag>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="username" label="报修人" align="center">
            </el-table-column> -->
            <el-table-column type="expand" label="报修照片" width="100px">
                <template #default="scope">
                    <el-image preview-teleported="true" class="image-list" :preview-src-list="JSON.parse(scope.row.paths)"
                        v-for="(item, i) in JSON.parse(scope.row.paths)" :key="i" :fit="fit"
                        style="width: 100px; height: 100px" :src="item"></el-image>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="130px">
                <template #default="scope">
                    <el-button type="text" class="editBtn" :disabled="scope.row.status == 3 || scope.row.orderCode"
                        @click="createOrder(scope.row)">转工单</el-button>
                    <el-button type="text" class="editBtn" :disabled="scope.row.status == 3 || scope.row.orderCode"
                        @click="Close(scope.row)">结案</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="center page">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
        <repair-dialog v-if="dialogData.visible" :dialog-data="dialogData" @getFaultPage="getFaultPage" />
        <order-dialog v-if="orderDialogData.visible" :dialog-data="orderDialogData" @getFaultPage="getFaultPage" />
        <el-dialog align-center :append-to-body="true" draggable v-model="closeDialog.visible" custom-class="addDiagram"
            :title="closeDialog.title" width="500px">
            <el-form class="form" ref="form" :model="closeDialog.form" :rules="closeDialog.rule">
                <el-form-item label="结案描述：" prop="remark">
                    <el-input type="textarea" placeholder="请输入内容" v-model="closeDialog.form.remark"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" size="small" class="saveBtn" @click="saveRepair('form')">确 定</el-button>
                </div>
            </template>
        </el-dialog>
        <el-dialog align-center :append-to-body="true" draggable v-model="process" custom-class="addDiagram"
            title="工单详情" width="500px">
            <div class="content card-body">
                <el-timeline class="time">
                    <el-timeline-item v-for="(item, index) in points" :key="index" :timestamp="item.start_TIME_"
                        placement="top" v-show="item.act_NAME_">
                        <div class="point-item">
                            <h3>{{ item.act_NAME_ }}</h3>
                            <h4>处理人:{{ item.name ? item.name : (index==0?item.createName:'') }}</h4>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import repairDialog from './components/repairDialog.vue'
import orderDialog from './components/orderDialog.vue'
import dayjs from 'dayjs'
import {
    ElMessage
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    reactive,
    toRefs,
    onMounted,
    
    computed,
    watch,
    onUnmounted
} from 'vue'
import { useAppStore } from '@/stores/app'

export default {
    name: 'repair',
    components: {
        repairDialog,
        orderDialog
    },
    setup() {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            types: [], // 巡检项数据类型  状态 数值
            date: [],
            type: '',
            users: [],
            staff: [],
            userId: '',
            dialogData: {
                visible: false,
                title: '新增',
                repair: {
                    deviceId: null,
                    description: '',
                    name: '',
                },
                rule: {
                    name: [{
                        required: true,
                        message: '报修名称不能空',
                        trigger: 'blur',
                    },],

                    description: [{
                        required: true,
                        message: '描述不能空',
                        trigger: 'change',
                    },]
                }
            },
            closeDialog: {
                visible: false,
                title: '结案',
                form: {
                    remark: ''
                },
                rule: {
                    remark: [{
                        required: true,
                        message: '描述不能空',
                        trigger: 'change',
                    }]
                }
            },
            orderDialogData: {
                visible: false,
                title: '',
                order: {

                },
                rule: {
                    name: [{
                        required: true,
                        message: '区域不能空',
                        trigger: 'blur',
                    }],
                    leader: [{
                        required: true,
                        message: '所属专业不能为空',
                        trigger: 'change',
                    },],
                    description: [{
                        required: true,
                        message: '描述不能空',
                        trigger: 'change',
                    }]
                }
            },
            process: false,
            points: [],
            interval: null,
        })

        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getUserList()
            getFaultPage()
            // getStaff();
            state.interval = setInterval(() => {
                getFaultPage()
            }, 2000);
        })
        onUnmounted(() => {
            clearInterval(state.interval)
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getUserList()
                getFaultPage()
            }
        })
        const getFaultPage = () => {
            api.getFault({
                projectId: getCookie('gh_projectId'),
                page: state.page,
                size: state.size,
                userId: state.userId,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const getUserList = () => {
            api.getUser({
                projectId: [getCookie('gh_projectId')],
                status: 1,
            }).then((res) => {
                state.users = res.data
            })
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getFaultPage()
        }
        const search = () => {
            state.page = 1
            getFaultPage()
        }
        const addRepair = () => {
            state.dialogData.visible = true
            state.dialogData.title = '发起报修'
            state.dialogData.repair = {
                deviceId: null,
                description: '',
                name: '',
            }
        }
        // 转工单
        const createOrder = (data) => {
            state.orderDialogData.visible = true
            state.orderDialogData.title = '转工单'
            state.orderDialogData.order = Object.assign({}, data)
        }
        const Close = (data) => {
            state.closeDialog.visible = true
            state.closeDialog.form = data
        }
        const saveRepair = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    api.updateFault({
                        areaName: state.closeDialog.form.areaName,
                        code: state.closeDialog.form.code,
                        createTime: state.closeDialog.form.createTime,
                        creator: state.closeDialog.form.creator,
                        description: state.closeDialog.form.description,
                        deviceId: state.closeDialog.form.deviceId,
                        deviceName: state.closeDialog.form.deviceName,
                        id: state.closeDialog.form.id,
                        name: state.closeDialog.form.name,
                        orderCode: state.closeDialog.form.orderCode,
                        orderId: state.closeDialog.form.orderId,
                        paths: state.closeDialog.form.paths,
                        projectId: state.closeDialog.form.projectId,
                        typeName: state.closeDialog.form.typeName,
                        username: state.closeDialog.form.username,
                        status: 3,
                        remark: state.closeDialog.form.remark
                    }).then(res => {
                        if (res.success) {
                            state.closeDialog.visible = false
                            getFaultPage()
                            ElMessage({
                                type: 'success',
                                message: res.msg
                            })
                        }
                    })
                } else {
                    return false
                }
            })

        }
        const showProcess = (row) => {
            state.process = true;
            if (row.instanceId) {
                getOrderProcess(row.instanceId);
            }

        }
    

        const getOrderProcess = (id) => {
            api.getPatrolProcess({
                instanceId: id,
            }).then((res) => {
                state.points = res.data
            })
        }
        return {
            ...toRefs(state),
            getUserList,
            getFaultPage,
            handleCurrentChange,
            search,
            addRepair,
            Close,
            createOrder,
            saveRepair,
            projectId,
            showProcess
        }
    },
}
</script>

<style lang="scss" scoped>
.h100 {
    .image-list {
        margin: 5px 8px;
    }

    .djd {
        color: #e3731b;
    }

    .gq {
        color: #13d4d9;
    }
}
</style>
