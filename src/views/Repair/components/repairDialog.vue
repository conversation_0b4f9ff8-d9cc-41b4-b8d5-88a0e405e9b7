<template>
    <div>
        <el-dialog align-center :append-to-body="true" draggable custom-class="addDiagram border0" @open="open"
            v-model="dialogData.visible" width="940px" :title="dialogData.title">
            <el-form class="form" ref="form" :model="dialogData.repair" :rules="dialogData.rule" label-width="100px">
                <el-row type="flex" :gutter="30">
                    <el-col :span="12">
                        <el-form-item label="报修主题:" prop="name">
                            <el-input placeholder="请输入名称" v-model="dialogData.repair.name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人:" prop="repairPerson">
                            <el-select v-model="dialogData.repair.repairPerson" clearable filterable @change="changePerson">
                                <el-option v-for="item in staff" :label="item.name" :key="item.name"
                                    :value="item.name"></el-option>
                            </el-select>
                            <!-- <el-input v-model="dialogData.repair.repairPerson"></el-input> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系方式:" prop="phone">
                            <el-input v-model="dialogData.repair.phone"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="设备类型:">
                            <el-select @change="changeDeviceType" v-model="dialogData.repair.type" class="w100"
                                filterable clearable>
                                <el-option v-for="item in types" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="选择区域:">
                            <el-cascader popper-class="cascader" v-model="dialogData.repair.areaId" :options="areas"
                                clearable :props="props" placeholder="请选择" @change="getDevicePage"></el-cascader>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="设备选择:">
                            <el-select popper-class="cascader" ref="device" @change="changeDevice" clearable
                                v-model="dialogData.repair.deviceId">
                                <el-option v-for="item in devices" :key="item.id" :label="item.name"
                                    :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="故障描述:" prop="description">
                    <el-input type="textarea" placeholder="请输入内容" v-model="dialogData.repair.description"></el-input>
                </el-form-item>
                <el-form-item label="图片上传" prop="content" class="upload">
                    <el-upload class="upload-demo" :on-preview="handlePictureCardPreview" :headers="headers"
                        :action="action" name="files" :data="param" list-type="picture-card" :file-list="imgs"
                        :on-remove="handleRemove" :on-success="upload">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer search_box">
                    <div type="primary" size="mini" class="searchBtn" :disabled="loading" @click="saveRepair('form')">确
                        定</div>
                </div>
            </template>
        </el-dialog>
        <el-dialog align-center :append-to-body="true" draggable v-model="dialogVisible" title="图片预览"
            custom-class="custom_dialog">
            <img style="width:100%;height:100%" :src="dialogImageUrl" alt="Preview Image" />
        </el-dialog>
    </div>
</template>

<script>
import {
    ElMessage
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'

import {
    computed,
    
    nextTick,
    onMounted,
    ref,
    watch
} from 'vue'
import { useAppStore } from '@/stores/app'


export default {
    props: {
        dialogData: {
            visible: false,
            title: '',
            repair: {
                deviceId: null,
                description: '',
                name: '',
                areaId: null,
                type: null,
            },
        }
    },
    setup(props) {
        const api = inject('$api')
        const store = useAppStore()
        const state = reactive({
            action: (process.env.NODE_ENV == "development" ? window.DEV_BASE_API : window.PROD_BASE_API) + "/filecenter-service/upload",
            devices: [],
            headers: {
                Authorization: 'bearer ' + getCookie('gh_token'),
            },
            imgs: [],
            staff: [],
            param: {
                fileType: 0,
                projectId: getCookie('gh_projectId'),
            },
            props: {
                label: 'name',
                value: 'id',
            },
            areaProps: {
                label: 'name',
                value: 'id',
                // checkStrictly: true
            },

            paths: [],
            dialogVisible: false,
            dialogImageUrl: '',
            loading: false,
            leader: 0,
            types: [],
            typeSelect: null,

        })
        const areas = ref([])
        onMounted(() => {
            nextTick(() => {
                getProjectArea()
            });
            getStaff()
            getType()
        })

        const open = () => {

        }
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectArea()
            }
        })
        const getStaff = () => {
            api.getProjectStaff({
                projectId: getCookie('gh_projectId')
            }).then((res) => {
                state.staff = res.data
            })
        }
        const getType = () => {
            api.getProjectDeviceType({
                projectId: getCookie("gh_projectId")
            }).then(res => {
                state.types = res.data;
            });
        }

        const getTreeData = (data) => {
            for (var i = 0; i < data.length; i++) {
                if (data[i].children.length < 1) {
                    data[i].children = undefined;
                } else {
                    getTreeData(data[i].children);
                }
            }
            return data;
        }

        const getProjectArea = () => {
            api.getProjectArea({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                areas.value = res.data

            });
        }
        const upload = (res, file, filelist) => {
            state.paths.push(res.data[0]);
            state.imgs.push({
                name: file.name,
                url: res.data[0],
                uid: file.uid,
            })

        }
        const handleRemove = (file) => {
            if (file) {
                state.imgs.forEach((img, i) => {
                    if (img.uid == file.uid) {
                        state.imgs.splice(i, 1)
                    }
                })
            }
        }

        const changeDeviceType = (val) => {
            props.dialogData.repair.deviceId = null;
            getDevicePage();
        }

        const getDevicePage = () => {
            api.getDeviceInfo({
                projectId: getCookie('gh_projectId'),
                areaId: props.dialogData.repair.areaId?.length ? props.dialogData.repair.areaId[props.dialogData.repair.areaId.length - 1] : null,
                deviceType: props.dialogData.repair.type
            }).then((res) => {
                state.devices = res.data
            })
        }
        const saveRepair = (formName) => {
            if (props.dialogData.repair.deviceId && props.dialogData.repair.deviceId.length > 0) {
                props.dialogData.repair.deviceId = props.dialogData.repair.deviceId[
                    props.dialogData.repair.deviceId.length - 1
                ]
            }

            if (props.dialogData.repair.areaId && props.dialogData.repair.areaId.length > 0) {
                props.dialogData.repair.areaId = props.dialogData.repair.areaId[props.dialogData.repair.areaId.length - 1];
            }

            props.dialogData.repair.paths = JSON.stringify(state.paths)
            props.dialogData.repair.projectId = getCookie('gh_projectId')
            props.dialogData.repair.leader = state.leader;

            proxy.$refs[formName].validate((validate) => {
                if (validate) {
                    state.loading = true;
                    api.addFault(props.dialogData.repair).then((res) => {
                        if (res.success) {
                            props.dialogData.visible = false
                            proxy.$emit('getFaultPage')
                            ElMessage({
                                type: 'success',
                                message: res.msg
                            })
                            state.imgs = [];
                            state.paths = [];
                            state.loading = false;
                        }
                    })
                }
            })
        }
        const changeDevice = (item) => {
            if (item) {
                let data = state.devices.find(d => d.id == item);
                if (data) {
                    api.getLeader({
                        staffType: data.professionType,
                        projectId: getCookie("gh_projectId")
                    }).then(res => {
                        state.leader = res.data;
                    })
                }

            } else {
                state.leader = 0;
            }

        }

        const handlePictureCardPreview = (uploadFile) => {
            state.dialogImageUrl = uploadFile.url
            state.dialogVisible = true
        }
        const changePerson = (val) => {
            if (val) {
                let staff = state.staff.find(s => s.name == val);
                if (staff) {
                    props.dialogData.repair.phone = staff.phone;
                }
            } else {
                props.dialogData.repair.phone = "";
            }
        }

        return {
            ...toRefs(state),
            getDevicePage,
            changeDeviceType,
            upload,
            handleRemove,
            saveRepair,
            changeDevice,
            getProjectArea,
            projectId,
            handlePictureCardPreview,
            open,
            areas,
            changePerson
        }
    }
}
</script>
