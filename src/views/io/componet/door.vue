<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: {
        yAxisData: {
            type: Array,
            default: () => {
                return []
            },
        },
        echartData: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })
        let data = {
            "chart": [{
                    month: "1月",
                    value: 138,
                    ratio: 14.89
                },

                {
                    month: "2月",
                    value: 114,
                    ratio: 79.49
                },

                {
                    month: "3月",
                    value: 714,
                    ratio: 75.8
                },

                {
                    month: "4月",
                    value: 442,
                    ratio: 19.8
                },

                {
                    month: "5月",
                    value: 622,
                    ratio: 44.5
                },

                {
                    month: "6月",
                    value: 528,
                    ratio: 87.3
                }

            ]
        }

        let xAxisMonth = [],
            barData = [],
            lineData = [];
        for (let i = 0; i < data.chart.length; i++) {
            xAxisMonth.push(data.chart[i].month);
            barData.push({
                "name": xAxisMonth[i],
                "value": data.chart[i].value
            });

        }

        let option = {
            title: '',
            grid: {
                top: '10',
                left: '0',
                bottom: '10',
                containLabel: true
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'none'
                },
                formatter: function (params) {
                    return params[0]["data"].name + "<br/>" + '训练人次: ' + params[1]["data"].value + "<br/>" + '合格率: ' + params[0]["data"].value;
                }
            },
            xAxis: {
                type: 'category',
                position: "bottom",
                data: xAxisMonth,
                boundaryGap: true,
                // offset: 40,
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },

                axisLabel: {
                    textStyle: {
                        color: '#b6b5ab'
                    }
                }
            },
            yAxis: {
                show: true,
                offset: 52,
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "rgba(255,255,255,0.2)"
                    }
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: true
                },
                axisLabel: {
                    show: true,
                    color: '#b6b5ab'
                }
            },
            color: ['#e54035'],
            series: [{
                    name: '训练人次',
                    type: 'pictorialBar',
                    // barCategoryGap: '-80%',
                    symbol: 'path://d="M150 50 L130 130 L170 130  Z"',
                    itemStyle: {
                        normal: {
                            color: function (params) {
                                let colorList = [
                                    'rgba(13,177,205,0.8)', 'rgba(29,103,182,0.6)',
                                    'rgba(13,177,205,0.8)', 'rgba(29,103,182,0.6)',
                                    'rgba(13,177,205,0.8)', 'rgba(29,103,182,0.6)'
                                ];
                                return colorList[params.dataIndex];
                            }
                        },
                        emphasis: {
                            opacity: 1
                        }
                    },
                    data: barData,
                },
                {
                    name: '背景',
                    type: 'bar',
                    data: [1000, 1000, 1000, 1000, 1000, 1000],
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,0.1)'
                        }
                    }
                },

            ]
        }
        const initChart = () => {
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 150px;
    width: 100%;
}
</style>
