<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: {
        yAxisData: {
            type: Array,
            default: () => {
                return []
            },
        },
        echartData: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        let color = ["#2b8ef3", "#49e5e5"];
        let option = {
            // backgroundColor: '#031d33',
            tooltip: {
                show: true
            },
            title: {
                text: '总车位:100',
                x: 'center',
                y: 'center',
                textStyle: {
                    fontSize: 12
                }
            },
            series: [{
                    type: "pie",
                    radius: ["60%", "85%"],
                    center: ["50%", "50%"],
                    color: color,
                    hoverAnimation: true,
                    z: 10,
                    itemStyle: {
                        color: (params) => {
                            var index = params.dataIndex;

                            return color[index];
                        },
                        normal: {
                            borderWidth: 5,
                            borderColor: "#020d1a",
                            label: {
                                show: true,
                                fontSize: 12,
                                formatter(params) {
                                    return params.name ?
                                        params.name + '\n' + params.value + '人' :
                                        '';
                                }
                            },
                            labelLine: {
                                width: 4,
                                length: 30,
                                length2: 30,
                                show: true,
                                color: '#00ffff'
                            }
                        }
                    },
                    label: {
                        show: false
                    },
                    data: [{
                        name: '使用',
                        value: 50
                    }, {
                        name: '剩余',
                        value: 23
                    }],
                    labelLine: {
                        show: false
                    }
                },

                {
                    type: "pie",
                    radius: ["90%", "91%"],
                    itemStyle: {
                        color: "#667990"
                    },
                    hoverAnimation: true,
                    label: {
                        show: false
                    },
                    data: [100],
                    labelLine: {
                        show: false
                    }
                },

                { //内圆
                    type: 'pie',
                    radius: '50%',
                    center: ['50%', '50%'],
                    z: 1,
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.RadialGradient(.5, .5, .8, [{
                                    offset: 0,
                                    color: '#122031'
                                },
                                {
                                    offset: .5,
                                    color: '#2c4053'
                                },
                                {
                                    offset: 1,
                                    color: '#587a91'
                                }
                            ], false),
                            label: {
                                show: false
                            },
                            labelLine: {
                                show: false
                            }
                        },
                    },
                    hoverAnimation: false,
                    label: {
                        show: false,
                    },
                    tooltip: {
                        show: false
                    },
                    data: [100],
                    animationType: "scale"
                }

            ]
        }

        const initChart = () => {
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 150px;
    width: 100%;
}
</style>
