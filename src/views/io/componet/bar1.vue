<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: {
        yAxisData: {
            type: Array,
            default: () => {
                return []
            },
        },
        echartData: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        let option = {

            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                top: '15%',
                right: '3%',
                left: '15%',
                bottom: '15%'
            },
            xAxis: [{
                type: 'category',
                data: ['top1', 'top2', 'top3', 'top4', 'top5'],
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,0.12)'
                    }
                },
                axisLabel: {
                    margin: 10,
                    color: '#e2e9ff',
                    textStyle: {
                        fontSize: 14
                    },
                },

            }],
            yAxis: [{
                name: '',
                axisLabel: {
                    formatter: '{value}',
                    color: '#e2e9ff',
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: 'rgba(255,255,255,1)'
                    }
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,0.12)'
                    }
                }
            }],
            series: [{
                    type: 'bar',
                    data: [20, 60, 80, 60, 10, 66],
                    barWidth: '20px',
                    stack: "11",
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0.9,
                                    color: '#13d7f8' // 100% 处的颜色
                                },
                                {
                                    offset: 0,
                                    color: '#0a3770' // 0% 处的颜色
                                }
                            ], false),
                            shadowColor: 'rgba(0,160,221,1)',
                            shadowBlur: 4,
                        }
                    },
                    label: {

                        normal: {
                            show: true,
                            position: 'top',
                            color: "#fff"
                        }
                    }
                },

                {
                    type: 'bar',
                    data: [2, 2, 2, 2, 2, 2],
                    barWidth: '30px',
                    stack: "11",
                    itemStyle: {
                        normal: {
                            color: '#149aff',
                        }
                    },

                },

                {
                    type: 'bar',
                    data: [100, 100, 100, 100, 100, 100],
                    barGap: "-150%",
                    barWidth: '40px',
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,0.1)'
                        }
                    }

                },

            ]
        };
        const initChart = () => {
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: calc(100% - 50px);
    width: 100%;
}
</style>
