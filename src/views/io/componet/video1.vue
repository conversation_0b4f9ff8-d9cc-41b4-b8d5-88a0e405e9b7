<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: {
        yAxisData: {
            type: Array,
            default: () => {
                return []
            },
        },
        echartData: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })
        let value = 55.33;
        let title = '%';
        var dataArr = [];
        for (var i = 0; i < 100; i++) {
            if (i % 2 === 0) {
                dataArr.push({
                    name: (i + 1).toString(),
                    value: 25,
                    itemStyle: {
                        normal: {
                            color: "#2ac9e1",
                            borderWidth: 10,
                            borderColor: "rgba(0,0,0,0)"
                        }
                    }
                })
            } else {
                dataArr.push({
                    name: (i + 1).toString(),
                    value: 20,
                    itemStyle: {
                        normal: {
                            color: "rgba(0,0,0,0)",
                            borderWidth: 0,
                            borderColor: "rgba(0,0,0,0)"
                        }
                    }
                })
            }

        }
        let option = {
            series: [

                {
                    type: 'gauge',
                    radius: '80%',
                    clockwise: true,
                    startAngle: '90',
                    endAngle: '-269.9999',
                    splitNumber: 20,
                    pointer: {
                        show: false
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: [
                                [0, '#1452ff'],
                                [52 / 100, '#023b36'],
                                [1, '#03e7a1']
                            ],
                            width: 10,
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: true,
                        distance: -10,
                        length: 10,
                        lineStyle: {
                            color: 'black',
                            width: 1
                        }
                    },
                    axisLabel: {
                        show: false
                    }
                },
                {
                    type: 'pie',
                    name: '内层细圆环',
                    radius: ['50%', '52%'],
                    hoverAnimation: false,
                    clockWise: false,
                    itemStyle: {
                        normal: {
                            color: '#00e1e1'
                        }
                    },
                    label: {
                        show: false
                    },
                    data: dataArr
                },

                {
                    type: 'pie',
                    name: '内层细圆环',
                    radius: ['85%', '95%'],
                    hoverAnimation: false,
                    clockWise: false,
                    itemStyle: {
                        color: '#021f30',
                        shadowColor: "#021f30",
                        shadowBlur: 12.5
                    },
                    label: {
                        show: false
                    },
                    data: [11]
                },

            ]
        };
        const initChart = () => {
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 112px;
    width: 112px;
}
</style>
