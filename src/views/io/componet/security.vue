<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: {
        yAxisData: {
            type: Array,
            default: () => {
                return []
            },
        },
        echartData: {
            type: Array,
            default: () => {
                return []
            },
        },
    },
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })

        /**
         * 外接数据
         * 
         */
        var data = { //标准参数
            id: 'multipleThree',
            title: '',
            legend: ['报警主机', '防区'],
            barWidth: 30,
            yAxis: ['在线', '离线'],
            xAxis: [
                [-160, -80],
                [165, 170]
            ],
            color: ['#5e94dd', '#49b5bd'],
        }

        let option = {

            title: {
                text: data.title,
                left: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 500,
                    color: '#414957'
                },
                top: 12
            },

            legend: {
                top: 12,
                itemGap: 10,
                itemWidth: 10,
                itemHeight: 10,
                data: data.legend
            },
            color: data.color,
            grid: {
                x: 50,
                x2: 30,
                y2: 5,
                containLabel: true
            },
            xAxis: {
                show: false
            },
            yAxis: [{
                type: 'category',
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(108, 128, 151, 1)"
                    }

                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    show: true,
                    interval: '0',
                    textStyle: {
                        fontSize: 12,
                        color: '#687284',
                    },
                },
                data: data.yAxis
            }],
            series: [{
                    name: data.legend[0],
                    type: 'bar',
                    barWidth: data.barWidth || 12,
                    stack: '总量',
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                offset: 0,
                                color: 'rgba(255, 253, 199, 1) '
                            }, {
                                offset: 0.8,
                                color: 'rgba(208, 222, 238, 0)'
                            }], false)
                        }
                    },

                    label: {
                        normal: {
                            show: true,
                            position: 'left',
                            color: '#687284',
                            fontSize: '10',
                            formatter: function (params) {
                                return params.data * -1;
                            }
                        },

                    },
                    data: data.xAxis[0]
                },
                {
                    name: data.legend[1],
                    type: 'bar',
                    barWidth: data.barWidth || 12,
                    stack: '总量',
                    itemStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                offset: 0,
                                color: 'rgba(21, 154, 255, 0) '
                            }, {
                                offset: 0.8,
                                color: 'rgba(21, 199, 255, 1)'
                            }], false)
                        }
                    },

                    label: {
                        normal: {
                            show: true,
                            position: 'right',
                            color: '#687284',
                            fontSize: '10',
                        }
                    },
                    data: data.xAxis[1]
                },
            ]
        }

        const initChart = () => {
            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 150px;
    width: 100%;
}
</style>
