<template>
<div class="">
    <div class="left">
        <div class="left-card">
            <div class="card-header">
                <div class="title">在线设备</div>
            </div>
            <div class="list">
                <div>
                    <img src="./img/在线设备.png" />
                    <div class="scene h_center">
                        <div class="mode">在线设备</div>
                    </div>
                </div>
                <div>
                    <span class="t1">{{on}}</span><span class="t2">台</span>
                </div>
            </div>
            <div class="list">
                <div>
                    <img src="./img/离线设备.png" />
                    <div class="scene h_center">
                        <div class="mode">离线设备</div>
                    </div>
                </div>
                <div>
                    <span class="t3">{{off}}</span><span class="t2">台</span>
                </div>
            </div>

            <div class="center">
                <div class="total center">
                    在线率:{{(on*100/(on+off)).toFixed(2)}}%
                </div>
            </div>
        </div>

        <div class="left-card">
            <div class="card-header">
                <div class="title">采集器</div>
            </div>
            <div class="card-body">
                <div class="list" v-for="(item,i) in server" :key="i">
                    <div>
                        <img src="./img/Frame_1000001639.png" />
                        <div class="scene h_center">
                            <div class="mode">{{item.name}}</div>
                        </div>
                    </div>
                    <div class="center">
                        <div class="off">{{item.active?'在线':'离线'}}</div>
                        <div class="circle" :class="item.active=='true'?'green':'red'"></div>
                    </div>
                </div>
            </div>

            <!-- <div class="list">
            <div>
                <img src="./img/Frame_1000001639.png" />
                <div class="scene h_center">
                    <div class="mode">采集器</div>
                </div>
            </div>
            <div class="center">
                <div class="on">在线</div>
                <div class="circle green"></div>
            </div>
        </div> -->

        </div>

    </div>
    <div class="right">
        <div class="h100">
            <div class="card-header1">
                <div class="title">通讯事件</div>
            </div>
            <div class="right-card-body">
                <div class="event" v-for="item in list" :key="item.id">
                    <div>{{item.time}}</div>
                    <div>{{item.eventDesc}}</div>
                    <div>{{item.eventType}}</div>
                    <div class="event-text">
                        <i class="iconfont icondingwei4 locat"></i>
                    </div>
                </div>

            </div>
        </div>

    </div>
</div>
</template>

<script>
import {
    defineComponent,
    reactive,
    toRefs,
    getCurrentInstance
} from 'vue'
import dayjs from 'dayjs';
import {
    getCookie
} from '@/utils/cookie';
export default defineComponent({
    name: "io",
    setup() {
        const api = inject('$api')
        const state = reactive({

            list: [],
            server: [],
            on: 0,
            off: 0,

        })
        const getEvent = () => {
            api.getEvents({
                projectId: getCookie('gh_projectId'),
                bt: dayjs().startOf('day').format("YYYY-MM-DD HH:mm:ss"),
                et: dayjs().endOf('day').format("YYYY-MM-DD HH:mm:ss"),
            }).then(res => {
                state.list = res.data;

            })
        }
        const getActiveList = () => {
            const projectId = getCookie('gh_projectId');
            api.getActive(projectId)
                .then((res) => {
                    state.server = res.data.children
                    state.server.forEach(d => {
                        if (d.active == 'true') {
                            state.on += 1;
                        } else {
                            state.off += 1;
                        }
                    })
                })
                .catch((err) => {});
        };
        getActiveList()
        getEvent()

        return {
            ...toRefs(state),
        }
    }
});
</script>

<style lang="scss" scoped>
.home {
    .home_wrapper {
        .left {
            .left-card {
                height: 50%;
                .list {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 20px 0;

                    div:nth-child(1) {
                        display: flex;
                    }

                    .mode {
                        color: rgba(255, 255, 255, 0.64);
                        font-family: PingFang SC;
                        font-weight: bold;
                        font-size: 20px;
                        margin-left: 16px;
                    }

                    .t1 {
                        color: rgba(23, 205, 206, 1);
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        font-size: 36px;
                    }

                    .t2 {
                        color: rgba(255, 255, 255, 0.64);
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        font-size: 18px;
                    }

                    .t3 {
                        color: rgba(215, 79, 79, 1);
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        font-size: 36px;
                    }

                    .circle {
                        width: 12px;
                        height: 12px;
                        border-radius: 6px;
                    }

                    .red {
                        background: red;
                    }

                    .green {
                        background: green;
                    }

                    .off {
                        margin-right: 8px;
                        color: rgba(240, 217, 217, 1);
                        font-family: PingFang SC;
                        font-weight: bold;
                        font-size: 20px;
                    }

                    .on {
                        margin-right: 8px;
                        color: rgba(217, 240, 232, 1);
                        font-family: PingFang SC;
                        font-weight: bold;
                        font-size: 20px;
                    }

                }

            }

            .total {
                color: #42ffff;
                width: 190px;
                height: 190px;
                background: url('./img/Group_1000000894.png') no-repeat;
            }

        }

        .card-body {
            height: calc(100% - 50px);
            overflow-y: auto;
        }

        .right {
            align-items: center;
            .right-card-body {
                .device {
                    margin-top: 29px;
                }

                .event {
                    width: 100%;
                    height: 38px;
                    font-size: 14px;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: space-around;
                    background: url("./img/矩形.png");
                    margin: 5px 0;

                    .event-text {
                        color: red;
                        cursor: pointer;
                    }
                }
            }
        }

    }
}
</style>
