<template>
  <div class="home">
    <div class="home_wrapper">
      <LayoutHeader />

      <secondMenu />

      <div class="z100">
        <Transition name="fade" mode="out-in" appear>
          <component :is="activeMenus.component || 'home'">
          </component>
        </Transition>
        <div id="player">

        </div>
        <div class="bg"></div>
      </div>

      <GMenu />
      <Floor v-if="activeMenus.showFloor"></Floor>

    </div>

    <el-dialog align-center :append-to-body="true" class="pop" v-model="dialogVisible" :title="dialog.name"
      width="1500px" draggable>
      <component :is="dialog.component"></component>
    </el-dialog>

    <cctv ref="camRef"></cctv>
    <panel ref="panelRef"></panel>
  </div>
</template>

<script setup>

import secondMenu from '@/components/secondMenu.vue'
import {
  getCookie,
} from "@/utils/cookie";

import "@/lib/peer-stream";
import cctv from '@/components/cctv/src/main.vue'
import panel from '@/components/panel/src/main.vue'
import { useAppStore } from '@/stores/app';
import { useMitt } from '@/hooks/mitt';
import axios from 'axios';

const dialogVisible = ref(false)
const iframeUrl = ref('')
const ps = ref(null)
const camRef = ref(null)
const panelRef = ref(null)
const time = ref(0.6)
const showFloor = ref(true)
const weather = ref('晴')
const interval = ref(null)
const interval1 = ref(null)

const store = useAppStore();
const api = inject('$api')

const mitt=useMitt()
mitt.miitOn("changeNetWeather", (data) => {
  if (data) {
    if (interval.value) {
      clearInterval(interval.value)
    }
    interval.value = setInterval(() => {
      processWeather();
    }, 1000 * 60 * 30);
  } else {
    if (interval.value) {
      clearInterval(interval.value)
    }
  }

});

mitt.miitOn('ue', (data) => {
  if (data) {
    ps.value?.emitMessage(JSON.stringify(data))
  }
})

mitt.miitOn('msg', (data) => {
  if (data.type == 'cam') {
    camRef.value.opened(data);
  } else if (data.type == 'floor') {
    emitter.miitEmit('changeFloor', data);
  } else if (data.type == 'diagram') {
    panelRef.value.opened(data);
  }
})




const activeMenus = computed(() => {
  let menu = getCookie("funMenus");
  return store.funMenus ?
    store.funMenus : menu ? JSON.parse(menu) : {
      name: '首页',
      fullName: '首页',
      component: 'home',
      showFloor: false
    };
});

const dialog = computed(() => {
  return store.dialog
});

const areaId = computed(() => {
  return store.area;
});

const processWeather = () => {
  axios
    .get(`https://restapi.amap.com/v3/weather/weatherInfo?city=360400&key=504581ecfd67cb6d006010facdb7a3c8`)
    .then((result) => {
      let cmd = "clear_skies";
      if (weather.value != result.data.lives[0].weather) {
        let weather = result.data.lives[0].weather;
        if (weather == '晴' || weather == '平静' || weather == '微风' || weather == "和风" || weather == "清风" || weather == "有风" || weather == "热" || weather == "冷" || weather == "未知") {
          cmd = "clear_skies";
        } else if (weather == '晴间多云' || weather == '少云' || weather == '多云' || weather == '强风/劲风' || weather == '疾风' || weather == '大风' || weather == '烈风') {
          cmd = "cloudy";
        } else if (weather == '风暴' || weather == '狂暴风' || weather == '飓风' || weather == '热带风暴' || weather == '阵雨' || weather == "雷阵雨" || weather == "雷阵雨并伴有冰雹") {
          cmd = "rain_t";
        } else if (weather == "阴") {
          cmd = "overcast";
        } else if (weather == "小雨" || weather == "毛毛雨/细雨" || weather == "雨" || weather == "小雨-中雨" || weather == "中雨-大雨" || weather == "大雨-暴雨" || weather == "暴雨-大暴雨" || weather == "大暴雨-特大暴雨") {
          cmd = "rain_light";
        } else if (weather == "中雨" || weather == "大雨" || weather == "暴雨" || weather == "大暴雨" || weather == "特大暴雨" || weather == "雷阵雨" || weather == "强雷阵雨" || weather == "极端降雨") {
          cmd = "rain";
        } else if (weather == "雪" || weather == "小雪" || weather == "阵雪" || weather == "小雪-中雪" || weather == "雨雪天气" || weather == "雨夹雪" || weather == "阵雨加雪" || weather == "冻雨") {
          cmd = "snow";
        } else if (weather == "中雪" || weather == "大雪" || weather == "暴雪" || weather == "大雪-暴雪" || weather == "中雪-大雪") {
          cmd = "snow_b";
        }
        else if (weather == "霾" || weather == "中度霾" || weather == "重度霾" || weather == "严重霾" || weather == "浮尘" || weather == "扬沙" || weather == "强沙尘暴" || weather == "沙尘暴" || weather == "龙卷风" || weather == "雾" || weather == "浓雾" || weather == "强浓雾" || weather == "轻雾" || weather == "大雾" || weather == "特强浓雾") {
          cmd = "foggy";
        }
        ps.value.emitMessage(JSON.stringify({
          type: 'weather',
          value: cmd
        }))

      }
      weather.value = result.data.lives[0].weather;

    })
    .catch((error) => {
      console.log(error)
    })
}

watch(dialog, (val) => {
  if (val.component) {
    dialogVisible.value = true;
  } else {
    dialogVisible.value = false;
  }
})
onMounted(() => {


  ps.value = document.createElement("video", {
    is: "peer-stream"
  });
  ps.value.id = window.PROD_UE;
  document.getElementById("player").append(ps.value);


})

onBeforeUnmount(() => {

  if (interval.value) {
    clearInterval(interval.value)
  }
  if (interval1.value) {
    clearInterval(interval1.value)
  }
})




</script>

<style lang="scss" scoped>
.home {
  position: relative;
  // background: linear-gradient(270deg, rgba(0, 8, 18, 1) 0%, rgba(0, 14, 30, 0.9) 72.19%, rgba(0, 15, 32, 0.68) 83.65%, rgba(0, 12, 26, 0) 100%);
  width: 100%;
  height: 100%;

  .content-item {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 10px 0;
    border-bottom: 1px solid rgb(103, 146, 160);
    color: white;
    cursor: pointer;
  }

  .home_wrapper {
    position: absolute;
    width: 100%;
    height: 100%;

  }

  .pop {
    position: absolute;
    width: 100%;
    height: calc(100% - 64px);
    top: 64px;
    background: url("@/assets/images/home.jpg") no-repeat 100%/100% fixed;
    z-index: 101;
  }
}

.tuli {
  position: absolute;
  right: 100px;
  bottom: 100px;
  color: white;

  .list {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  svg {
    margin-right: 10px;
  }
}

.view {
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 8;
}

.diagram {

  width: 12rem;
  margin: 1rem auto 0.7rem auto;
  height: calc(100% - 1.7rem);

  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<style>
video {
  /* position: absolute; */
  width: 100%;
  height: 100%;

}

#player {
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 1;

}

.bg {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: url("@/assets/images/bg.png") no-repeat;
  background-size: 100% 100%;
  pointer-events: none;
}
</style>
