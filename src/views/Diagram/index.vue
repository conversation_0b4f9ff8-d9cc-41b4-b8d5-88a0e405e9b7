<template>
<div class="ba_container">
    <div class="diagram">
        <iframe :src="url" v-if="url"></iframe>
        <diagramNoData v-else />
    </div>

</div>
</template>

<script>

import {
    defineComponent,
    toRefs,
    onMounted,
    onUnmounted,
} from "vue";
export default defineComponent({
    name: "diagram",
    setup() {
        onUnmounted(() => {
         
        });
        onMounted(() => {
        });    
        return {
            
        };
    },
});
</script>

<style lang="scss" scoped>
.ba_container {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    background-image: url("@/assets/images/2D.jpg");
    background-color: linear-gradient(270deg,
            #060a0d 0%,
            rgba(5, 10, 14, 0) 51%,
            #050a0e 100%);
    background-repeat: no-repeat;
    background-size: cover;
    overflow: hidden;


}
</style>
