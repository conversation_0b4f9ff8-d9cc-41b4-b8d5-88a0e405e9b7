<template>
    <div class="echart" ref="myEcharts"></div>
</template>
    
<script>
import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick
} from 'vue'
export default {
    props: ['names', 'values'],
    setup(props) {
        const myEcharts = ref(null)
        const myChart = ref({})
        let echarts = inject('ec') //引入

        watch(props, (newVal, oldVal) => {
            if (newVal) initChart()
        })
        onMounted(() => {
            nextTick(() => {
                initChart()
            })

        })
        const initChart = () => {
            const myChart = echarts.init(myEcharts.value)
            var salvProName = ["安徽省", "河南省", "浙江省"];
            var salvProValue = [239, 181, 154];
            var salvProMax = [];//背景按最大值
            for (let i = 0; i < salvProValue.length; i++) {
                salvProMax.push(salvProValue[0])
            }
            let option = {

                grid: {
                    left: '2%',
                    right: '2%',
                    bottom: '2%',
                    top: '2%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    },
                    formatter: function (params) {
                        return params[0].name + ' : ' + params[0].value
                    }
                },
                xAxis: {
                    show: false,
                    type: 'value'
                },
                yAxis: [{
                    type: 'category',
                    inverse: true,
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#fff'
                        },
                    },
                    splitLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    data: props.names
                }, {
                    type: 'category',
                    inverse: true,
                    axisTick: 'none',
                    axisLine: 'none',
                    show: true,
                    axisLabel: {
                        textStyle: {
                            color: '#ffffff',
                            fontSize: '12'
                        },
                    },
                    data: props.values
                }],
                series: [{
                    name: '值',
                    type: 'bar',
                    zlevel: 1,
                    itemStyle: {
                        normal: {
                            barBorderRadius: 30,
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                offset: 0,
                                color: 'rgb(57,89,255,1)'
                            }, {
                                offset: 1,
                                color: 'rgb(46,200,207,1)'
                            }]),
                        },
                    },
                    barWidth: 20,
                    data: props.values
                }
                ]
            };
            myChart.setOption(option)
            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })

        }
        return {
            myEcharts,
            myChart,
            initChart
        }
    },
}
</script>
    
<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
    