<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick
} from 'vue'
export default {
    props: ['data1', 'data2', 'data3'],
    setup(props) {
        const myEcharts = ref(null)
        const myChart = ref({})
        let echarts = inject('ec') //引入

        watch(props, (newVal, oldVal) => {
            if (newVal) initChart()
        })
        onMounted(() => {
            nextTick(() => {
                initChart()
            })

        })
        const initChart = () => {
            const myChart = echarts.init(myEcharts.value)
            var data = {
                city: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                num: ["40", "60", "22", "85", "50", "40", "30"]
            }
            let option = {
                grid: {
                    top: '50px',
                    left: '15px',
                    right: '10px',
                    bottom: '0',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        lineStyle: {
                            color: '#57617B'
                        }
                    }
                },
                xAxis: [{
                    type: 'category',
                    boundaryGap: false,
                    axisLine: { //坐标轴轴线相关设置。数学上的x轴
                        show: true,
                        lineStyle: {
                            color: '#626F79'
                        },
                    },
                    axisLabel: { //坐标轴刻度标签的相关设置
                        textStyle: {
                            color: '#EAEBEC',
                            // margin: 15,
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    data: props.data1
                }],
                yAxis: [{
                    splitLine: {
                        show: false,
                        lineStyle: {
                            color: '#092b5d'
                        },
                    },
                    axisLine: {
                        show: false,
                        lineStyle: {
                            color: "#092b5d"
                        }

                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#C0C2C4',

                        },
                    },
                    axisTick: {
                        show: false,
                    },
                }],
                series: [{
                        name: 'CO₂浓度',
                        type: 'line',
                        markPoint: {
                            itemStyle: {
                                color: 'rgb(54,145,248)'
                            },
                            label: {
                                color: '#fff'
                            },
                            data: [{
                                    type: 'max',
                                    name: '最大值'
                                }, {
                                    type: 'min',
                                    name: '最小值'
                                },

                            ],
                            animationDelay: 3000,
                            animationDuration: 1000
                        },
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                color: "#318FF7", // 线条颜色
                            },
                            borderColor: '#318FF7',
                        },
                        itemStyle: {

                            color: "#318FF7",
                            borderColor: "#318FF7",
                            borderWidth: 0

                        },

                        areaStyle: { //区域填充样式
                            normal: {
                                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                        offset: 0,
                                        color: "rgba(124, 128, 244,.3)"

                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(124, 128, 244, 0)"
                                    }
                                ], false),
                                shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
                                shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
                            }
                        },
                        data: props.data2
                    },

                ]
            };
            myChart.setOption(option)
            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })

        }
        return {
            myEcharts,
            myChart,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
