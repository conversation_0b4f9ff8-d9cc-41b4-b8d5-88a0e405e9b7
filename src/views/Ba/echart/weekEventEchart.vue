<template>
    <div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick,
    
    reactive
} from 'vue'
import dayjs from 'dayjs'
export default {

    setup(props) {
        const api = inject('$api')
        const myEcharts = ref(null)
        let myChart = null;
        let echarts = inject('ec') //引入
        const state = reactive({
            temperature: 0,
            humidity: 0,
        })
        watch(props, (newVal, oldVal) => {
            if (newVal) initChart()
        })
        let xdata = [];
        let data = [];
        let data1 = [];
        onMounted(() => {
            initChart()
            setInterval(() => {
              // getHWReal ()
               // setOption();
            }, 2000);


        })
        const setOption = () => {
            if (xdata.length > 5) {
                xdata.shift();
                data.shift();
                data1.shift();
            }
            xdata.push(dayjs().format('HH:mm:ss'));
            data.push(state.temperature);
            data1.push(state.humidity);
            myChart.setOption({
                xAxis: {
                    data: xdata
                },
                series: [{
                    data: data
                }, {
                    data: data1
                }]
            })
        }
        const initChart = () => {
            myChart = echarts.init(myEcharts.value)
            let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        lineStyle: {
                            color: '#57617B'
                        }
                    }
                },

                grid: {
                    top: '10px',
                    left: '15px',
                    right: '0',
                    bottom: '10px',
                    containLabel: true
                },
                xAxis: [{
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        lineStyle: {
                            color: '#466582'
                        }
                    },
                    axisLabel: {

                        textStyle: {

                            color: 'white'
                        }
                    },
                    data: []
                }],
                yAxis: [{
                    type: 'value',
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#466582'
                        },

                    },
                    axisLabel: {
                        margin: 10,
                        textStyle: {
                            fontSize: 14,
                            color: 'white'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#466582'
                        },
                        show: false
                    }
                }],
                series: [
                    {
                        name: '温度',
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 5,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                width: 3
                            }
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(0,249,250,0.3)'
                                }, {
                                    offset: 0.8,
                                    color: 'rgba(0,249,250,0.05)'
                                }], false),
                                shadowColor: 'rgba(0, 0, 0, 0.1)',
                                shadowBlur: 10
                            },
                        },
                        itemStyle: {
                            normal: {

                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                    offset: 0,
                                    color: '#00F9FA'
                                }, {
                                    offset: 1,
                                    color: '#00F9FA'
                                }])
                            },
                            emphasis: {
                                color: 'rgb(99,250,235)',
                                borderColor: 'rgba(99,250,235,0.2)',
                                extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
                                borderWidth: 10
                            }
                        },
                        data: []
                    },
                    {
                        name: '湿度',
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 5,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                width: 3
                            }
                        },
                        areaStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: "rgba(254,186,52,0.3)"

                                },
                                {
                                    offset: 1,
                                    color: "rgba(254,186,52,0.1)"
                                }], false),
                                shadowColor: 'rgba(0, 0, 0, 0.1)',
                                shadowBlur: 10
                            },
                        },
                        itemStyle: {
                            normal: {

                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                    offset: 0,
                                    color: 'rgba(254,186,52,0.3)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(254,186,52,0.3)'
                                }])
                            },
                            emphasis: {
                                color: 'rgba(254,186,52,0.3)',
                                borderColor: 'rgba(99,250,235,0.2)',
                                extraCssText: 'box-shadow: 8px 8px 8px rgba(0, 0, 0, 1);',
                                borderWidth: 10
                            }
                        },
                        data: []
                    }

                ]
            };
            myChart.setOption(option)
            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })

        }
        const getHWReal = async () => {
            const res = await api.getHWReal({
                pageId: 45,
                serverCode: 'zgkon'
            });

            if (res && res.length > 0) {
                res.forEach(d => {
                    if (d.devicePropertyId == "DTL00005") {
                        state.temperature = d.currentValue
                    } else if (d.devicePropertyId == "DTL00006") {
                        state.humidity = d.currentValue
                    }
                })
            }

        }

        return {
            myEcharts,
            myChart,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
