<template>
    <div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    watch,
    nextTick
} from 'vue'
export default {
    props: {

        data: {
            type: Array,
            default: () => {
                return []
            },
        },

    },
    setup(props) {
        const myEcharts = ref(null)
        const myChart = ref({})
        let echarts = inject('ec') //引入

        watch(props, (newVal, oldVal) => {
            if (newVal) initChart()
        })
        onMounted(() => {
            nextTick(() => {
                initChart()
            })

        })
        const initChart = () => {
            const myChart = echarts.init(myEcharts.value)


            props.data.sort(function (a, b) {
                return b.rate - a.rate
            })
            let data = props.data.map(item => {
                return item.rate
            })
            let data1=props.data.map(item=>{
                return item.name
            })


            var option = {
                grid: {
                    left: '5%',
                    right: '5%',
                    bottom: '1%',
                    top: '2%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'none'
                    },
                    formatter: function (params) {
                        return params[0].name + '<br/>' +
                            "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>" +
                            params[0].seriesName + ' : ' + Number((params[0].value.toFixed(4) / 10000).toFixed(2)).toLocaleString() + ' 万元<br/>'
                    }
                },
                xAxis: {
                    show: false,
                    type: 'value'
                },
                yAxis: [{
                    type: 'category',
                    inverse: true,
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#fff'
                        },
                    },
                    splitLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    data:data1.slice(0, 3)
                }, {
                    type: 'category',
                    inverse: true,
                    axisTick: 'none',
                    axisLine: 'none',
                    show: true,
                    axisLabel: {
                        textStyle: {
                            color: '#ffffff',
                            fontSize: '12'
                        },
                        formatter: function (value) {
                            if (value >= 10000) {
                                return (value / 10000).toLocaleString() + '万';
                            } else {
                                return value.toLocaleString();
                            }
                        },
                    },
                    data: data.slice(0, 3)
                }],
                series: [{
                    name: '金额',
                    type: 'bar',
                    zlevel: 1,
                    itemStyle: {
                        normal: {
                            barBorderRadius: 30,
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                offset: 0,
                                color: 'rgb(57,89,255,1)'
                            }, {
                                offset: 1,
                                color: 'rgb(46,200,207,1)'
                            }]),
                        },
                    },
                    barWidth: 15,
                    data: data.slice(0, 3),
                },
                {
                    name: '背景',
                    type: 'bar',
                    barWidth: 15,
                    barGap: '-100%',
                    data: [100, 100, 100],
                    itemStyle: {
                        normal: {
                            color: 'rgba(24,31,68,1)',
                            barBorderRadius: 30,
                        }
                    },
                },
                ]
            };

            myChart.setOption(option)
            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })

        }
        return {
            myEcharts,
            myChart,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    width: 100%;
    height: 100%;
}
</style>
