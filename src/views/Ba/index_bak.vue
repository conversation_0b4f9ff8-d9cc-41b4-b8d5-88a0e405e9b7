<template>
    <div class="z100">
        <div class="left">
            <div class="header flex-start">
                <img src="@/assets/images/common/head.png">
                <div> 设备列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="searchDevice" prefix-icon="Search" placeholder="按设备名称搜索">
                </el-input>
            </div>
            <div class="device">
                <el-scrollbar v-if="list.length > 0">
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor" @click="showDetail(item)">
                            <span class="iconfont " v-if="item.icon" :class="item.icon"></span>
                            <img v-else src="@/assets/images/common/feng.png" />
                            <div class="name">{{ item.name }}</div>
                        </div>
                        <div class="center state" style="margin-right: 15px;">
                            <div :style="{ color: getColor(realData[p.id], p.config) }" v-for="(p, j) in item.state"
                                :key="j">
                                {{ getName(realData[p.id], p.config) }}</div>
                        </div>
                        <div class="position cursor" @click="zoomToPosition(item)">
                            <img src="@/assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else></noData>
            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>

        <diagram v-model:show="showDevice" :name="deviceDetail.name">
            <div class="detail">
                <div class="tabs">
                    <div v-for="item in tabs" :class="item.component == tabComponent ? 'active' : ''"
                        class="center tab cursor" :key="item.component" @click="changeTab(item)">
                        <span class="name">{{ item.name }}</span>
                    </div>
                </div>

                <component :is="tabComponent" :deviceId="deviceDetail.id"></component>
            </div>
        </diagram>

        <!-- 中间查询记录 -->
        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in">
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

        <!-- 右侧列表 -->
        <Transition name="fade" mode="out-in" appear>
            <component :is="activeMenus.secondComponent"></component>
        </Transition>

    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    onMounted,
    inject,
    watch,
    nextTick,
    onUnmounted
} from 'vue';
import device from '@/components/device/device.vue'
import alarm from '@/components/device/alarm.vue'
import history from '@/components/device/history.vue'
import real from '@/components/device/real.vue'
import health from '@/components/device/health.vue'
import repair from '@/components/device/repair.vue'
import run from '@/components/device/run.vue'
import work from '@/components/device/work.vue'
import cmd from '@/components/device/cmd.vue'
import {
    getCookie
} from "@/utils/cookie";

import calc from '@/utils/eval';
import socket from "@/utils/socket";
import diagram from '@/components/diagram/index.vue'
import pop from '@/components/pop/index.vue'
import {
    Search
} from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app';


export default defineComponent({
    name: "common1",
    components: {

        diagram,
        device,
        health,
        run,
        work,
        alarm,
        repair,
        real,
        history,
        cmd,
        pop,
        Search

    },
    sockets: {
        live(res) {
            this.subscribeData(res);
        },
        onVarsChangedCallback(res) {
            this.subscribeData(res);
        },
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 20,
            page: 1,
            total: 0,
            sockets: null,
            showDevice: false,
            list: [], //设备列表
            realData: {}, //订阅返回的实时数据
            tabComponent: "device",
            tabs: [
                {
                    name: '设备信息',
                    component: 'device'
                },
                {
                    name: '实时数据',
                    component: 'real'
                },
                {
                    name: '历史工况',
                    component: 'history'
                },
                {
                    name: '运行记录',
                    component: 'run'
                },
                {
                    name: '报警记录',
                    component: 'alarm'
                },
                {
                    name: '维保记录',
                    component: 'work'
                },
                {
                    name: '维修记录',
                    component: 'repair'
                },
                {
                    name: '健康度',
                    component: 'health'
                },
                {
                    name: '设备面板',
                    component: 'cmd'
                },
            ],
            deviceDetail: {
                name: null,
                id: null,
            },
            popName: 'AcsRecord',
            pushData: {},//推送UE数据
            unit: []
        })
        state.sockets = inject("socket");
        onMounted(() => {
            getDeviceList();
            getUnit();
        });
        onUnmounted(() => {
            socket.unsubscribe(state.sockets, "ba", "real");
        });
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        //当前激活的楼层
        const areaId = computed(() => {
            return store.area;
        });
        const emitter = inject('mitt');

        emitter.off('pushData');
        emitter.on('pushData', async (data) => {
            await getDeviceList()
            if (data.type == "env") {
                // Object.keys(state.pushData).forEach((d) => {
                //     emitter.emit("ue", {
                //         type: "data",
                //         token: state.pushData[d].code,
                //         value: state.pushData[d].value
                //     });
                // });

            }
        });


        watch(areaId, (val) => {
            getDeviceList();
        });

        watch(activeMenus, (val) => {

            if (!val.popName) { //中间记录不需要加载设备
                getDeviceList();
            }

        });
        //解析设备指标
        const deviceStd = (data) => {
            let ws = [];
            state.pushData = {};
            state.realData = {};
            state.list = [];
            data.forEach((d, i) => {
                if (d.deviceStandards) {
                    let data = {};
                    data.name = d.name;
                    data.icon = d.icon;
                    data.id = d.id;
                    data.code = d.code;
                    data.state = [];
                    //设备指标          
                    d.deviceStandards.forEach((s, j) => {
                        let id = d.productId + "_" + d.id + '_' + s.identifier;
                        let variable = d.productId + ":" + d.id + ":" + s.identifier;
                        let subItem = {
                            productId: d.productId,
                            deviceId: d.id,
                            identifier: s.identifier,
                        };
                        if (s.identifier == 'comStatus') {
                            state.pushData = Object.assign({}, state.pushData, {
                                [id]: {
                                    code: d.code,
                                    name: s.name,
                                    value: 0,
                                    unit: ""
                                },
                            });
                        }


                        //状态输入  
                        if (s.showList  && s.dataTypeName == 'bool') {
                            data.state.push({
                                name: s.name,
                                id: id,
                                config: s.config ? JSON.parse(s.config) : null
                            });
                            let item = subItem;
                            state.realData = Object.assign({}, state.realData, {
                                [id]: 0,
                            });
                            ws.push(item);
                        }
                        else if (s.typeId == 2 && s.dataTypeName == "bool") {


                            ws.push(subItem);

                        } else if (s.typeId == 1 && (s.dataTypeName == 'string' || s.dataTypeName == 'int'
                            || s.dataTypeName == 'float' || s.dataTypeName == 'double')) {
                            subItem.unit = getUnitName(s.unitId);
                            ws.push(subItem);

                        } else if (s.typeId == 2 && (s.dataTypeName == 'string' || s.dataTypeName == 'int'
                            || s.dataTypeName == 'float' || s.dataTypeName == 'double')) {

                            ws.push(subItem);

                        } else if (s.dataTypeName == 'enmu') {
                            if (p.paramValue) {


                                ws.push(subItem);
                            }
                        }

                    });
                    state.list.push(data);
                }
            });
            if (ws.length > 0) {
                nextTick(() => {
                    socket.subscribe(state.sockets, "ba", null, ws);
                });
            }
        };

        const getDeviceList = async () => {
            socket.unsubscribe(state.sockets, "ba");
            let id = null;
            if (areaId.value && areaId.value instanceof Array) {
                id = areaId.value;
            } else if (areaId.value && areaId.value.id == -1) {
                id = null;
            } else if (areaId.value && areaId.value.id != -1) {
                id = areaId.value.id;
            }
            let {
                data,
                total
            } = await api.getDevicesStd({
                areaId: id,
                menuId: activeMenus.value ? activeMenus.value.id : "",
                keyword: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = total;
            deviceStd(data);
            Object.keys(state.pushData).forEach((d) => {
                emitter.emit("ue", {
                    type: "data",
                    token: state.pushData[d].code,
                    value: state.pushData[d].value
                });
            });
        };

        const handleCurrentChange = (page) => {
            state.page = page;
            getDeviceList();
        }

        const searchDevice = () => {
            state.page = 1;
            getDeviceList();
        }

        const getUnit = () => {
            api.getDicUtil({
                dicCode: 'unit',
                projectId: getCookie("gh_projectId"),
            }).then((res) => {
                state.unit = res.data
            })
        }

        const getUnitName = (val) => {
            let name = ''
            state.unit.forEach((u) => {
                if (u.id == val) {
                    name = u.tagName
                }
            })
            return name
        }

        const getName = (value, config) => {
            let name = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        name = c.text;
                    }
                })
            }
            return name;
        }
        const getColor = (value, config) => {
            let color = "";
            if (config && config.length && config.length > 0) {
                config.forEach(c => {
                    if (calc(value, c.factor, c.value)) {
                        color = c.color;
                    }
                })
            }
            return color;
        }
        const subscribeData = (res) => {
            if (res) {
                let data = JSON.parse(res);
                if (data.batchDefinitionId == "ba") {
                    data.data.forEach((d) => {
                        if (!Object.keys(d).includes("value")) {
                            d.value = 0;
                        }
                        state.realData[d.id] = Number(d.value);

                        if (state.pushData[d.id]) {
                            emitter.emit("ue", {
                                type: "data",
                                token: state.pushData[d.id].code,
                                value: Number(d.value)
                            });
                            state.pushData[d.id].value = Number(d.value);
                        }
                    });
                }
            }
        };

        const showDetail = (item) => {
            state.showDevice = true;
            state.deviceDetail = item;
        }

        const changeTab = (item) => {
            state.tabComponent = item.component
        }
        const zoomToPosition = (item) => {

            if (item.code) {
                emitter.emit('ue', {
                    type: 'position',
                    token: item.code,
                })
            }
        };

        return {
            ...toRefs(state),
            activeMenus,
            handleCurrentChange,
            searchDevice,
            getName,
            getColor,
            subscribeData,
            showDetail,
            changeTab,
            zoomToPosition,
        }
    }
});
</script>

<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("@/assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;
            background-size: 100% 100% !important;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("@/assets/images/common/tab_active.png") no-repeat !important;
        width: 104px;
        height: 40px;
        background-size: 100% 100% !important;
    }

}
</style>
