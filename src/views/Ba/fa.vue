<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='设备统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{ yan.all+wen.all+shou.all+xiao.all+sg.all }}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">烟感正常/故障/火警:</span><span
                            class="num">{{ yan.normal }}/{{yan.fault}}/{{yan.fire  }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">温感正常/故障/火警:</span><span
                            class="num">{{ wen.normal }}/ {{ wen.fault }} /{{ wen.fire }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">手报正常/故障/火警:</span><span
                            class="num">{{ shou.normal }}/{{shou.fault}}/{{shou.fire  }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">消报正常/故障/火警:</span><span
                            class="num">{{ xiao.normal }}/{{xiao.fault}}/{{xiao.fire  }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">声光正常/故障/火警:</span><span
                            class="num">{{ sg.normal }}/{{sg.fault}}/{{ sg.fire }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='报警分析' />
            <div class="item-body kong">
                <Alarm :xAxisData="xAxisData" :echartData="echartData" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='报警列表' />
            <div class="item-body event">
                <el-scrollbar v-if="alarms.length > 0">
                    <div class="list space-between" v-for="(item, i) in alarms" :key="i">
                        <div class="name">
                            <div>
                                <span style="color:red" class="iconfont iconbaojing11"></span>
                            </div>
                            <div>{{ item.deviceName ? item.deviceName : '未知' }}</div>
                        </div>

                        <div class="time">
                            {{ item.createTime }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
                <noData v-else></noData>

            </div>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    onMounted,

} from 'vue';

import {
    getCookie
} from "@/utils/cookie";


import pop from '@/components/pop/index.vue'
import Alarm from '@/components/echarts/weekEventEchart.vue'
export default defineComponent({
    name: "fa",
    components: {
        pop,
        Alarm
    },

    setup() {
        const api = inject('$api')
        const state = reactive({
            echartData: [10, 11, 12, 5, 8, 6, 2],
            xAxisData: ['周一', '周二', '周三', '周四', '周五', '周六', '周天'],
            alarms: [],
            yan:{
                all:0,
                normal:0,
                fault:0,
                fire:0,
            },
            wen:{
                all:0,
                normal:0,
                fault:0,
                fire:0,
            },
            xiao:{
                all:0,
                normal:0,
                fault:0,
                fire:0,
            },
            sg:{
                all:0,
                normal:0,
                fault:0,
                fire:0,
            },
            shou:{
                all:0,
                normal:0,
                fault:0,
                fire:0,
            },
            
            
        })

        onMounted(() => {
            getAlarmWeek()
            getHistroyRecordPage();
            getDeviceTypeStatus();

        });

        const getHistroyRecordPage = () => {
            api.getHistroyRecord({
                projectId: getCookie("gh_projectId"),
                page: 1,
                size: 10,
                alarmSource: 4

            }).then((res) => {
                state.alarms = res.data;
            })
        }
        const getAlarmWeek = () => {
            api.getAlarmWeek({
                projectId: getCookie("gh_projectId"),
                alarmSource: 4

            }).then((res) => {
                state.echartData = res.data.map(el => el.count);
            })
        }
        const getDeviceTypeStatus = async () => {
            state.all = 0;
            state.fault = 0;
            state.normal = 0;
            state.fire = 0;

            const { data } = await api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: ['yangan','wengan','sg','shoubao','xiaobao']
            });
            data.forEach(d => {
                if (d.code=='yangan'&& d.keyCode == "fire") {
                    state.yan.all++
                    if (d.dataVal == '0') {
                        state.yan.normal++;
                    } else if (d.dataVal == '1') {
                        state.yan.fire++;
                    }else if (d.dataVal == '2') {
                        state.yan.fault++;
                    }
                }
                if (d.code=='wengan'&& d.keyCode == "fire") {
                    state.wen.all++
                    if (d.dataVal == '0') {
                        state.wen.normal++;
                    } else if (d.dataVal == '1') {
                        state.wen.fire++;
                    }else if (d.dataVal == '2') {
                        state.wen.fault++;
                    }
                }
                if (d.code=='sg'&& d.keyCode == "fire") {
                    state.sg.all++
                    if (d.dataVal == '0') {
                        state.sg.normal++;
                    } else if (d.dataVal == '1') {
                        state.sg.fire++;
                    }else if (d.dataVal == '2') {
                        state.sg.fault++;
                    }
                }
                if (d.code=='shoubao'&& d.keyCode == "fire") {
                    state.shou.all++
                    if (d.dataVal == '0') {
                        state.shou.normal++;
                    } else if (d.dataVal == '1') {
                        state.shou.fire++;
                    }else if (d.dataVal == '2') {
                        state.shou.fault++;
                    }
                }
                if (d.code=='xiaobao'&& d.keyCode == "fire") {
                    state.xiao.all++
                    if (d.dataVal == '0') {
                        state.xiao.normal++;
                    } else if (d.dataVal == '1') {
                        state.xiao.fire++;
                    }else if (d.dataVal == '2') {
                        state.xiao.fault++;
                    }
                }
            })

           
        }



        return {
            ...toRefs(state),
        }
    }
});
</script>

<style lang="scss" scoped>
.right {
    .event {
        .time {
            width: 130px
        }
    }
    .order-text{
        .text{
            font-size: 14px;
            white-space: nowrap;
        }
        .num{
            font-size: 14px;
            white-space: nowrap;
        }
    }
}
</style>
