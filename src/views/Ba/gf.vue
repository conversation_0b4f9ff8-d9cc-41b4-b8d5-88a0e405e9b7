<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='月发电量(kWh)' />
            <div class="item-body order">
                <div>
                    <div class="total center">总发电量</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{ (dhg + gtk + djg).toFixed(1) }}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">碲化镉:</span><span
                            class="num">{{ dhg}}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">钙钛矿:</span><span
                            class="num">{{ gtk
                            }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">单晶硅:</span><span
                            class="num">{{ djg }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='设备群控' />
            <div class="energy-stats-panel">
                    <!-- 用电量统计 -->
                    <div class="energy-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="iconfont icondianchi"></i>
                            </div>
                            <div class="section-title">用电量统计(kwh)</div>
                        </div>

                        <div class="energy-cards">
                            <div class="energy-card electricity daily">
                                <div class="circle-progress">
                                    <svg viewBox="0 0 36 36" class="circular-chart">
                                        <circle class="circle-bg" cx="18" cy="18" r="15.9155"></circle>
                                        <circle class="circle-progress-path" cx="18" cy="18" r="15.9155"
                                            stroke-dashoffset="1">
                                        </circle>
                                    </svg>
                                    <div class="progress-value">{{ Number(electricityData.currentDayValue).toFixed(1) }}</div>
                                </div>
                                <div class="card-title">今日用电</div>
                            </div>

                            <div class="energy-card electricity monthly">
                                <div class="circle-progress">
                                    <svg viewBox="0 0 36 36" class="circular-chart">
                                        <circle class="circle-bg" cx="18" cy="18" r="15.9155"></circle>
                                        <circle class="circle-progress-path" cx="18" cy="18" r="15.9155"
                                            stroke-dashoffset="1">
                                        </circle>
                                    </svg>
                                    <div class="progress-value">{{ Number(electricityData.currentMonthValue).toFixed(1) }}</div>
                                </div>
                                <div class="card-title">本月用电</div>

                            </div>

                            <div class="energy-card electricity yearly">
                                <div class="circle-progress">
                                    <svg viewBox="0 0 36 36" class="circular-chart">
                                        <circle class="circle-bg" cx="18" cy="18" r="15.9155"></circle>
                                        <circle class="circle-progress-path" cx="18" cy="18" r="15.9155"
                                            stroke-dashoffset="1">
                                        </circle>
                                    </svg>
                                    <div class="progress-value">{{ Number(electricityData.currentYearValue).toFixed(1) }}</div>
                                </div>
                                <div class="card-title">今年用电</div>

                            </div>
                        </div>
                    </div>

                    <!-- 光伏发电量统计 -->
                    <div class="energy-section">
                        <div class="section-header">
                            <div class="section-icon solar">
                                <i class="iconfont icontaiyangneng"></i>
                            </div>
                            <div class="section-title">光伏发电统计(kwh)</div>

                        </div>

                        <div class="energy-cards">
                            <div class="energy-card solar daily">
                                <div class="circle-progress">
                                    <svg viewBox="0 0 36 36" class="circular-chart">
                                        <circle class="circle-bg" cx="18" cy="18" r="15.9155"></circle>
                                        <circle class="circle-progress-path" cx="18" cy="18" r="15.9155"
                                            stroke-dashoffset="1">
                                        </circle>
                                    </svg>
                                    <div class="progress-value">{{ rifdl.toFixed(1)}}</div>
                                </div>
                                <div class="card-title">今日发电</div>

                            </div>

                            <div class="energy-card solar monthly">
                                <div class="circle-progress">
                                    <svg viewBox="0 0 36 36" class="circular-chart">
                                        <circle class="circle-bg" cx="18" cy="18" r="15.9155"></circle>
                                        <circle class="circle-progress-path" cx="18" cy="18" r="15.9155"
                                            stroke-dashoffset="1">
                                        </circle>
                                    </svg>
                                    <div class="progress-value">{{ yfdl.toFixed(1) }}</div>
                                </div>
                                <div class="card-title">本月发电</div>

                            </div>

                            <div class="energy-card solar yearly">
                                <div class="circle-progress">
                                    <svg viewBox="0 0 36 36" class="circular-chart">
                                        <circle class="circle-bg" cx="18" cy="18" r="15.9155"></circle>
                                        <circle class="circle-progress-path" cx="18" cy="18" r="15.9155"
                                            stroke-dashoffset="1">
                                        </circle>
                                    </svg>
                                    <div class="progress-value">{{ nfdl.toFixed(1) }}</div>
                                </div>
                                <div class="card-title">今年发电</div>

                            </div>
                        </div>
                    </div>

            
                </div>
        </div>
   
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    inject,
    watch
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";
import socket from "@/utils/socket";

import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "gf",
    components: {},
     setup() {
        const api = inject('$api')
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            manual: [],
            sockets: null,
            manualLog: [],
            on: 0,
            off: 0,
            fault: 0,
            all: 0,
            dhg: 0,
            gtk: 0,
            djg: 0,
            electricityData: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0,
            },
            rifdl:0,
            yfdl:0,
            nfdl:0
        })
        const store = useAppStore()
        state.sockets = inject("socket");
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        watch(activeMenus, (val) => {
            getDeviceTypeStatus();
            getRunConfigPage("manual");
            getRunManualPage(3, "manualLog")
        });

        const getRunConfigPage = (type) => {
            state.manual = [];
            api.getRunConfig({
                projectId: getCookie("gh_projectId"),
                tag: 2,
                type,
                menuId: activeMenus.value.id
            }).then((res) => {
                if (type == "strategy") {
                    // state.strategies = res.data.strategies;
                    // getRunModelPage(2);
                }
                if (type == "manual") {
                    state.manual = res.data.manuals;

                }
            });
        };
        const writeValue = (group, status) => {
            ElMessageBox.confirm('是否确认该操作？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                if (group && group.length > 0) {
                    group.forEach((g, i) => {
                        g.groupVars.forEach((v, j) => {
                            socket.writeValue(
                                state.sockets,
                                v.variable,
                                v.varValue,
                                "manual",
                                state.sockets.id,
                                getCookie("gh_projectId"),
                                getCookie("gh_id")
                            );
                        });
                    });
                    ElMessage.success('操作成功')
                }
            })

        };

        const getRunManualPage = (type, log) => {
            api.getDeviceLog({
                projectId: getCookie("gh_projectId"),
                menuId: activeMenus.value.id,
                page: 1,
                size: 10,
            }).then((res) => {
                state[log] = res.data;
            });
        };

        const getDeviceTypeStatus = () => {
            let typeCode = [];
            if (activeMenus.value.name.includes('空调')) {
                typeCode = ['FCU']
            } else if (activeMenus.value.name.includes('新风')) {
                typeCode = ['PAU']
            }
            let on = 0,
                off = 0,
                fault = 0,
                all = 0;
            let areaId = null;
            api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: typeCode,
                areaId: areaId
            }).then(res => {
                res.data.forEach(d => {
                    //空调系统和全热新风
                    if (d.identifier == "runStatus" || d.identifier == "sts") {
                        all++;
                        if (d.value == '1') {
                            on++;

                        } else if (d.value == '0') {
                            off++;
                        }
                    }

                })
                state.on = on;
                state.off = off;
                state.all = all;
                state.fault = fault;
            });
        }


        getRunConfigPage("manual")
        getRunManualPage(3, "manualLog");
        // getDeviceTypeStatus();

        //获取钙钛矿发电量
        let data =  api.getDevicesStdById({
            deviceId: 895,
            projectId: getCookie("gh_projectId")
        }).then(res => {
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "min") {
                    state.gtk = parseFloat(d.value)
                }
            })
        })


      
        api.getDevicesStdById({
            deviceId: 894,
            projectId: getCookie("gh_projectId")
        }).then(res => {
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "min") {
                    state.dhg = parseFloat(d.value)
                }
            })
        })


        //获取单晶硅发电量
        api.getDevicesStdById({
            deviceId: 933,
            projectId: getCookie("gh_projectId")
        }).then(res => {
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "min") {
                    state.djg = parseFloat(d.value)
                }
            })
        })

        const getSolar = async (id) => {
          let res= await  api.getDevicesStdById({
                deviceId: 894,
                projectId: getCookie("gh_projectId")
            })
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "din") {
                    state.rifdl += parseFloat(d.value)
                }
                if (d.identifier == "min") {
                    state.yfdl += parseFloat(d.value)
                }
                if (d.identifier == "yin") {
                    state.nfdl += parseFloat(d.value)
                }
            })

            res= await  api.getDevicesStdById({
                deviceId: 895,
                projectId: getCookie("gh_projectId")
            })
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "din") {
                    state.rifdl += parseFloat(d.value)
                }
                if (d.identifier == "min") {
                    state.yfdl += parseFloat(d.value)
                }
                if (d.identifier == "yin") {
                    state.nfdl += parseFloat(d.value)
                }
            })


            res= await  api.getDevicesStdById({
                deviceId: 933,
                projectId: getCookie("gh_projectId")
            })
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "din") {
                    state.rifdl += parseFloat(d.value)
                }
                if (d.identifier == "min") {
                    state.yfdl += parseFloat(d.value)
                }
                if (d.identifier == "yin") {
                    state.nfdl += parseFloat(d.value)
                }
            })




        }

        const getEnergyOver = (type, data) => {
            api.getEnergyOverView({
                type: type
            }).then((res) => {
                res.data.forEach((element) => {
                    Object.keys(element).forEach((key) => {
                        if (key !== 'time') {
                            data[key] = element[key]
                        }
                    })
                })
            })
        }

        getEnergyOver(1, state.electricityData);

        getSolar();



        return {
            ...toRefs(state),
            writeValue,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>
.event {
    .name {
        width: auto !important;

        &>div {
            width: 95px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .time {
        width: 130px;
    }
}

.energy-stats-panel {
        width: 100%;
        margin-bottom: 8px;
        background: rgba(16, 52, 87, 0.1);
        border-radius: 10px;
        padding: 10px;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(78, 160, 255, 0.15);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at top right, rgba(46, 213, 115, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .energy-section {
            position: relative;
            z-index: 1;
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 10px;
            }

            .section-header {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                position: relative;

                .section-icon {
                    width: 28px;
                    height: 28px;
                    border-radius: 8px;
                    background: linear-gradient(135deg, #55efc4 0%, #00cec9 100%);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10px;
                    box-shadow: 0 2px 4px rgba(85, 239, 196, 0.3);

                    i {
                        font-size: 16px;
                        color: #ffffff;
                    }

                    &.solar {
                        background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
                        box-shadow: 0 2px 4px rgba(255, 234, 167, 0.3);
                    }
                }

                .section-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #E6F4FF;
                    margin-right: 6px;
                    text-shadow: 0 0 5px rgba(230, 244, 255, 0.3);
                }

                .section-unit {
                    font-size: 12px;
                    font-weight: normal;
                    color: rgba(230, 244, 255, 0.7);
                }
            }

            .energy-cards {
                display: flex;
                gap: 10px;
                margin-bottom: 0;

                .energy-card {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    background: rgba(16, 52, 87, 0.2);
                    border-radius: 8px;
                    padding: 10px 8px;
                    position: relative;
                    overflow: hidden;
                    backdrop-filter: blur(4px);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                    border: 1px solid rgba(46, 213, 115, 0.15);
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
                    }

                    &:before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 3px;
                        background: linear-gradient(to right, #10845c, #2ed573);
                    }

                    &.solar:before {
                        background: linear-gradient(to right, #00a8b5, #3bc8c4);
                    }

                    .circle-progress {
                        position: relative;
                        width: 70px;
                        height: 70px;
                        margin-bottom: 8px;

                        .circular-chart {
                            width: 100%;
                            height: 100%;
                            transform: rotate(-90deg);

                            .circle-bg {
                                fill: none;
                                stroke: rgba(16, 52, 87, 0.2);
                                stroke-width: 2;
                            }

                            .circle-progress-path {
                                fill: none;
                                stroke-width: 2.5;
                                stroke-linecap: round;
                                animation: progress-animation 1.5s ease-out forwards;
                                transition: stroke-dashoffset 1s ease;
                            }
                        }

                        .progress-value {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            font-size: 14px;
                            font-weight: bold;
                            color: #FFFFFF;
                            text-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
                            white-space: nowrap;
                        }
                    }

                    &.electricity {
                        &.daily {
                            .circle-progress-path {
                                stroke: #55efc4;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(85, 239, 196, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #55efc4, #00cec9);
                            }
                        }

                        &.monthly {
                            .circle-progress-path {
                                stroke: #74b9ff;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(116, 185, 255, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #74b9ff, #0984e3);
                            }
                        }

                        &.yearly {
                            .circle-progress-path {
                                stroke: #a29bfe;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(162, 155, 254, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #a29bfe, #6c5ce7);
                            }
                        }
                    }

                    &.solar {
                        &.daily {
                            .circle-progress-path {
                                stroke: #ffeaa7;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(255, 234, 167, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #ffeaa7, #fdcb6e);
                            }
                        }

                        &.monthly {
                            .circle-progress-path {
                                stroke: #fab1a0;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(250, 177, 160, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #fab1a0, #e17055);
                            }
                        }

                        &.yearly {
                            .circle-progress-path {
                                stroke: #ff7675;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(255, 118, 117, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #ff7675, #d63031);
                            }
                        }
                    }

                    .card-title {
                        font-size: 12px;
                        color: rgba(230, 244, 255, 0.7);
                        margin-bottom: 2px;
                        text-align: center;
                    }

                    .card-value {
                        font-size: 12px;
                        font-family: "BEBAS", sans-serif;
                        color: rgba(230, 244, 255, 0.6);
                        text-align: center;
                    }
                }
            }
        }

        .energy-comparison {
            position: relative;
            z-index: 1;
            background: rgba(16, 52, 87, 0.2);
            border-radius: 8px;
            padding: 10px;
            backdrop-filter: blur(4px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(46, 213, 115, 0.15);

            .comparison-title {
                font-size: 14px;
                font-weight: 500;
                color: #E6F4FF;
                margin-bottom: 10px;
                text-align: center;
            }

            .comparison-chart {
                .comparison-wrapper {
                    height: 24px;
                    background: rgba(16, 52, 87, 0.3);
                    border-radius: 12px;
                    display: flex;
                    overflow: hidden;
                    margin-bottom: 8px;

                    .electricity-bar {
                        height: 100%;
                        background: linear-gradient(to right, #55efc4, #00cec9);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: width 1s cubic-bezier(0.19, 1, 0.22, 1);

                        .comparison-label {
                            font-size: 12px;
                            font-weight: bold;
                            color: #ffffff;
                        }
                    }

                    .solar-bar {
                        height: 100%;
                        background: linear-gradient(to right, #ffeaa7, #fdcb6e);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: width 1s cubic-bezier(0.19, 1, 0.22, 1);

                        .comparison-label {
                            font-size: 12px;
                            font-weight: bold;
                            color: #ffffff;
                        }
                    }
                }

                .comparison-legend {
                    display: flex;
                    justify-content: center;
                    margin-top: 6px;

                    .legend-item {
                        display: flex;
                        align-items: center;
                        margin: 0 8px;

                        .legend-color {
                            width: 12px;
                            height: 12px;
                            border-radius: 3px;
                            margin-right: 5px;

                            &.electricity {
                                background: linear-gradient(to right, #55efc4, #00cec9);
                            }

                            &.solar {
                                background: linear-gradient(to right, #ffeaa7, #fdcb6e);
                            }
                        }

                        .legend-text {
                            font-size: 11px;
                            color: rgba(230, 244, 255, 0.7);
                        }
                    }
                }
            }
        }
    }

    @keyframes progress-animation {
        0% {
            stroke-dashoffset: 100;
        }
    }
</style>
