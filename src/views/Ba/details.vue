<template>
  <div class="device_con layout_wrapper">
    <div class="tabs">
      <div v-for="(item, index) in tab" :key="index" class="item" @click="handelTabs(item, index)"
        :class="tabIndex == index ? 'tabActive' : ''">
        {{ item.label }}
      </div>
    </div>
    <i class="el-icon-close closeBtn" @click="back"></i>
    <el-scrollbar class="scrollbar">
      <component :is="cur" :deviceId="deviceId"></component>
    </el-scrollbar>
  </div>
</template>

<script>
import device from '@/components/device/device.vue'
import real from '@/components/device/real.vue'
import history from '@/components/device/history.vue'
import command from '@/components/device/command.vue'
import run from '@/components/device/run.vue'
import alarm from '@/components/device/alarm.vue'
import work from '@/components/device/work.vue'
import repair from '@/components/device/repair.vue'
import health from '@/components/device/health.vue'
import analysis from '@/components/device/analysis.vue'
import {
  onMounted,
  reactive,
  toRefs
} from 'vue'
import {
  useRouter
} from 'vue-router'
export default {
  name:'detail',
  components: {
    device,
    real,
    history,
    command,
    run,
    alarm,
    work,
    repair,
    health,
    analysis
  },
  setup () {
    const router = useRouter()
    const state = reactive({
      tab: [{
        label: '设备信息',
        value: 'device',
      },
      {
        label: '实时工况',
        value: 'real',
      },
      {
        label: '历史工况',
        value: 'history',
      },
      // {
      //         label: '智控记录',
      //         value: 'command',
      // },
      {
        label: '运行记录',
        value: 'run',
      },
      {
        label: '报警记录',
        value: 'alarm',
      },
      {
        label: '维保记录',
        value: 'work',
      }, {
        label: '维修记录',
        value: 'repair'
      }, {
        label: '健康度',
        value: 'health'
      }, {
        label: '用能分析',
        value: 'analysis'
      }
      ],
      tabIndex: 0,
      cur: 'device',
      deviceId: ''

    })
    const handelTabs = (data, i) => {
      state.tabIndex = i
      state.cur = data.value
    }

    onMounted(() => {
      if (sessionStorage.getItem('deviceId')) {
        state.deviceId = sessionStorage.getItem('deviceId')
      }
    })
    const back = () => {
      router.push({
        path: '/ba'
      })
    }
    return {
      ...toRefs(state),
      handelTabs,
      back
    }
  }
}
</script>

<style lang="scss" scoped>
.device_con {
  .tabs {
    display: flex;
    height: 40px;
    line-height: 40px;
    background: rgba(68, 114, 141, 0.1);
    box-shadow: 0px 1px 0px 0px rgba(199, 223, 255, 0.5);
    margin-bottom: 16px;

    .item {
      width: 88px;
      text-align: center;
      cursor: pointer;
      font-family: "Alibaba-PuHuiTi";
      font-weight: 400;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .closeBtn {
    position: absolute;
    right: 20px;
    top: 10px;
  }
}

.tabActive {
  color: #c7dfff;
  background: linear-gradient(
    180deg,
    rgba(199, 223, 255, 0) 0%,
    rgba(199, 223, 255, 0.3) 100%
  );
  box-shadow: 0px 1px 0px 0px #c7dfff;
}
</style>
