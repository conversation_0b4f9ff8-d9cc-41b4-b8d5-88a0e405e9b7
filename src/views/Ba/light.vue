<template>
<div class="right">
    <div class="item" style="flex:1">
        <sub-title title='运行统计' />
        <div class="item-body order">
            <div>
                <div class="total center">回路总数</div>
                <div class="order-left">
                    <div class="center">
                        <div>{{ all }}</div>
                    </div>
                </div>
            </div>
            <div class="order-right">
                <div class="order-text">
                    <div class="dot" style="background:#1AAC1A"></div><span class="text">开启数量:</span><span class="num">{{ on }}</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#C47F13"></div><span class="text">关闭数量:</span><span class="num">{{ off }}</span>
                </div>
                <!-- <div class="order-text">
                    <div class="dot" style="background:#C33838"></div><span class="text">故障数量:</span><span class="num">100</span>
                </div> -->
            </div>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title title='开关群控' />
        <div class="item-body kong">
            <el-scrollbar v-if="manual.length>0">
                <div class="list space-between" v-for="item in manual" :key="item.id">
                    <div class="name">
                        <img src="@/assets/images/common/d3.png" />
                        <div>{{ item.name }}</div>
                    </div>
                    <div class="dot"></div>
                    <div class="btn space-between">
                        <div class="center cursor" @click="writeValue(item.startGroup, item.status)">
                            <div class="center">开</div>
                        </div>

                        <div class="center cursor" @click="writeValue(item.endGroup, item.status)">
                            <div class="center">关</div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
            <noData  v-else/>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title title='事件列表' />
        <div class="item-body event">
            <el-scrollbar v-if="manualLog.length>0">
                <div class="list space-between" v-for="(item,index) in manualLog" :key="index">
                    <div class="name">
                        <img v-if="item.newValue>0" src="@/assets/images/common/on.png" />
                        <img v-else src="@/assets/images/common/off.png" />
                        <div>{{item.deviceName}}</div>
                    </div>
                    <div>
                        {{item.standardName}}
                    </div>
                    <div>
                        {{item.newValue}}
                    </div>
                    <div class="time">
                        {{item.logTime}}
                    </div>

                    <div class="bar"></div>
                </div>
            </el-scrollbar>
            <noData  v-else/>
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    onMounted
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";

import { ElMessage,ElMessageBox } from 'element-plus'
import socket from "@/utils/socket";
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "light",
    components: {},
    setup() {
        const api = inject('$api')
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            all: 0,
            on: 0,
            off: 0,
            manual: [],
            sockets: null,
            manualLog: [],
        })
        onMounted(() => {
            getDeviceTypeStatus()
            getRunConfigPage("manual")
            getRunManualPage(3, "manualLog");
        })
        const store = useAppStore();
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                JSON.parse(menu) :
                "";
        });
       
        const getDeviceTypeStatus = () => {
            api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: 'light'
            }).then(res => {
                res.data.forEach(d => {
                    //新风系统
                    if (d.code == 'light' && d.keyCode == "onoff") {
                        state.all++;
                        if (d.dataVal == '1') {
                            state.on++;

                        } else if (d.dataVal == '0') {
                            state.off++;
                        }
                    }
                })
            });
        }
        const getRunConfigPage = (type) => {
            api.getRunConfig({
                projectId: getCookie("gh_projectId"),
                tag: 2,
                type,
                menuId: activeMenus.value.id
            }).then((res) => {
                if (type == "strategy") {
                    state.strategies = res.data.strategies;
                    // getRunModelPage(2);
                }
                if (type == "manual") {
                    state.manual = res.data.manuals;

                }
            });
        };
        const getRunManualPage = (type, log) => {
            api.getRunManualLog({
                projectId: getCookie("gh_projectId"),
                menuId: activeMenus.value.id,
                page: 1,
                size: 10,
            }).then((res) => {
                state[log] = res.data;
            });
        };
        const writeValue = (group, status) => {

            ElMessageBox.confirm('是否确认该操作？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                if (group && group.length > 0) {
                group.forEach((g, i) => {
                    g.groupVars.forEach((v, j) => {
                        socket.writeValue(
                                state.sockets,
                                v.variable,
                                v.varValue,
                                "manual",
                                state.sockets.id,
                                getCookie("gh_projectId"),
                                getCookie("gh_id")
                            );
                   
                    });
                });
                ElMessage.success('操作成功')
            }
            })


         
        };

        return {
            ...toRefs(state),
            writeValue,
            activeMenus,
        }
    }
});
</script>
