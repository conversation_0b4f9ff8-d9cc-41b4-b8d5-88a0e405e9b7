<template>
<div class="right">
    <div class="item" style="flex:1">
        <sub-title title='设备统计' />
        <div class="item-body order">
            <div>
                <div class="total center">设备总数</div>
                <div class="order-left">
                    <div class="center">
                        <div>{{on+off}}</div>
                    </div>
                </div>
            </div>
            <div class="order-right">
                <div class="order-text">
                    <div class="dot" style="background:#1AAC1A"></div><span class="text">在线状态:</span><span class="num">{{on}}</span>
                </div>
                <div class="order-text">
                    <div class="dot" style="background:#C47F13"></div><span class="text">离线状态:</span><span class="num">{{off}}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title title='门禁分析' />
        <div class="item-body kong">
            <Alarm :value="value" :value1="value1" />
        </div>
    </div>
    <div class="item" style="flex:2">
        <sub-title title='事件列表' />
        <div class="item-body event">
            <el-scrollbar v-if="events.length>0">
                <div class="list space-between" v-for="item in events" :key="item.eventId">
                    <div class="name">
                        <div>
                            <span v-if="item.inAndOutType==1" style="color:#0CCA0F" class="iconfont iconmenjin"></span>
                            <span v-else style="color:green" class="iconfont iconmenjin"></span>
                        </div>
                        <div>{{ item.doorName }}</div>
                    </div>

                    <div>
                        <span v-if="item.inAndOutType==1">进</span>
                        <span v-if="item.inAndOutType==0">出</span>
                        <span v-if="item.inAndOutType==-1">未知</span>
                    </div>

                    <div class="time">
                        {{ dayjs(item.eventTime).format("YYYY-MM-DD HH:mm:ss") }}
                    </div>

                    <div class="bar"></div>
                </div>
            </el-scrollbar>
            <noData v-else></noData>
        </div>
    </div>
</div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    onMounted,
    inject,

} from 'vue';

import dayjs from 'dayjs'
import Alarm from './echart/door.vue'
export default defineComponent({
    name: "door11",
    components: {
        Alarm
    },

    setup() {
        const api = inject('$api')
        const state = reactive({
            value: [],
            value1: [],
            on: 0,
            off: 0,
            dayjs,
            events: []
        })
        state.sockets = inject("socket");
        onMounted(() => {
            // getHKDoor();
            // getHKDoorEvent();

        });

        const getHKDoor = async () => {
            let {
                data
            } = await api.getHKController({
                page: 1,
                size: 1000
            });
            state.on = 0;
            state.off = 0;
            if (data.data && data.data.list) {
                data.data.list.forEach(d => {
                    if (d.online == 1) {
                        state.on++;

                    } else {
                        state.off++;
                    }
                })
            }

        }

        const getHKDoorEvent = async () => {
            let {
                data
            } = await api.getHKDoorEvent({
                page: 1,
                size: 1000,
                bt: dayjs().startOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00',
                et: dayjs().endOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00'
            });
            state.events = data.slice(0, 10);
            let value = new Array(24).fill(0)
            let value1 = new Array(24).fill(0)
            data.forEach(d => {
                if (d.inAndOutType == 1) {
                    let num = value[dayjs(d.eventTime).hour()]
                    value[dayjs(d.eventTime).hour()] = num + 1
                } else if (d.inAndOutType == 0) {
                    let num = value1[dayjs(d.eventTime).hour()]
                    value1[dayjs(d.eventTime).hour()] = num + 1
                }

            })
            state.value = value;
            state.value1 = value1;

        }

        return {
            ...toRefs(state),

        }
    }
});
</script>

<style lang="scss" scoped>
.detail {
    display: flex;
    flex-direction: column;
    height: 100%;

    &>div:nth-child(2) {
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tab {
            background: url("@/assets/images/common/tab.png") no-repeat;
            width: 104px;
            height: 40px;

            .name {
                font-size: 14px;
                font-family: "Alibaba-PuHuiTi";
                font-weight: 400;
                color: #03FFFF;
                background: linear-gradient(0deg, #DFFFFF 0%, #ABF6FF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

        }
    }

    .active {
        background: url("@/assets/images/common/tab_active.png") no-repeat !important;
    }

}
</style>
