<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='平均温湿度' />
            <div class="item-body jifang">
                <div class="h_center">
                    <img src="@/assets/images/common/t.png" />
                    <div>{{ (temperature / count).toFixed(2) }}℃</div>
                </div>
                <div class="h_center">
                    <img src="@/assets/images/common/h.png" />
                    <div>{{ (humidity / count).toFixed(2) }}%</div>
                </div>
            </div>
        </div>
        <div class="item" style="flex：3">
            <sub-title title1="(400-1000ppm)" title='CO₂监测' />
            <div class="item-body kong">
                <thc :data1="data1" :data2="data2" />
            </div>
        </div>
        <div class="item" style="flex：3">
            <sub-title title1="(冬16-20℃;夏26-28℃)" title='温度排名' />
            <div class="item-body kong">
                <bar :data="temperatures"/>
            </div>
        </div>
        <div class="item" style="flex：3">
            <sub-title title1="(35-75%RH)" title='湿度排名' />
            <div class="item-body kong">
                <bar :data="humiditys"/>
            </div>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed
} from 'vue';

import thc from './echart/timeechart.vue'
import bar from './echart/thc.vue'
export default defineComponent({
    name: "huanjing",
    components: {
        thc, bar
    },
    setup() {
        const api = inject('$api')
        const state = reactive({
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            temperature: 0,
            temperatures:[],
            humiditys:[],
            humidity: 0,
            events: [],
            count: 0,
            data: [],
            data1: [],
            data2: [],

            

        })

        const getDataByCode = (code) => {
            api.getDataByCode({
                identifier: 'airBox'
            }).then(res => {
                state.data = [];
                state.data1 = [];
                state.data2 = [];
                if (res && res.data.length > 0) {
                    state.count = res.data.length;
                    res.data.forEach(d => {
                        let param = { name: d.name, plts: [] };
                        d.params.forEach(p => {
                            if (p.name.includes('温度')) {
                                state.temperature = state.temperature + Number(p.value)
                                param.plts.push({ name: '温度', rate: p.value })
                                state.temperatures.push({ name: d.name, rate: p.value })
                            } else if (p.name.includes('湿度')) {
                                state.humidity = state.humidity + Number(p.value)
                                state.humiditys.push({ name: d.name, rate: p.value })
                                param.plts.push({ name: '湿度', rate: p.value })
                            }
                            else if (p.name.includes('CO₂')) {
                                state.data1.push(d.name);
                                state.data2.push(p.value);
                                param.plts.push({ name: 'CO₂', rate: p.value })
                            }
                        })
                        if (Object.keys(param).length == 2) {
                            param.plts.push({ name: 'CO₂', rate: 0 })
                        }
                        state.data.push(param);
                    })
                }
            })
        }
        getDataByCode();

        return {
            ...toRefs(state)
        }
    }
});
</script>

<style lang="scss" scoped>
.home {
    .home_wrapper {
        .left {
            .header {
                font-size: 16px;
                font-family: "DOUYU";
                font-weight: 400;
                color: #E6F4FF;
            }

            .input {
                margin-bottom: 12px;
            }

            .device {
                height: calc(100% - 130px);

                .list {
                    background: rgba(16, 52, 87, 0.25);
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #FFFFFF;
                    margin-bottom: 8px;
                    border-left: 1px solid #4274A3;
                    height: 52px;

                    .state {
                        div {
                            margin-right: 48px;
                        }

                        &>:last-child {
                            margin-right: unset;
                        }
                    }
                }

            }
        }

        .right {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;

            .item {
                height: calc(100% / 3);
                // flex: 1;

                &-body {
                    display: flex;
                    justify-content: center;
                    padding: 10px;
                }
            }

            .jifang {
                .h_center {
                    div {
                        font-size: 28px;
                        font-family: "BEBAS";
                        font-weight: 400;
                        color: #FFFFFF;
                        background: linear-gradient(0deg, #FFFFFF 0%, #1196FC 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    img {
                        width: 100px;
                        height: 100px;
                    }
                }

                &>div:first-child {
                    margin-right: 23px;
                }
            }

            .kong {
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #E6F0F6;
                flex-direction: column;
                height: calc(100% - 60px);

                .name {
                    margin-right: 7px;
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                }

                .btn {
                    width: 85px;
                    height: 44px;
                    background: url('@/assets/images/common/d5.png') no-repeat;
                    background-size: 100%;
                    margin-left: 7px;

                    &>div {
                        flex: 1
                    }

                    &>div>div {
                        height: 32px;
                        width: 32px;
                        background: url("@/assets/images/common/d2.png") no-repeat;
                        background-size: 100%;
                    }
                }

                .list {
                    width: 100%;
                    background: rgba(16, 52, 87, 0.25);
                    margin-bottom: 6px;
                }

                .dot {
                    height: 1px;
                    border-bottom: 1px dashed #6E94BA;
                    flex: 1
                }
            }

            .event {
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #E6F0F6;

                flex-direction: column;

                height: calc(100% - 60px);

                .name {
                    // flex: 1;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #F0F9FF;
                    width: 232px !important;
                }

                .time {

                    font-size: 18px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #C3D2E0;
                }

                .list {
                    width: 100%;
                    background: rgba(16, 52, 87, 0.25);
                    margin-bottom: 6px;
                    height: 40px;
                    position: relative;
                }

                .bar {
                    width: 7px;
                    height: 1px;
                    background: #466582;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

            }

        }

    }
}
</style>
