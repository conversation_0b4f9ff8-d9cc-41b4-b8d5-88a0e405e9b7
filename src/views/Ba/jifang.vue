<template>
    <div class="right">
        <div class="item" style="flex:1">
            <div class="title-bar">
                <sub-title title='环境参数' />
            </div>
            <div class="environment-params">
                <!-- 舒适度仪表盘 -->
                <div class="param-card">
                    <div class="gauge-wrapper">
                        <div class="gauge">
                            <div class="gauge-scale"></div>
                            <div class="gauge-pointer" :style="{ transform: `rotate(45deg)` }"></div>
                            <div class="gauge-center">
                                <i class="iconfont iconqitilei"></i>
                            </div>
                        </div>
                        <div class="gauge-label">舒适度: <span class="value-label">优</span></div>
                    </div>
                    <div class="param-data">
                        <div class="param-row">
                            <div class="param-title">温度</div>
                            <div class="param-value">
                                <span class="num">{{ temperature.toFixed(2) }}</span>
                                <span class="unit">°C</span>
                            </div>
                        </div>
                        <div class="param-row">
                            <div class="param-title">湿度</div>
                            <div class="param-value">
                                <span class="num">{{ humidity.toFixed(2) }}</span>
                                <span class="unit">%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 颗粒物仪表盘 -->
                <div class="param-card">
                    <div class="gauge-wrapper">
                        <div class="gauge">
                            <div class="gauge-scale"></div>
                            <div class="gauge-pointer" :style="{ transform: `rotate(45deg)` }"></div>
                            <div class="gauge-center">
                                <i class="iconfont iconkeliwuzhan"></i>
                            </div>
                        </div>
                        <div class="gauge-label">颗粒物: <span class="value-label">优</span></div>
                    </div>
                    <div class="param-data">
                        <div class="param-row">
                            <div class="param-title">PM2.5</div>
                            <div class="param-value">
                                <span class="num">{{ pm25.toFixed(2) }}</span>
                                <span class="unit">μg/m³</span>
                            </div>
                        </div>
                        <div class="param-row">
                            <div class="param-title">PM10</div>
                            <div class="param-value">
                                <span class="num">{{ pm10.toFixed(2) }}</span>
                                <span class="unit">μg/m³</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 有害气体仪表盘 -->
                <div class="param-card">
                    <div class="gauge-wrapper">
                        <div class="gauge">
                            <div class="gauge-scale"></div>
                            <div class="gauge-pointer" :style="{ transform: `rotate(45deg)` }"></div>
                            <div class="gauge-center">
                                <i class="iconfont iconqiti"></i>
                            </div>
                        </div>
                        <div class="gauge-label">有害气体: <span class="value-label">优</span></div>
                    </div>
                    <div class="param-data">
                        <div class="param-row">
                            <div class="param-title">TVOC</div>
                            <div class="param-value">
                                <span class="num">{{ tvoc.toFixed(2) }}</span>
                                <span class="unit">μg/m³</span>
                            </div>
                        </div>
                        <div class="param-row">
                            <div class="param-title">甲醛</div>
                            <div class="param-value">
                                <span class="num">{{ formaldehyde.toFixed(2) }}</span>
                                <span class="unit">μg/m³</span>
                            </div>
                        </div>
                        <div class="param-row">
                            <div class="param-title">CO²</div>
                            <div class="param-value">
                                <span class="num">{{ co2.toFixed(2) }}</span>
                                <span class="unit">μg/m³</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 水质监测模块 - 全新设计 -->
                <div class="water-quality-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="iconfont iconshuizhijiance"></i>
                        </div>
                        <div class="header-title">
                            <h3>水质监测</h3>
                            <p>实时数据监控</p>
                        </div>
                        <div class="header-status" :class="waterQualityStatus">
                            {{ waterQualityText }}
                        </div>
                    </div>
                    
                    <div class="water-params">
                        <!-- 水温参数 -->
                        <div class="water-param-item">
                            <div class="param-info">
                                <span class="param-icon temp-icon">
                                    <i class="iconfont iconshuiwen"></i>
                                </span>
                                <div class="param-details">
                                    <span class="param-name">水温</span>
                                    <span class="param-value">{{ waterTemp.toFixed(1) }}<small>°C</small></span>
                                </div>
                            </div>
                            <div class="param-bar-container">
                                <div class="param-bar temp-bar" :style="{ width: (waterTemp/50*100) + '%' }"></div>
                            </div>
                        </div>
                        
                        <!-- 浊度参数 -->
                        <div class="water-param-item">
                            <div class="param-info">
                                <span class="param-icon turbidity-icon">
                                    <i class="iconfont iconzhuodu"></i>
                                </span>
                                <div class="param-details">
                                    <span class="param-name">浊度</span>
                                    <span class="param-value">{{ turbidity.toFixed(2) }}<small>NTU</small></span>
                                </div>
                            </div>
                            <div class="param-bar-container">
                                <div class="param-bar turbidity-bar" :style="{ width: (turbidity/5*100) + '%' }"></div>
                            </div>
                        </div>
                        
                        <!-- pH值参数 -->
                        <div class="water-param-item">
                            <div class="param-info">
                                <span class="param-icon ph-icon">
                                    <i class="iconfont iconphzhi"></i>
                                </span>
                                <div class="param-details">
                                    <span class="param-name">pH值</span>
                                    <span class="param-value">{{ phValue.toFixed(1) }}<small>pH</small></span>
                                </div>
                            </div>
                            <div class="param-bar-container">
                                <div class="param-bar ph-bar" :style="{ width: (phValue/14*100) + '%' }"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="updated-time">
                        最后更新: {{ new Date().toLocaleTimeString() }}
                    </div>
                </div>
                
                
     
            </div>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";
import bar from './echart/bar.vue'
export default defineComponent({
    name: "jifang",
    components: {
        bar
    },
    setup() {
        const api = inject('$api')
        const state = reactive({
            echartData: [10, 11, 12, 0, 8, 6, 2],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            temperature: 12.5,
            humidity: 62.91,
            pm25: 5,
            pm10: 11.00,
            tvoc: 12.5,
            formaldehyde: 14.00,
            co2: 502.00,
            // 水质检测数据
            waterTemp: 23.5,
            turbidity: 0.68,
            phValue: 7.2,
            showPanel: true,
            events: [],
            all1: 1,
            all2: 1,
            datas: [],
            names1: [],
            values1: [],
            names2: [],
            values2: [],
            dailyPower: 125.6,
            monthlyPower: 3752.8,
            yearlyPower: 42586.4,
            electricityData: {
                currentDayValue: 0,
                lastDayValue: 0,
                currentMonthValue: 0,
                lastMonthValue: 0,
                currentYearValue: 0,
            },
            rifdl:0,
            yfdl:0,
            nfdl:0,
        })

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });

        const getEnergyOver = (type, data) => {
            api.getEnergyOverView({
                type: type
            }).then((res) => {
                res.data.forEach((element) => {
                    Object.keys(element).forEach((key) => {
                        if (key !== 'time') {
                            data[key] = element[key]
                        }
                    })
                })
            })
        }

        const getDeviceTypeStatus = () => {
            let temperature = 0;
            let humidity = 0;
            let tvoc = 0;
            let co2 = 0;
            let formaldehyde = 0;
            let pm25 = 0;

            let allTemperature = 0;
            let allHumidity = 0;
            let allTvoc = 0;
            let allCo2 = 0;
            let allFormaldehyde = 0;
            let allPm25 = 0;


            api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: ['airBox']
            }).then(res => {
                res.data.forEach(d => {
                    if (d.identifier == 'temperature') {
                        temperature += Number(d.value);
                        allTemperature++;
                    } else if (d.identifier == 'humidity') {
                        humidity += Number(d.value);
                        allHumidity++;
                    } else if (d.identifier == 'TVOC') {
                        tvoc += Number(d.value);
                        allTvoc++;
                    } else if (d.identifier == 'CO2') {
                        co2 += Number(d.value);
                        allCo2++;
                    } else if (d.identifier == 'ch2o') {
                        formaldehyde += Number(d.value);
                        allFormaldehyde++;
                    } else if (d.identifier == 'pm2.5') {
                        pm25 += Number(d.value);
                        allPm25++;
                    }
                })
                state.temperature = temperature / allTemperature;
                state.humidity = humidity / allHumidity;
                state.tvoc = tvoc / allTvoc;
                state.co2 = co2 / allCo2;
                state.formaldehyde = formaldehyde / allFormaldehyde;
                state.pm25 = pm25 / allPm25;

            });
        }

        const closePanel = () => {
            state.showPanel = false;
        }

        const getSolar = async (id) => {
          let res= await  api.getDevicesStdById({
                deviceId: 894,
                projectId: getCookie("gh_projectId")
            })
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "din") {
                    state.rifdl += parseFloat(d.value)
                }
                if (d.identifier == "min") {
                    state.yfdl += parseFloat(d.value)
                }
                if (d.identifier == "yin") {
                    state.nfdl += parseFloat(d.value)
                }
            })

            res= await  api.getDevicesStdById({
                deviceId: 895,
                projectId: getCookie("gh_projectId")
            })
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "din") {
                    state.rifdl += parseFloat(d.value)
                }
                if (d.identifier == "min") {
                    state.yfdl += parseFloat(d.value)
                }
                if (d.identifier == "yin") {
                    state.nfdl += parseFloat(d.value)
                }
            })


            res= await  api.getDevicesStdById({
                deviceId: 933,
                projectId: getCookie("gh_projectId")
            })
            res.data[0].deviceStandards.forEach(d => {
                if (d.identifier == "din") {
                    state.rifdl += parseFloat(d.value)
                }
                if (d.identifier == "min") {
                    state.yfdl += parseFloat(d.value)
                }
                if (d.identifier == "yin") {
                    state.nfdl += parseFloat(d.value)
                }
            })




        }

        // 在setup函数中添加计算属性
        const waterQualityStatus = computed(() => {
            // 根据水质参数计算状态
            if (state.phValue >= 6.5 && state.phValue <= 8.5 && state.turbidity < 1.0 && state.waterTemp >= 20 && state.waterTemp <= 25) {
                return 'excellent';
            } else if (state.phValue >= 6.0 && state.phValue <= 9.0 && state.turbidity < 2.0 && state.waterTemp >= 18 && state.waterTemp <= 28) {
                return 'good';
            } else if (state.phValue >= 5.5 && state.phValue <= 9.5 && state.turbidity < 3.0 && state.waterTemp >= 15 && state.waterTemp <= 30) {
                return 'normal';
            } else {
                return 'poor';
            }
        });
        
        const waterQualityText = computed(() => {
            switch (waterQualityStatus.value) {
                case 'excellent': return '优';
                case 'good': return '良';
                case 'normal': return '一般';
                case 'poor': return '较差';
                default: return '未知';
            }
        });

        const getWaterQuality = async () => {
            let {
                data
            } = await api.getDevicesStdById({
                deviceId: 945,
                projectId: getCookie("gh_projectId")
            })

            data[0].deviceStandards.forEach(d => {
                if (d.identifier == "temperature") {
                    state.waterTemp = parseFloat(d.value)
                } else if (d.identifier == "ntc") {
                    state.turbidity = parseFloat(d.value)
                } else if (d.identifier == "ph") {
                    state.phValue = parseFloat(d.value)
                }
                
                
            })
        }

        getDeviceTypeStatus();

        getEnergyOver(1, state.electricityData);

        getSolar();

        getWaterQuality();

        return {
            ...toRefs(state),
            closePanel,
            waterQualityStatus,
            waterQualityText
        }
    }
});
</script>

<style lang="scss" scoped>
.home {
    .home_wrapper {
        .left {
            .header {
                font-size: 16px;
                font-family: "DOUYU";
                font-weight: 400;
                color: #E6F4FF;
            }

            .input {
                margin-bottom: 12px;
            }

            .device {
                height: calc(100% - 130px);

                .list {
                    background: rgba(16, 52, 87, 0.25);
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #FFFFFF;
                    margin-bottom: 8px;
                    border-left: 1px solid #4274A3;
                    height: 52px;

                    .state {
                        div {
                            margin-right: 48px;
                        }

                        &>:last-child {
                            margin-right: unset;
                        }
                    }
                }

            }
        }

        .right {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;

            .item {
                height: calc(100% / 3);
                // flex: 1;

                &-body {
                    display: flex;
                    justify-content: center;
                    padding: 10px;
                }
            }

            .jifang {
                .h_center {
                    div {
                        font-size: 28px;
                        font-family: "BEBAS";
                        font-weight: 400;
                        color: #FFFFFF;
                        background: linear-gradient(0deg, #FFFFFF 0%, #1196FC 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    img {
                        width: 100px;
                        height: 100px;
                    }
                }

                &>div:first-child {
                    margin-right: 23px;
                }
            }

            .kong {
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #E6F0F6;
                flex-direction: column;
                height: calc(100% - 60px);

                .name {
                    margin-right: 7px;
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                }

                .btn {
                    width: 85px;
                    height: 44px;
                    background: url('@/assets/images/common/d5.png') no-repeat;
                    background-size: 100%;
                    margin-left: 7px;

                    &>div {
                        flex: 1
                    }

                    &>div>div {
                        height: 32px;
                        width: 32px;
                        background: url("@/assets/images/common/d2.png") no-repeat;
                        background-size: 100%;
                    }
                }

                .list {
                    width: 100%;
                    background: rgba(16, 52, 87, 0.25);
                    margin-bottom: 6px;
                }

                .dot {
                    height: 1px;
                    border-bottom: 1px dashed #6E94BA;
                    flex: 1
                }
            }

            .event {
                font-family: "Alibaba-PuHuiTi";
                font-weight: normal;
                color: #E6F0F6;

                flex-direction: column;

                height: calc(100% - 60px);

                .name {
                    // flex: 1;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: normal;
                    color: #F0F9FF;
                    width: 232px !important;
                }

                .time {

                    font-size: 18px;
                    font-family: "BEBAS";
                    font-weight: 400;
                    color: #C3D2E0;
                }

                .list {
                    width: 100%;
                    background: rgba(16, 52, 87, 0.25);
                    margin-bottom: 6px;
                    height: 40px;
                    position: relative;
                }

                .bar {
                    width: 7px;
                    height: 1px;
                    background: #466582;
                    position: absolute;
                    top: 0;
                    right: 0;
                }

            }

        }

    }
}

.title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .close-btn {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: rgba(16, 52, 87, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: background 0.3s;
        margin-right: 10px;

        &:hover {
            background: rgba(255, 71, 87, 0.7);
        }

        i {
            font-size: 16px;
            color: #E6F4FF;
        }
    }
}

.environment-params {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    height: calc(100% - 60px);
    padding: 15px;
    overflow-y: auto;

    .param-card {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        margin-bottom: 15px;

        .gauge-wrapper {
            position: relative;
            width: 140px;
            min-width: 140px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .gauge {
                position: relative;
                width: 120px;
                height: 60px;
                overflow: hidden;

                .gauge-scale {
                    position: absolute;
                    width: 100%;
                    height: 200%;
                    border-radius: 50% 50% 0 0;

                    &:before {
                        content: '';
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background: conic-gradient(#ff4757 0deg 60deg,
                                #ffa502 60deg 120deg,
                                #2ed573 120deg 180deg);
                        mask: radial-gradient(transparent 65%, black 65%, black 85%, transparent 85%);
                        -webkit-mask: radial-gradient(transparent 65%, black 65%, black 85%, transparent 85%);
                        transform: rotate(-90deg);
                    }

                    &:after {
                        content: '';
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        // background-image: repeating-conic-gradient(
                        //     from -90deg,
                        //     rgba(255, 255, 255, 0.2) 0deg 2deg,
                        //     transparent 2deg 6deg
                        // );
                        mask: radial-gradient(transparent 65%, black 65%, black 85%, transparent 85%);
                        -webkit-mask: radial-gradient(transparent 65%, black 65%, black 85%, transparent 85%);
                        transform: rotate(-90deg);
                    }
                }

                .gauge-pointer {
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    width: 2px;
                    height: 25px;
                    background: #2ed573;
                    transform-origin: center bottom;
                    transform: translateX(-50%) rotate(45deg);
                    border-radius: 1px;
                    box-shadow: 0 0 3px rgba(46, 213, 115, 0.5);

                    &:after {
                        content: '';
                        position: absolute;
                        bottom: -4px;
                        left: -3px;
                        width: 8px;
                        height: 8px;
                        background: #2ed573;
                        border-radius: 50%;
                    }
                }

                .gauge-center {
                    position: absolute;
                    top: 65%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 32px;
                    height: 32px;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    i {
                        font-size: 18px;
                        color: #E6F4FF;
                    }
                }
            }

            .gauge-label {
                margin-top: 5px;
                font-size: 16px;
                color: #E6F4FF;

                .value-label {
                    color: #2ed573;
                    font-weight: bold;
                    font-size: 18px;
                }
            }
        }

        .param-data {
            flex: 1;
            padding-left: 20px;

            .param-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: rgba(16, 52, 87, 0.25);
                padding: 8px 12px;
                margin-bottom: 8px;
                border-radius: 2px;

                .param-title {
                    font-size: 14px;
                    color: #E6F4FF;
                }

                .param-value {
                    display: flex;
                    align-items: baseline;

                    .num {
                        font-size: 24px;
                        font-family: "BEBAS", sans-serif;
                        color: #2ed573;
                        line-height: 1;
                    }

                    .unit {
                        font-size: 12px;
                        color: #E6F4FF;
                        margin-left: 4px;
                    }
                }
            }
        }
    }

    .energy-stats-panel {
        width: 100%;
        margin-bottom: 8px;
        background: rgba(16, 52, 87, 0.1);
        border-radius: 10px;
        padding: 10px;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(78, 160, 255, 0.15);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at top right, rgba(46, 213, 115, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .energy-section {
            position: relative;
            z-index: 1;
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 10px;
            }

            .section-header {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                position: relative;

                .section-icon {
                    width: 28px;
                    height: 28px;
                    border-radius: 8px;
                    background: linear-gradient(135deg, #55efc4 0%, #00cec9 100%);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10px;
                    box-shadow: 0 2px 4px rgba(85, 239, 196, 0.3);

                    i {
                        font-size: 16px;
                        color: #ffffff;
                    }

                    &.solar {
                        background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
                        box-shadow: 0 2px 4px rgba(255, 234, 167, 0.3);
                    }
                }

                .section-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #E6F4FF;
                    margin-right: 6px;
                    text-shadow: 0 0 5px rgba(230, 244, 255, 0.3);
                }

                .section-unit {
                    font-size: 12px;
                    font-weight: normal;
                    color: rgba(230, 244, 255, 0.7);
                }
            }

            .energy-cards {
                display: flex;
                gap: 10px;
                margin-bottom: 0;

                .energy-card {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    background: rgba(16, 52, 87, 0.2);
                    border-radius: 8px;
                    padding: 10px 8px;
                    position: relative;
                    overflow: hidden;
                    backdrop-filter: blur(4px);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                    border: 1px solid rgba(46, 213, 115, 0.15);
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
                    }

                    &:before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 3px;
                        background: linear-gradient(to right, #10845c, #2ed573);
                    }

                    &.solar:before {
                        background: linear-gradient(to right, #00a8b5, #3bc8c4);
                    }

                    .circle-progress {
                        position: relative;
                        width: 70px;
                        height: 70px;
                        margin-bottom: 8px;

                        .circular-chart {
                            width: 100%;
                            height: 100%;
                            transform: rotate(-90deg);

                            .circle-bg {
                                fill: none;
                                stroke: rgba(16, 52, 87, 0.2);
                                stroke-width: 2;
                            }

                            .circle-progress-path {
                                fill: none;
                                stroke-width: 2.5;
                                stroke-linecap: round;
                                animation: progress-animation 1.5s ease-out forwards;
                                transition: stroke-dashoffset 1s ease;
                            }
                        }

                        .progress-value {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            font-size: 14px;
                            font-weight: bold;
                            color: #FFFFFF;
                            text-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
                            white-space: nowrap;
                        }
                    }

                    &.electricity {
                        &.daily {
                            .circle-progress-path {
                                stroke: #55efc4;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(85, 239, 196, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #55efc4, #00cec9);
                            }
                        }

                        &.monthly {
                            .circle-progress-path {
                                stroke: #74b9ff;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(116, 185, 255, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #74b9ff, #0984e3);
                            }
                        }

                        &.yearly {
                            .circle-progress-path {
                                stroke: #a29bfe;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(162, 155, 254, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #a29bfe, #6c5ce7);
                            }
                        }
                    }

                    &.solar {
                        &.daily {
                            .circle-progress-path {
                                stroke: #ffeaa7;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(255, 234, 167, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #ffeaa7, #fdcb6e);
                            }
                        }

                        &.monthly {
                            .circle-progress-path {
                                stroke: #fab1a0;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(250, 177, 160, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #fab1a0, #e17055);
                            }
                        }

                        &.yearly {
                            .circle-progress-path {
                                stroke: #ff7675;
                            }

                            .progress-value {
                                text-shadow: 0 0 4px rgba(255, 118, 117, 0.5);
                            }

                            &:before {
                                background: linear-gradient(to right, #ff7675, #d63031);
                            }
                        }
                    }

                    .card-title {
                        font-size: 12px;
                        color: rgba(230, 244, 255, 0.7);
                        margin-bottom: 2px;
                        text-align: center;
                    }

                    .card-value {
                        font-size: 12px;
                        font-family: "BEBAS", sans-serif;
                        color: rgba(230, 244, 255, 0.6);
                        text-align: center;
                    }
                }
            }
        }

        .energy-comparison {
            position: relative;
            z-index: 1;
            background: rgba(16, 52, 87, 0.2);
            border-radius: 8px;
            padding: 10px;
            backdrop-filter: blur(4px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(46, 213, 115, 0.15);

            .comparison-title {
                font-size: 14px;
                font-weight: 500;
                color: #E6F4FF;
                margin-bottom: 10px;
                text-align: center;
            }

            .comparison-chart {
                .comparison-wrapper {
                    height: 24px;
                    background: rgba(16, 52, 87, 0.3);
                    border-radius: 12px;
                    display: flex;
                    overflow: hidden;
                    margin-bottom: 8px;

                    .electricity-bar {
                        height: 100%;
                        background: linear-gradient(to right, #55efc4, #00cec9);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: width 1s cubic-bezier(0.19, 1, 0.22, 1);

                        .comparison-label {
                            font-size: 12px;
                            font-weight: bold;
                            color: #ffffff;
                        }
                    }

                    .solar-bar {
                        height: 100%;
                        background: linear-gradient(to right, #ffeaa7, #fdcb6e);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: width 1s cubic-bezier(0.19, 1, 0.22, 1);

                        .comparison-label {
                            font-size: 12px;
                            font-weight: bold;
                            color: #ffffff;
                        }
                    }
                }

                .comparison-legend {
                    display: flex;
                    justify-content: center;
                    margin-top: 6px;

                    .legend-item {
                        display: flex;
                        align-items: center;
                        margin: 0 8px;

                        .legend-color {
                            width: 12px;
                            height: 12px;
                            border-radius: 3px;
                            margin-right: 5px;

                            &.electricity {
                                background: linear-gradient(to right, #55efc4, #00cec9);
                            }

                            &.solar {
                                background: linear-gradient(to right, #ffeaa7, #fdcb6e);
                            }
                        }

                        .legend-text {
                            font-size: 11px;
                            color: rgba(230, 244, 255, 0.7);
                        }
                    }
                }
            }
        }
    }

    @keyframes progress-animation {
        0% {
            stroke-dashoffset: 100;
        }
    }
}

/* 水质监测卡片样式 */
.water-quality-card {
    width: 95%;
    margin: 0 auto 15px auto;
    background: rgba(13, 41, 68, 0.4);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(78, 160, 255, 0.2);
    position: relative;
    overflow: hidden;
    max-width: 500px;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at top right, rgba(0, 183, 255, 0.1) 0%, transparent 70%);
        pointer-events: none;
    }
    
    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        .header-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #0396FF, #0165E1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
            box-shadow: 0 2px 6px rgba(3, 150, 255, 0.4);
            
            i {
                font-size: 20px;
                color: #ffffff;
            }
        }
        
        .header-title {
            flex: 1;
            
            h3 {
                font-size: 18px;
                color: #E6F4FF;
                margin: 0 0 4px 0;
                font-weight: 500;
                letter-spacing: 0.5px;
            }
            
            p {
                font-size: 12px;
                color: rgba(230, 244, 255, 0.7);
                margin: 0;
            }
        }
        
        .header-status {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
            
            &.excellent {
                background-color: rgba(29, 209, 161, 0.15);
                color: #1dd1a1;
                border: 1px solid rgba(29, 209, 161, 0.3);
            }
            
            &.good {
                background-color: rgba(72, 219, 251, 0.15);
                color: #48dbfb;
                border: 1px solid rgba(72, 219, 251, 0.3);
            }
            
            &.normal {
                background-color: rgba(254, 211, 48, 0.15);
                color: #fed330;
                border: 1px solid rgba(254, 211, 48, 0.3);
            }
            
            &.poor {
                background-color: rgba(255, 107, 107, 0.15);
                color: #ff6b6b;
                border: 1px solid rgba(255, 107, 107, 0.3);
            }
        }
    }
    
    .water-params {
        .water-param-item {
            margin-bottom: 14px;
            
            .param-info {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                
                .param-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 10px;
                    
                    &.temp-icon {
                        background: linear-gradient(135deg, #FF9E9E, #FF4757);
                        box-shadow: 0 2px 4px rgba(255, 158, 158, 0.3);
                    }
                    
                    &.turbidity-icon {
                        background: linear-gradient(135deg, #54A0FF, #2E86DE);
                        box-shadow: 0 2px 4px rgba(84, 160, 255, 0.3);
                    }
                    
                    &.ph-icon {
                        background: linear-gradient(135deg, #5EE7DF, #2BDEAC);
                        box-shadow: 0 2px 4px rgba(94, 231, 223, 0.3);
                    }
                    
                    i {
                        font-size: 16px;
                        color: #ffffff;
                    }
                }
                
                .param-details {
                    flex: 1;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    
                    .param-name {
                        font-size: 14px;
                        color: rgba(230, 244, 255, 0.9);
                        font-weight: 500;
                    }
                    
                    .param-value {
                        font-size: 20px;
                        font-family: "BEBAS", sans-serif;
                        color: #E6F4FF;
                        letter-spacing: 0.5px;
                        
                        small {
                            font-size: 12px;
                            font-family: "Alibaba-PuHuiTi";
                            opacity: 0.8;
                            margin-left: 2px;
                        }
                    }
                }
            }
            
            .param-bar-container {
                height: 6px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
                overflow: hidden;
                
                .param-bar {
                    height: 100%;
                    border-radius: 3px;
                    transition: width 1s cubic-bezier(0.19, 1, 0.22, 1);
                    
                    &.temp-bar {
                        background: linear-gradient(to right, #FF9E9E, #FF4757);
                    }
                    
                    &.turbidity-bar {
                        background: linear-gradient(to right, #54A0FF, #2E86DE);
                    }
                    
                    &.ph-bar {
                        background: linear-gradient(to right, #5EE7DF, #2BDEAC);
                    }
                }
            }
        }
    }
    
    .updated-time {
        text-align: right;
        font-size: 11px;
        color: rgba(230, 244, 255, 0.5);
        margin-top: 6px;
    }
}
</style>
