<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='车位统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">车位总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>0</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">空闲车位:</span><span
                            class="num">0</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">占用车位:</span><span
                            class="num">0</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='入场分析' />
            <div class="item-body kong">
                <Alarm :xAxisData="xAxisData" :echartData="value1" />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='出场分析' />
            <div class="item-body event">
                <Alarm :xAxisData="xAxisData" :echartData="value" />
            </div>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed, inject, onUnmounted
} from 'vue';

import {
    getCookie,
} from "@/utils/cookie";
import Alarm from '@/components/echarts/weekEventEchart.vue'
import dayjs from 'dayjs'
import {
    onMounted
} from 'vue';
export default defineComponent({
    name: "park",
    components: {
        Alarm
    },
    setup() {
        const api = inject('$api')
        const emitter = inject("mitt");
        const state = reactive({
            echartData: [10, 11, 12, 0, 8, 6, 2],
            value: [],
            value1: [],
            xAxisData: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
            interval: null,
        })

        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });

        function getPlate(total = 5) {
            let stateList = '京津冀晋辽吉沪苏浙皖闽琼赣鲁豫鄂湘粤渝川贵云陕甘蒙黑桂藏青宁新'
            let charList = 'ABCDEFGHJKLMNQPRSTUVWXYZ'
            let numList = '1234567890'
            let halfList = [charList, numList]
            let state = dicingChar(stateList)
            let city = dicingChar(charList)
            let sequence = ''
            while (total--) {
                sequence += dicingChar(halfList[Math.round(Math.random())])
            }
            return `${state}${city} ${sequence}`;
        }

        function dicingChar(series) {
            return series[~~(Math.random() * series.length)]
        }


        onMounted(() => {
          
            
          
        });

        onUnmounted(() => {
            if (state.interval) {
                clearInterval(state.interval);
            }
        })

        const crossRecords = async () => {
            let {
                data
            } = await api.crossRecords({
                page: 1,
                size: 1000,
                bt: dayjs().startOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00',
                et: dayjs().endOf('day').format('YYYY-MM-DDTHH:mm:ss') + '+08:00'
            });
            // state.list = data.list.slice(0, 10);
            let value = new Array(24).fill(0)
            let value1 = new Array(24).fill(0)
            let xAxisData = [];
            for (let i = 0; i < 24; i++) {
                xAxisData[i] = i + ":00"
            }
            data.list.forEach(d => {
                if (d.vehicleOut == 1) {//出场
                    let num = value[dayjs(d.eventTime).hour()]
                    value[dayjs(d.eventTime).hour()] = num + 1
                } else if (d.vehicleOut == 0) {//进场
                    let num = value1[dayjs(d.eventTime).hour()]
                    value1[dayjs(d.eventTime).hour()] = num + 1
                }

            })
            state.value = value;
            state.value1 = value1;
            state.xAxisData = xAxisData;

        }

        return {
            ...toRefs(state)
        }
    }
});
</script>

<style lang="scss" scoped></style>
