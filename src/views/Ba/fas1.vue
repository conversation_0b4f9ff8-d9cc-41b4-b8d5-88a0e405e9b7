<template>
    <div class="right">
        <div class="item" style="flex:1">
            <sub-title title='运行统计' />
            <div class="item-body order">
                <div>
                    <div class="total center">设备总数</div>
                    <div class="order-left">
                        <div class="center">
                            <div>{{ all }}</div>
                        </div>
                    </div>
                </div>
                <div class="order-right">
                    <div class="order-text">
                        <div class="dot" style="background:#1AAC1A"></div><span class="text">正常:</span><span class="num">{{ on
                            }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C47F13"></div><span class="text">超压:</span><span class="num">{{ off
                            }}</span>
                    </div>
                    <div class="order-text">
                        <div class="dot" style="background:#C33838"></div><span class="text">欠压:</span><span
                            class="num">{{ fault }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='设备群控' />
            <div class="item-body kong">
                <el-scrollbar v-if="manual.length > 0">
                    <div class="list space-between" v-for="item in manual" :key="item.id">
                        <div class="name">
                            <img src="@/assets/images/common/d3.png" />
                            <div>{{ item.name }}</div>
                        </div>
                        <div class="dot"></div>
                        <div class="btn space-between">
                            <div class="center cursor" @click="writeValue(item.startGroup, item.status)">
                                <div class="center">开</div>
                            </div>

                            <div class="center cursor" @click="writeValue(item.endGroup, item.status)">
                                <div class="center">关</div>
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>
        </div>
        <div class="item" style="flex:2">
            <sub-title title='运行事件' />
            <div class="item-body event">
                <el-scrollbar v-if="manualLog.length > 0">
                    <div class="list space-between" v-for="(item, index) in manualLog" :key="index">
                        <div class="name">
                            <img v-if="item.newValue > 0" src="@/assets/images/common/on.png" />
                            <img v-else src="@/assets/images/common/off.png" />
                            <div>{{ item.deviceName }}</div>
                        </div>
                        <div>
                            {{ item.standardName }}
                        </div>
                        <div>
                            {{ item.newValue }}
                        </div>
                        <div class="time">
                            {{ item.logTime }}
                        </div>

                        <div class="bar"></div>
                    </div>
                </el-scrollbar>
                <noData v-else />
            </div>
        </div>
    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    inject,
    watch
} from 'vue';
import {
    getCookie,
} from "@/utils/cookie";
import socket from "@/utils/socket";

import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "qp",
    components: {},
    setup() {
        const api = inject('$api')
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            manual: [],
            sockets: null,
            manualLog: [],
            on: 0,
            off: 0,
            fault: 0,
            all: 0,
        })
        const store = useAppStore()
        state.sockets = inject("socket");
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        watch(activeMenus, (val) => {
            getDeviceTypeStatus();
            getRunConfigPage("manual");
            getRunManualPage(3, "manualLog")
        });

        const getRunConfigPage = (type) => {
            state.manual = [];
            api.getRunConfig({
                projectId: getCookie("gh_projectId"),
                tag: 2,
                type,
                menuId: activeMenus.value.id
            }).then((res) => {
                if (type == "strategy") {
                    // state.strategies = res.data.strategies;
                    // getRunModelPage(2);
                }
                if (type == "manual") {
                    state.manual = res.data.manuals;

                }
            });
        };
        const writeValue = (group, status) => {
            ElMessageBox.confirm('是否确认该操作？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                if (group && group.length > 0) {
                    group.forEach((g, i) => {
                        g.groupVars.forEach((v, j) => {
                            socket.writeValue(
                                state.sockets,
                                v.variable,
                                v.varValue,
                                "manual",
                                state.sockets.id,
                                getCookie("gh_projectId"),
                                getCookie("gh_id")
                            );
                        });
                    });
                    ElMessage.success('操作成功')
                }
            })

        };

        const getRunManualPage = (type, log) => {
            api.getDeviceLog({
                projectId: getCookie("gh_projectId"),
                menuId: activeMenus.value.id,
                page: 1,
                size: 10,
            }).then((res) => {
                state[log] = res.data;
            });
        };

        const getDeviceTypeStatus = () => {
            let typeCode = ['FCU', 'HaierVRF'];
            if (activeMenus.value.name.includes('空调')) {
                typeCode = ['FCU', 'HaierVRF']
            } else if(activeMenus.value.name.includes('气瓶')) {
                typeCode = ['gasPa']
            }
            let on = 0,
                off = 0,
                fault = 0,
                all = 0;
            api.getDeviceTypeStatus({
                projectId: getCookie("gh_projectId"),
                typeCode: typeCode
            }).then(res => {
                res.data.forEach(d => {
                    //新风系统
                    if (d.identifier == "pressSts") {
                        all++;
                        if (d.value == '0') {
                            on++;

                        } else if (d.value == '2') {
                            off++;
                        }else if (d.value == '1') {
                            fault++;
                        }
                    }
                })
                state.on = on;
                state.off = off;
                state.all = all;
                state.fault = fault;
            });
        }


        getRunConfigPage("manual")
        getRunManualPage(3, "manualLog");
        getDeviceTypeStatus();

        return {
            ...toRefs(state),
            writeValue,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>
.event {
    .name {
        width: auto !important;

        &>div {
            width: 95px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .time {
        width: 130px;
    }
}
</style>
