<template>
    <div class="energy_container">
  
       <iframe src="http://**********:9016/#/preview?diagramId=169"></iframe>

    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    onMounted,
    inject,
} from 'vue';

import { ElMessageBox } from 'element-plus'
import pop from '@/components/pop/index.vue'
import dayjs from 'dayjs'

export default defineComponent({
    name: "water",
    components: {
        pop,
    },
    setup() {
        const api = inject('$api')
        const state = reactive({
            keyword: "",
            room: [], //设备列表
            meeting: [],
            popName: '',
            auditVisible: false,
            desc: null,
            on: 0,
            off: 0,
            all: 0,
            audit: [
            ],
            activeMenus: {
                popName: ''
            },
            rooms: [
                {
                    name: '1F小型接待会议室',
                    value: 'oa_1'
                },
                {
                    name: '2F大会议室',
                    value: 'oa_2'
                },
                {
                    name: '3F小会议室',
                    value: 'oa_3'
                },
                {
                    name: '4F小会议室',
                    value: 'oa_4'
                },
                {
                    name: '5F报告厅',
                    value: 'oa_5'
                },


            ],
            oldRooms: [],
            roomsInfo: [],
            roomAppsInfo: [],
            roomAppsInfo1: [],
            dayjs: dayjs,
        })

        onMounted(() => {


        });

        const emitter = inject("mitt")

        const getRoom = async () => {
            let {
                data
            } = await api.getRoom({
                keyword: state.keyword
            })
            state.on = 0;
            state.off = 0;
            if (data) {
                state.room = data;
                data.forEach(d => {
                    if (d.usage.appointTime.length > 0) {
                        state.on++;
                    } else {
                        state.off++;
                    }
                })
            }

        };
       
        const handleCurrentChange = (page) => {
            state.page = page;
            getRoom();
        }

        const search = () => {

            if (state.keyword) {
                state.rooms = state.oldRooms.filter(item => item.name.includes(state.keyword))
            } else {
                state.rooms = state.oldRooms;
            }
        }

        const agreeMeeting = (item) => {
            ElMessageBox.confirm('是否确认要同意该会议预约？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const {
                    success
                } = await api.auditMeeting({
                    id: state.meeting.id,
                    desc: "",
                    pass: 1
                });
            })
        };

        const unAgreeMeeting = (item) => {
            state.auditVisible = true;
            state.meeting = item;

        }

        const auditMeeting = async () => {
            const {
                success
            } = await api.auditMeeting({
                id: state.meeting.id,
                desc: state.desc,
                pass: 0
            });
            if (success) {
                state.auditVisible = false;
            }
        }

        const zoomToPosition = (item) => {

            emitter.emit('ue', {
                type: 'position',
                token: item.value,
            })

        };



        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            agreeMeeting,
            unAgreeMeeting,
            auditMeeting,
            zoomToPosition
        }
    }
});
</script>

<style lang="scss" scoped>

.energy_container{
    background: transparent !important;
}
.kong {
    .list {
        padding: 0 20px;
    }

    .name {
        display: flex;
        // flex-direction: column;
        // align-items: flex-start;
        width: 100%;

        .hour {
            margin: 0 10px
        }
    }

    .descrition {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-left: 10px;
    }
}

.event {
    .name {
        display: flex;
        // flex-direction: column;
        width: auto;

        .hour {
            margin: 0 10px;
            white-space: nowrap;
            width: 52px;
        }

        &>div:nth-child(2) {
            width: 90px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .time {
        width: 120px;

    }


}
</style>
