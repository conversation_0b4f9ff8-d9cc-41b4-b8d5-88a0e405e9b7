<template>
<div class="layout_wrapper">
    <el-form :inline="true" class="search_box form_inline" size="small">
        <el-form-item label="时间">
            <el-date-picker v-model="date" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="search">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="巡更人员">
            <el-select placeholder="请选择" clearable v-model="userId" >
                <el-option v-for="item in users" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button @click="search" class="searchBtn" size="small" type="text">查询</el-button>
        </el-form-item>
    </el-form>
    <div class="table">
        <el-table :height="tableHeight" :data="list"  fit table-layout="auto">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="logTime" label="巡检时间" align="center">
            </el-table-column>
            <el-table-column prop="staffName" label="巡检人员" align="center">
            </el-table-column>
            <el-table-column prop="pointName" label="巡检点" align="center">
            </el-table-column>
            <el-table-column prop="taskName" label="关联任务" align="center">
            </el-table-column>
            <el-table-column prop="instanceId" label="关联工单" align="center">
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="text" class="editBtn" @click="edit(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="page center">
        <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
        </el-pagination>
    </div>
</div>
</template>

<script>
import dayjs from 'dayjs'
import {
    getCookie
} from '@/utils/cookie'
import {
    computed,
    
    onMounted,
    reactive,
    toRefs,
    watch
} from 'vue'
import {
    useRouter
} from 'vue-router'
import { useAppStore } from '@/stores/app'


export default {
    setup() {
        const api = inject('$api')
        const router = useRouter()
        const store = useAppStore()
        const state = reactive({
            date: [],
            page: 1,
            size: 10,
            total: 0,
            status: '',
            list: [],
            users: [],
            userId: '',
            tableHeight: window.innerHeight * 0.60,
        })
        onMounted(() => {
            state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'))
            state.date.push(dayjs().format('YYYY-MM-DD 23:59:59'))
            getUserList()
            getPatrolRecordList()
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (res) => {
            if (val) {
                getUserList()
                getPatrolRecordList
            }
        })
        const search = () => {
            state.page = 1
            getPatrolRecordList()
        }
        const getPatrolRecordList = () => {
            api.getPatrolRecord({
                projectId: getCookie('gh_projectId'),
                page: state.page,
                size: state.size,
                type: 5, //1-4 是方便小程序查询,
                userId: state.userId,
                bt: typeof state.date[0] == 'string' ?
                    state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                et: typeof state.date[1] == 'string' ?
                    state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
            }).then((res) => {
                state.list = res.data
                state.total = res.total
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getPatrolRecordList()
        }
        const edit = (row) => {
            sessionStorage.setItem('record', JSON.stringify(row))
            router.push({
                path: `/patrol/record/detail`,
            })
        }
        const getUserList = () => {
            api.getUser({
                projectId: [getCookie('gh_projectId')],
                status: 1,
            }).then((res) => {
                state.users = res.data
            })
        }

        return {
            ...toRefs(state),
            getUserList,
            edit,
            search,
            getPatrolRecordList,
            handleCurrentChange,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
</style>
