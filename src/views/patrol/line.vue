<template>
    <div class="wrapper h100">
        <el-form :inline="true" class="search_box form_inline" size="small">
            <div>
                <el-form-item label="名称">
                    <el-input v-model="keyword" placeholder="请输入名称查询"></el-input>
                </el-form-item>
                <el-form-item>
                    <div class="searchBtn" type="text" @click="search">查询</div>
                </el-form-item>
            </div>

        </el-form>

        <el-table class="table" :data="list"  fit @select="select" @select-all="select"
            table-layout="auto">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="regionName" label="管理区" align="center">
            </el-table-column>
            <el-table-column prop="name" label="名称" align="center">
            </el-table-column>
            <el-table-column prop="number" label="编号" align="center">
            </el-table-column>
            <el-table-column prop="pointNum" label="巡更点数量" align="center">
            </el-table-column>
            <el-table-column prop="statusStr" label="状态" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.status == 0" type="success">正常</el-tag>
                    <el-tag v-else type="danger">作废</el-tag>
                </template>
            </el-table-column>
        </el-table>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>


</template>

<script>

import {
    onMounted,
    reactive,
    toRefs,
    ref,
    
} from 'vue'


export default {
    name: 'patrolline',

    setup() {
        const title = ref(null)
        const api = inject('$api')
        const state = reactive({
            page: 1,
            size: 10,
            total: 0,
            list: [],
            keyword: '',

        })
        const getPoint = () => {
            api.getPatrolLine({
                page: state.page,
                size: state.size,
                search: state.keyword,
            }).then((res) => {
                state.list = res.data.result.content;
                state.total = res.data.result.totalElements;
            })
        }

        onMounted(() => {
             getPoint()
        })


        const handleCurrentChange = (page) => {
            state.page = page
            getPoint()
        }



        const search = () => {
            state.page = 1
            getPoint()
        }
        return {
            ...toRefs(state),
            getPoint,
            title,
            search,
            handleCurrentChange,
        }
    },
}
</script>

<style lang="scss" scoped>
.qrcode {
    background: #091822;
}
</style>
