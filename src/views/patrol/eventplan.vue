<template>
    <div class="plan_container layout_wrapper">
        <div class="left-plan">
            <panel title="巡检计划"></panel>
            <div class="btn">
                <div class="btn-group search_box">
                    <div type="primary" icon="Plus" size="mini" class="searchBtn" @click="addSecurityPlan">新增</div>
                    <div type="primary" icon="Minus" size="mini" class="delBtn" @click="delPlan">删除</div>
                    <div type="primary" icon="el-icon-setting" size="mini" class="updateBtn" @click="editPlan">编辑</div>
                </div>
            </div>
            <el-scrollbar class="tree">
                <el-tree ref="tree" :data="data" node-key="id" check-strictly :props="props" show-checkbox
                    @check="handleCheckChange" />
            </el-scrollbar>
        </div>
        <div class="right-plan">
            <FullCalendar ref="calendar" :options='calendarOptions'>
            </FullCalendar>
        </div>
        <el-dialog align-center :append-to-body="true" title="计划配置" draggable v-model="planVisibale" width="500px"
            custom-class="addDiagram border0">
            <el-form label-width="100px" label-position="right" ref="form" :model="plan" :rules="rule">
                <el-form-item label="计划名称" prop="name">
                    <el-input v-model="plan.name"></el-input>
                </el-form-item>

                <el-form-item label="计划周期">
                    <el-select v-model="plan.type" style="width:100%">
                        <el-option label="周" :value="1"></el-option>
                        <el-option label="月" :value="2"></el-option>
                        <el-option label="年" :value="3"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="线路选择" prop="lineId">
                    <el-select v-model="plan.lineId" style="width:100%">
                        <el-option v-for="item in lines" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="接收组" prop="staff">
                    <el-select v-model="plan.staff" style="width:100%">
                        <el-option v-for="item in staff" :label="item.tagName" :value="parseInt(item.tagValue)"
                            :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否启用" prop="staff">
                    <el-select v-model="plan.status" style="width:100%">
                        <el-option v-for="(item, index) in status" :label="item.name" :value="item.status"
                            :key="index"></el-option>
                        <!-- <el-option label="停止" value="false"></el-option> -->
                    </el-select>
                </el-form-item>

            </el-form>
            <template #footer>
                <span class="dialog-footer search_box">
                    <div type="primary" size="small" class="searchBtn" @click.prevent="savePlan">确 定</div>
                    <div type="danger" size="small" class="delBtn" v-if="edit" @click="delPlanEvent">删除</div>
                </span>
            </template>

        </el-dialog>

        <el-dialog align-center :append-to-body="true" title="计划时间" draggable v-model="schedulerVisibale" width="500px"
            custom-class="addDiagram border0">
            <el-form label-width="100px" label-position="right" ref="form1">

                <el-form-item label="开始时间">
                    <el-input v-model="planEvent.startTime"></el-input>
                </el-form-item>
                <el-form-item label="结束时间">
                    <el-input v-model="planEvent.endTime"></el-input>
                </el-form-item>
                <el-form-item label="计划次数">
                    <el-input v-model="planEvent.times"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer search_box">
                    <div type="primary" size="small" class="searchBtn" :disabled="loading"
                        @click.prevent="savePlanEvent">确 定</div>
                    <div type="danger" size="small" class="delBtn" @click="delPlanEvent">删除</div>
                </span>
            </template>

        </el-dialog>

    </div>
</template>

<script>
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import multiMonthPlugin from '@fullcalendar/multimonth'
import cn from '@fullcalendar/core/locales/zh-cn'

import {
    PlanEvent
} from '@/model/plan'

import dayjs from 'dayjs'
import {
    getDicUtilR
} from '@/api/dic'
import {
    getCookie
} from '@/utils/cookie'

import {
    getPatrolLine,
    getSecurityPlan,
    addSecurityPlan,
    editSecurityPlan,
    deleteSecurityPlan,
    addSecurityPlanEvent,
    editSecurityPlanEvent,
    deleteSecurityPlanEvent
} from '@/api/patrol.ts'

import {
    getProjectStaff,
} from '@/api/staff'

export default {
    name: 'patroleventplan',
    components: {
        FullCalendar
    },

    data() {
        return {
            schedulerVisibale: false,
            plan: new PlanEvent(),
            calendarOptions: {
                plugins: [
                    dayGridPlugin,
                    multiMonthPlugin,
                    timeGridPlugin,
                    interactionPlugin // needed for dateClick
                ],
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridWeek,dayGridMonth,dayGridYear'
                },
                // dayHeaderContent:function(date){
                //     return date+'111';
                // },
                dayCellContent: function (arg) {
                    // MM-DD
                    return dayjs(arg.date).format('MM-DD');


                },

                initialView: 'timeGridWeek',
                multiMonthMaxColumns: 1,
                editable: true,
                selectable: true,
                selectMirror: true,
                dayMaxEvents: true,
                weekends: true,
                select: this.handleDateSelect,
                eventClick: this.handleEventClick,
                // eventsSet: this.handleEvents,
                height: "100%",
                timezone: "local",
                allDaySlot: false,
                locale: cn,
                eventResize: this.resize,
                eventDrop: this.resize
            },
            staff: [], //人员专业组
            currentEvents: [],
            lines: [],
            plans: [],
            planVisibale: false,
            staffs: [],
            selectId: [], //tree check
            props: {
                label: 'name',
            },
            data: [],
            planEvent: {
                id: 0,
                startTime: '',
                endTime: '',
                planId: '',
                day: '',
                times: 1,
            },
            event: null,
            status: [{
                name: '启用',
                status: true
            }, {
                name: '停止',
                status: false
            }],
            form1: null,
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change'
                }],
                lineId: [{
                    required: true,
                    message: '线路不能为空',
                    trigger: 'change'
                }],
                staff: [{
                    required: true,
                    message: '接收组不能为空',
                    trigger: 'change'
                }],
            },
            edit: false,
            loading: false,
        }
    },
    mounted() {
        this.getPlan();
        this.getProjectStaff();
        this.getLine();
        let _this = this;

        let pre = document.getElementsByClassName('fc-prev-button');
        pre[0].addEventListener('click', () => {
            this.handleBtn();
        })
        let next = document.getElementsByClassName('fc-next-button');
        next[0].addEventListener('click', () => {
            this.handleBtn();
        });

        getDicUtilR({
            dicCode: 'profession_type',
            projectId: getCookie('gh_projectId')
        }).then(res => {
            this.staff = res.data;
        });

    },

    methods: {
        handleBtn() {
            if (this.selectId.length > 0) {
                this.getPlanEvent(this.selectId[0].id, this.selectId[0].type);
            }
        },
        handleCheckChange(data, state) {
            this.selectId = [];
            let _this = this;
            let keys = this.$refs.tree.getCheckedKeys();
            if (keys.length == 0 && state.checkedKeys.length == 0) {
                _this.$refs.tree.setChecked(data.id, false);
                this.clearEvent();
            } else {
                if (keys.length > 0)
                    keys.forEach(k => {
                        if (k != data.id) {
                            _this.$refs.tree.setChecked(k, false);
                        }

                    });
                this.$refs.tree.setChecked(data.id, true);
                this.selectId.push(data);
                this.clearEvent();
                this.getPlanEvent(data.id, data.type);
            }

        },
        getProjectStaff() {
            getProjectStaff({
                projectId: [getCookie('gh_projectId')],
                // type: 2
            }).then(res => {
                this.staffs = res.data;
            });
        },
        getLine() {
            getPatrolLine({
                projectId: getCookie('gh_projectId')
            }).then(res => {
                this.lines = res.data;
            });
        },
        getPlanEvent(id, type) {
            let calendar = this.$refs.calendar.getApi();
            let end = dayjs(calendar.currentData.viewApi.activeEnd).add(-8, 'hour').format("YYYY-MM-DD 23:59:59");
            let start = dayjs(calendar.currentData.viewApi.activeStart).add(-8, 'hour').format("YYYY-MM-DD 00:00:00");
            // if (type == 2) {
            //     this.calendarOptions.headerToolbar = {
            //         left: '',
            //         center: '',
            //         right: ''
            //     };
            // } else if (type == 1) {
            let right = "timeGridWeek,dayGridMonth,dayGridYear";

            if (this.selectId && this.selectId.length > 0) {
                let plan = this.selectId[0];
                if (plan.type == 1) {
                    right = "timeGridWeek";
                } else if (plan.type == 2) {
                    right = "dayGridMonth";
                } else if (plan.type == 3) {
                    right = "dayGridYear";
                }
            }
            this.calendarOptions.headerToolbar = {
                left: 'prev,next today',
                center: 'title',
                right: right,
               
            };
            this.$refs.calendar.getApi().changeView(right);
            // this.calendarOptions.initialView = right;
            // }

            getSecurityPlan({
                id: id,
                // bt: type == 1 ? start : null,
                // et: type == 1 ? end : null,
                projectId: getCookie('gh_projectId'),
            }).then(res => {
                if (res.data && res.data.length > 0 && res.data[0].planEvents) {
                    this.clearEvent();
                    res.data[0].planEvents.forEach(d => {
                        let start = d.startTime;
                        let end = d.endTime;

                        // let current = dayjs().day();
                        // if (current == 0) {
                        //     current = 7;
                        // }
                        // if (d.day == 0) {
                        //     d.day = 7;
                        // }
                        // let currentDay = dayjs().date(dayjs().date() + d.day - current);
                        // start = currentDay.format("YYYY-MM-DD ") + d.startTime.split(' ')[1];
                        // end = currentDay.format("YYYY-MM-DD ") + d.endTime.split(' ')[1];


                        this.$refs.calendar.getApi().addEvent({
                            title: "计划名称:" + res.data[0].name,
                            id: d.id,
                            start: start,
                            end: end,
                            day: d.day,
                            color: '#3E82D2',
                            times: d.times
                        });

                    })
                }
            });
        },

        getPlan() {
            getSecurityPlan({
                projectId: getCookie('gh_projectId'),
            }).then(res => {
                this.data = res.data;
            });
        },
        clearEvent() {
            let api = this.$refs.calendar.getApi();
            api.getEvents().forEach(e => {
                e.remove();
            })
        },

        // handleWeekendsToggle() {
        //     this.calendarOptions.weekends = !this.calendarOptions.weekends // update a property
        // },

        handleDateSelect(selectInfo) {
            if (this.selectId.length > 0) {
                this.planEvent = {
                    id: 0,
                    startTime: '',
                    endTime: '',
                    planId: '',
                    times: 1
                };
                this.planEvent.planId = this.selectId[0].id;
                this.schedulerVisibale = true;
                this.planEvent.startTime = dayjs(selectInfo.start).format("YYYY-MM-DD HH:mm:ss");
                this.planEvent.endTime = dayjs(selectInfo.end).format("YYYY-MM-DD HH:mm:ss");
                this.planEvent.day = dayjs(selectInfo.start).day();
            }

        },

        handleEventClick(clickInfo) {
            // let event = this.plans.find((v, i) => v.id == clickInfo.event.id);
            this.schedulerVisibale = true;
            this.event = clickInfo.event;
            this.createEvent(clickInfo.event);
            // this.event=this.planEvent;
        },

        handleEvents(events) {
            this.currentEvents = events
        },
        addSecurityPlan() {
            this.plan = new PlanEvent()
            this.planVisibale = true;
            this.edit = false;
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },
        editPlan() {
            if (this.selectId.length == 0) {
                this.$message({
                    type: 'warning',
                    message: '请选择要编辑的计划'
                });
                return;
            }
            this.planVisibale = true;
            this.edit = true;
            this.plan = {
                ...this.selectId[0]
            };
        },
        delPlan() {
            if (this.selectId.length == 0) {
                this.$message({
                    type: 'warning',
                    message: '请选择要删除的计划'
                });
                return;
            }

            this.$confirm('是否确认要删除该数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                deleteSecurityPlan({
                    ids: this.selectId[0].id
                }).then(res => {
                    this.getPlan();
                    this.selectId[0] = [];
                });
            });
        },
        savePlan() {
            this.$refs.form.validate(validate => {
                if (validate) {
                    this.plan.projectId = getCookie('gh_projectId');
                    // this.plan.day = dayjs().day();
                    this.tag = false;
                    if (this.plan.id == 0) {
                        addSecurityPlan(this.plan).then(res => {
                            this.planVisibale = false;
                            this.getPlan();
                        });
                    } else {
                        editSecurityPlan(this.plan).then(res => {
                            this.planVisibale = false;
                            this.getPlan();
                        });
                    }
                }
            });

        },
        savePlanEvent() {
            this.loading = true;
            if (this.planEvent.id == 0) {
                addSecurityPlanEvent(this.planEvent).then(res => {
                    this.schedulerVisibale = false;
                    this.clearEvent();
                    this.getPlanEvent(this.selectId[0].id, this.selectId[0].type);
                    this.loading = false;
                })
            } else {
                editSecurityPlanEvent(this.planEvent).then(res => {
                    this.schedulerVisibale = false;
                    this.clearEvent();
                    this.getPlanEvent(this.selectId[0].id, this.selectId[0].type);
                    this.loading = false;
                });
            }
        },
        delPlanEvent() {
            if (this.planEvent && this.planEvent.id) {
                deleteSecurityPlanEvent({
                    ids: this.planEvent.id
                }).then(res => {
                    this.event.remove();
                    this.event = null;
                    this.schedulerVisibale = false;
                });
            }
        },
        resize(info) {
            this.schedulerVisibale = true;
            this.createEvent(info.event);
        },
        // drop(info) {
        //     this.schedulerVisibale = true;
        //     this.createEvent(info.);
        // },
        createEvent(event) {
            this.planEvent.id = event.id;
            this.planEvent.times = event.extendedProps.times;
            this.planEvent.name = event.title.split(':')[0];
            this.planEvent.day = dayjs(event.start).day();
            this.planEvent.startTime = dayjs(event.start).format("YYYY-MM-DD HH:mm:ss");
            this.planEvent.endTime = dayjs(event.end).format("YYYY-MM-DD HH:mm:ss");
            this.event = event;
        }
    }
}
</script>

<style lang="scss" scoped>
.plan_container {
    display: flex;
    width: 100%;

    .left-plan {
        width: 334px;
        margin-right: 16px;
        height: calc(100% - 224px);

        .btn {
            display: flex;
            justify-content: space-between;
        }

        .tree {
            padding: 10px 0;
            height: 530px;
            border: 1px solid #2b2e32;
            box-sizing: border-box;
        }
    }

    .right-plan {
        flex: 1;
        height: 530px;
        color: #fff;

    }
}
</style>
