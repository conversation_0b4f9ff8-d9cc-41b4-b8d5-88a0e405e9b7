<template>
<div class="content_con layout_wrapper">
    <div class="wrapper">
        <div class="left-content">
            <sub-title2 title="设备类型"></sub-title2>
            <el-scrollbar class="tree-list tree">
                <el-tree ref="tree" :data="data" node-key="id" check-strictly :props="props" show-checkbox @check="handleCheckChange" />
            </el-scrollbar>
        </div>
        <div class="right_tab">
            <div class="btn-group">
                <el-button icon="Plus" type="primary" size="mini" @click="add" class="addBtn">新增</el-button>
                <el-button icon="Minus" type="primary" size="mini" @click="del" class="deleteBtn">删除</el-button>
            </div>
            <div class="table">
                <el-table :data="list" :height="tableHeight" fit @select="select" @select-all="select" table-layout="auto">
                    <template #empty>
                        <no-data />
                    </template>
                    <el-table-column type="selection" width="55" align="center">
                    </el-table-column>
                    <el-table-column prop="name" label="名称" align="center">
                    </el-table-column>
                    <el-table-column prop="content" label="内容" align="center">
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                        <template #default="scope">
                            <el-button type="text" class="editBtn" @click="edit(scope.row)">编辑</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page" layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>
        </div>

        <el-dialog align-center :append-to-body="true" v-model="dialogContentVisible" title="巡检内容管理" custom-class="custom_dialog drag-target" width="482px">
            <el-form ref="ruleForm" :model="content" :rules="rule">
                <el-form-item label="名称" prop="name">
                    <el-input placeholder="请输入名称" v-model="content.name"></el-input>
                </el-form-item>
                <el-form-item label="内容" prop="content">
                    <el-input type="textarea" v-model="content.content" :row="6" placeholder="请输入内容">
                    </el-input>
                </el-form-item>
                <el-form-item label="数据类型" prop="type">
                    <el-select v-model="content.type">
                        <el-option v-for="item in types" :label="item.tagName" :value="parseInt(item.tagValue)" :key="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" class="saveBtn" size="mini" @click="saveContent('ruleForm')">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</div>
</template>

<script>
import {
    ElMessage,
    ElMessageBox
} from 'element-plus'
import {
    getCookie
} from '@/utils/cookie'
import {
    onMounted,
    reactive,
    toRefs,
    nextTick,
    ref,
    
    computed,
    watch
} from 'vue'
import { useAppStore } from '@/stores/app';

export default {
    name: 'patrolcontent',
    setup() {
        const api = inject('$api')
        const tree = ref()
        const selectId = ref([])
        const store = useAppStore()
        const state = reactive({
            tableHeight: window.innerHeight * 0.60,
            page: 1,
            size: 10,
            total: 0,
            list: [],
            props: {
                label: 'name',
            },
            data: null,
            dialogContentVisible: false,
            stdData: [],
            items: [], //表格select
            content: {
                id: null,
                name: '',
                content: '',
                projectId: null,
                deviceType: null,
                type: 1,
            },
            rule: {
                name: [{
                    required: true,
                    message: '名称不能空',
                    trigger: 'change',
                }, ],
                content: [{
                    required: true,
                    message: '内容不能空',
                    trigger: 'change',
                }, ],
                type: [{
                    required: true,
                    message: '数据类型不能为空',
                    trigger: 'change',
                }, ],
            },
            types: [] // 巡检项数据类型  状态 数值
        })

        onMounted(() => {
            getProjectDeviceType()
            getDicUtilPage()
            getPatrolContentList()
        })
        const projectId = computed(() => {
            return store.projectId || getCookie('gh_projectId')
        })
        watch(projectId, (val) => {
            if (val) {
                getProjectDeviceType()
                getDicUtilPage()
                getPatrolContentList()
            }
        })
        const getDicUtilPage = () => {
            api.getDicUtil({
                dicCode: 'patrol_content_type',
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.types = res.data
            })
        }
        const getProjectDeviceType = () => {
            api.getDeviceTypeTree({
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.data = res.data
            })
        }
        const getPatrolContentList = () => {
            api.getPatrolContent({
                deviceType: selectId.value.length > 0 ? selectId.value[0].id : '',
                projectId: getCookie('gh_projectId'),
            }).then((res) => {
                state.list = res.data;
                state.total = res.total;
            })
        }
        const handleCurrentChange = (page) => {
            state.page = page
            getPatrolContentList()
        }
        const handleCheckChange = (data, state) => {
            selectId.value = []
            let keys = proxy.$refs.tree.getCheckedKeys()
            if (keys.length == 0 && state.checkedKeys.length == 0) {
                proxy.$refs.tree.setChecked(data.id, false)
            } else {
                if (keys.length > 0)
                    keys.forEach((k) => {
                        if (k != data.id) proxy.$refs.tree.setChecked(k, false)
                    })
                proxy.$refs.tree.setChecked(data.id, true)
                selectId.value.push(data)
            }
            getPatrolContentList()
        }
        const del = () => {
            if (state.items.length == 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择要删除的巡检内容',
                })
                return
            }
            ElMessageBox.confirm('是否确认要删除该数据？', '提示', {
                confirmButtonClass: 'confirmBtn',
                cancelButtonClass: 'cancelBtn',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.deletePatrolContent({
                    ids: state.items,
                }).then((res) => {
                    getPatrolContentList()
                    state.items = []
                })
            })
        }
        const edit = (row) => {
            state.dialogContentVisible = true
            state.content = {
                ...row,
            }
            selectId.value[0] = {
                id: row.deviceType,
            }
        }
        const add = () => {
            let keys = proxy.$refs.tree.getCheckedKeys()
            if (keys.length === 0) {
                ElMessage({
                    type: 'warning',
                    message: '请选择设备类型',
                })
                return
            }
            state.content = {
                id: null,
                name: '',
                content: '',
                projectId: null,
                deviceType: null,
                type: 1,
            }
            state.dialogContentVisible = true
            nextTick(() => {
                proxy.$refs.ruleForm.resetFields()
            })
        }

        const select = (selection, row) => {
            state.items = []
            if (selection.length > 0) {
                selection.forEach((item) => {
                    state.items.push(item.id)
                })
            }
        }
        // 保存
        const saveContent = (formName) => {
            proxy.$refs[formName].validate((valid) => {
                if (valid) {
                    state.content.projectId = getCookie('gh_projectId')
                    state.content.deviceType = selectId.value[0].id
                    if (state.content.id > 0) {
                        api.editPatrolContent(state.content).then((res) => {
                            getPatrolContentList()
                            state.dialogContentVisible = false
                        })
                    } else {
                        api.addContent(state.content).then((res) => {
                            getPatrolContentList()
                            state.dialogContentVisible = false
                        })
                    }
                } else {
                    return false
                }
            })
        }
        return {
            ...toRefs(state),
            selectId,
            getProjectDeviceType,
            getPatrolContentList,
            getDicUtilPage,
            handleCurrentChange,
            handleCheckChange,
            del,
            edit,
            add,
            select,
            saveContent,
            tree,
            projectId
        }
    },
}
</script>

<style lang="scss" scoped>
.content_con {
    .wrapper {
        display: flex;
        height: 100%;

        .right_tab {
            flex: 1;
        }

        .left-content {
            width: 334px;
            padding: 8px;
            height: calc(100% - 90px);
            margin-right: 20px;

            .tree-list {
                border: 1px solid #2b2e32;
            }
        }
    }
}
</style>
