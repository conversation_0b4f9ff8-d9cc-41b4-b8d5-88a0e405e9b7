<template>
<div class="record-details layout_wrapper">
    <div class="wrapper">
        <el-form :model="record" class="form" label-width="90px">
            <el-row type="flex" :gutter="30">
                <el-col :span="12">
                    <el-form-item label="巡检时间:">
                        <el-input disabled v-model="record.logTime"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="巡检人员:">
                        <el-input disabled v-model="record.staffName"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="巡检点:" prop="type">
                        <el-input disabled v-model="record.pointName"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="工单号:">
                        <el-input v-model="record.instanceId" disabled>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="巡检描述">
                        <el-input v-model="record.description" disabled />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="巡检照片">
                <div class="imgs" v-for="(imgs,i) in record.paths" :key="i">
                    <el-image preview-teleported="true" :src="imgs" alt="" style="width: 100px; height: 100px" :preview-src-list="JSON.parse(record.paths)">
                    </el-image>
                </div>
            </el-form-item>
        </el-form>
        <panel title="巡检内容" />
        <el-collapse v-model="activeNames" class="list">
            <el-collapse-item :title="item.name" :name="i" :key="i" v-for="(item, i) in record.devices">
                <div class="content" v-for="(c, j) in item.contents" :key="j">
                    <div class="content-item">
                        <div class="item-lable">巡检项：</div>
                        <div>{{ c.name }}</div>
                    </div>
                    <div class="content-item">
                        <div class="item-lable">巡检内容：</div>
                        <div>{{ c.content }}</div>
                    </div>
                    <div class="content-item">
                        <div class="item-lable">巡检值：</div>
                        <div>
                            {{ c.val }}
                        </div>
                    </div>
                    <div class="content-item">
                        <div class="item-lable">是否完成：</div>
                        <div>
                            <span style="color: green" v-if="c.tag">完成</span>
                            <span style="color: red" v-if="!c.tag">未完成</span>
                        </div>
                    </div>
                </div>
            </el-collapse-item>
        </el-collapse>
    </div>
</div>
</template>

<script>
import {
    onMounted,
    reactive,
    toRefs
} from 'vue'
import {
    useRouter
} from 'vue-router'
export default {
    setup() {
        const router = useRouter()
        const state = reactive({
            record: {}
        })
        onMounted(() => {
            if (sessionStorage.getItem('record')) {
                state.record = JSON.parse(sessionStorage.getItem('record'));
                state.record.paths = JSON.parse(state.record.paths)
            }
        })

    
        return {
            ...toRefs(state),
   
        }
    }
}
</script>

<style lang="scss" scoped>
.record-details {
    .wrapper {
        display: flex;
        flex-direction: column;
        height: 100%;

        .closeBtn {
            position: absolute;
            right: 20px;
            top: 10px;
        }

        .form {
            width: 940px;
            margin: 18px auto;

            .imgs {
                display: flex;
                flex-wrap: wrap;
            }
        }

        .list {
            color: #fff;
            height: calc(50% - 40px);
            overflow-y: auto;

            .content {
                display: flex;
                justify-content: space-around;
                margin-bottom: 10px;
                color: rgb(171, 186, 204);

                .content-item {
                    flex: 1;
                    display: flex;

                    .item-lable {
                        margin-right: 5px;
                    }
                }
            }
        }
    }
}
</style><style>
.el-collapse {
    border-top: 1px solid rgba(193, 206, 222, 0.15);
    border-bottom: 1px solid rgba(193, 206, 222, 0.15);
}

.el-collapse-item__wrap {
    border-bottom: 1px solid rgba(193, 206, 222, 0.15);
}

.el-collapse-item__header {
    font-size: 16px;
    border-bottom: none;
}

.el-collapse-item__header,
.el-collapse-item__wrap {
    background-color: transparent !important;
    color: #fff;
}

.el-collapse {
    border-bottom: none;
}

.el-image {
    width: 30%;
    margin: 5px 8px;
}
</style>
