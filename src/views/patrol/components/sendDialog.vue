<template>
  <el-dialog align-center :append-to-body="true" custom-class="custom_dialog" v-model="dialogData.visible" width="440px" :title="dialogData.title">
    <el-form class="form" ref="form" :model="dialogData.send" :rules="dialogData.rule">
      <el-form-item label="接单人员:" prop="userId">
        <el-select clearable filterable v-model="dialogData.send.userId" >
          <el-option v-for="item in users" :key="item.userId" :value="item.userId" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" size="small" class="saveBtn" @click="selectUser('form')">派 单</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  
  onMounted,
  reactive,
  toRefs
} from 'vue'
import {
  getCookie
} from '@/utils/cookie'
export default {
  props: {
    dialogData: {
      visible: false,
      title: '',
      send: {

      },
      rule: {

      }
    }
  },
  setup (props) {
    const api = inject('$api')
    const state = reactive({
      staffs: [],
      users: [],
      send: {
        "cmd": -2,
        "taskId": '',
        "description": "",
        "instanceId": "",
        "operator": getCookie("gh_id"),
        "projectId": getCookie("gh_projectId"),
        "staffType": "",
        "status": "",
        "id": "",
        "userId": getCookie("gh_id")
      }
    })
    onMounted(() => {
      // api.getProjectStaff({
      //   projectId: getCookie('gh_projectId'),
      //   type: 2
      // }).then(res => {
      //   state.staffs = res.data;
      // });
      api.getProjectStaff({
        // staffType: props.dialogData.send.staffType,
        projectId: getCookie('gh_projectId')
      }).then(res => {
        state.users = res.data;
      });
    })
    const selectUser = (formName) => {
      proxy.$refs[formName].validate((valid) => {
        if (valid) {
          state.send.userId = props.dialogData.send.userId;
          state.send.cmd = 1;
          state.send.taskId = props.dialogData.send.userId.taskId;
          state.send.instanceId = props.dialogData.send.userId.instanceId;
          state.send.staffType = props.dialogData.send.userId.staffType;
          state.send.status = props.dialogData.send.userId.status;
          state.send.id = props.dialogData.send.userId.id;
          api.processTask(state.send).then(res => {
            if (res.success) props.dialogData.visible = false
          })

        } else {
          return false
        }
      })
    }
    return {
      ...toRefs(state),
      selectUser
    }
  }
}
</script>

