<template>
  <el-dialog align-center :append-to-body="true" custom-class="custom_dialog" v-model="dialogData.visible" width="940px" :title="dialogData.title">
    <el-form class="form" ref="form" :model="dialogData.work" :rules="dialogData.rule">
      <el-row type="flex" :gutter="30">
        <el-col :span="12">
          <el-form-item label="设备类型：" prop="deviceType">
            <el-cascader :disabled="dialogData.disabled" popper-class="cascader" v-model="dialogData.work.deviceType"
              style="width: 100%" :options="types" clearable :props="props1" placeholder="请选择"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="选择区域：" prop="area">
            <el-cascader :disabled="dialogData.disabled" popper-class="cascader" v-model="dialogData.work.areaId"
              style="width: 100%" :options="areas" clearable :props="props" placeholder="请选择"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="名称：" prop="name">
            <el-input :disabled="dialogData.disabled" placeholder="请输入名称" v-model="dialogData.work.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工单主题：">
            <el-input :disabled="dialogData.disabled" placeholder="请输入工单主题"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="故障描述：" prop="description">
        <el-input :disabled="dialogData.disabled" type="textarea" placeholder="请输入内容"
          v-model="dialogData.work.description"></el-input>
      </el-form-item>
    </el-form>
    <template #footer v-if="dialogData.edit">
      <div class="dialog-footer">
        <el-button type="primary" size="mini" class="saveBtn" @click="saveWork('form')">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs } from 'vue';
import { computed,  onMounted, watch } from 'vue';
import { getCookie } from '@/utils/cookie'
import { useAppStore } from '@/stores/app';

export default {
  props: {
    dialogData: {
      visible: false,
      title: '',
      disabled: false,
      work: {
        deviceType: ''
      },
      rule: {

      }
    }
  },
  setup () {
    const api = inject('$api')
    const store = useAppStore()
    const state = reactive({
      types: [],
      areas: [],
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      props1: {
        label: 'name',
        value: 'id',
        checkStrictly: true,
      },
    })

    onMounted(() => {
      getDeviceTypeTree()
      getProjectArea()
    })
    watch(projectId, (val) => {
      if (val) {
        getDeviceTypeTree()
        getProjectArea()
      }
    })
    const projectId = computed(() => {
      return store.projectId || getCookie('gh_projectId')
    })
    const getDeviceTypeTree = () => {
      api.getDeviceTypeTree({
        projectId: getCookie('gh_projectId'),
      }).then((res) => {
        state.types = res.data
      })
    }
    const getProjectArea = () => {
      api.getProjectArea({
        projectId: getCookie('gh_projectId')
      }).then(res => {
        state.areas = res.data
      });
    }
    const saveWork = (formName) => {
      proxy.$refs[formName].validate((validate) => {
        if (validate) {

        } else {
          console.log('error submit!!');
          return false;
        }
      })
    }
    return {
      ...toRefs(state), saveWork, getDeviceTypeTree, getProjectArea,
      projectId
    }
  }
};
</script>