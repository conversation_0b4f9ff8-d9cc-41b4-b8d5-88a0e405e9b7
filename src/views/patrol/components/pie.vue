<template>
<div class="echart" ref="myEcharts"></div>
</template>

<script>
import {
    ref,
    inject,
    onMounted,
    nextTick,
    watch
} from 'vue'

export default {
    props: {
        color: {
            type: Array,
            default: () => {
                return ["rgb(255, 122, 0)", "#f4e168", "#49e5e5", "#2d8ef3"]
            },
        },
        data: {
            type: Array,
            default: () => {
                return [
                ]
            },
        },
    },
    setup(props) {
        const myEcharts = ref(null)
        let echarts = inject('ec') //引入
        watch(props, () => {
            initChart();
        });
        onMounted(() => {
            nextTick(() => {
                initChart()
            })
        })
        const getTotal = () => {
            let sum = 0;
            props.data.forEach(d => {
                sum += d.value
            })
            return sum;
        }

        const initChart = () => {
            let option = {
                // backgroundColor: '#031d33',
                tooltip: {
                    show: true
                },
                title: {
                    text: '总数:' + getTotal(),
                    x: 'center',
                    y: 'center',
                    textStyle: {
                        fontSize: 12
                    }
                },
                series: [{
                        type: "pie",
                        radius: ["60%", "85%"],
                        center: ["50%", "50%"],
                        color: props.color,
                        hoverAnimation: true,
                        z: 10,
                        itemStyle: {
                            color: (params) => {
                                var index = params.dataIndex;

                                return color[index];
                            },
                            normal: {
                                borderWidth: 5,
                                borderColor: "#020d1a",
                                label: {
                                    show: true,
                                    fontSize: 12,
                                    formatter(params) {
                                        return params.name ?
                                            params.name + '\n' + params.value :
                                            '';
                                    }
                                },
                                labelLine: {
                                    width: 4,
                                    length: 30,
                                    length2: 30,
                                    show: true,
                                    color: '#00ffff'
                                }
                            }
                        },
                        label: {
                            show: false
                        },
                        data: props.data,
                        labelLine: {
                            show: false
                        }
                    },

                    {
                        type: "pie",
                        radius: ["90%", "91%"],
                        itemStyle: {
                            color: "#667990"
                        },
                        hoverAnimation: true,
                        label: {
                            show: false
                        },
                        data: [100],
                        labelLine: {
                            show: false
                        }
                    },

                    { //内圆
                        type: 'pie',
                        radius: '50%',
                        center: ['50%', '50%'],
                        z: 1,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.RadialGradient(.5, .5, .8, [{
                                        offset: 0,
                                        color: '#122031'
                                    },
                                    {
                                        offset: .5,
                                        color: '#2c4053'
                                    },
                                    {
                                        offset: 1,
                                        color: '#587a91'
                                    }
                                ], false),
                                label: {
                                    show: false
                                },
                                labelLine: {
                                    show: false
                                }
                            },
                        },
                        hoverAnimation: false,
                        label: {
                            show: false,
                        },
                        tooltip: {
                            show: false
                        },
                        data: [100],
                        animationType: "scale"
                    }

                ]
            }

            var myChart = echarts.init(myEcharts.value)
            // 绘制图表
            myChart.setOption(option)

            myChart.resize() //刷新画布
            window.addEventListener('resize', () => {
                myChart.resize() //刷新画布
            })
        }
        return {
            myEcharts,
            initChart
        }
    },
}
</script>

<style lang="scss" scoped>
.echart {
    height: 150px;
    width: 100%;
}
</style>
