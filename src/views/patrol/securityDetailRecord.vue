<template>
    <div class="wrapper">
        <el-form :inline="true" class="search_box">
            <el-form-item label="时间选择:">
                <el-date-picker style="width: 280px;" v-model="date" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" @change="getEvent">
                </el-date-picker>
            </el-form-item>

            <!-- <el-form-item label="巡更人员:">
                <el-input placeholder="关键字" v-model="keyword"></el-input>
            </el-form-item> -->

            <el-form-item label="状态:">
                <el-select clearable v-model="status" placeholder="请选择">
                    <el-option label="超时" value="0"></el-option>
                    <el-option label="漏巡" value="1"></el-option>
                    <el-option label="正常" value="2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="巡检计划:">
                <el-select clearable filterable>
                    <el-option v-if="item in plans" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <div class="searchBtn cursor" @click="search" type="text">查询</div>
            </el-form-item>
            <el-form-item>
                <div class="searchBtn cursor" @click="down" type="text">导出</div>
            </el-form-item>
        </el-form>

        <el-table :data="list" height="calc(100% - 65px)" fit table-layout="auto">
            <template #empty>
                <no-data />
            </template>
            <el-table-column prop="startTime" label="计划开始时间" align="center" width="200">
            </el-table-column>
            <el-table-column prop="endTime" label="计划结束时间" align="center" width="200">
            </el-table-column>
            <el-table-column prop="times" label="计划次数" align="center">
            </el-table-column>
            <el-table-column prop="planName" label="计划名称" align="center">
            </el-table-column>
            <el-table-column prop="logTime" label="巡检时间" align="center" width="200">
            </el-table-column>
            <el-table-column prop="events" label="巡检标准" width="200" align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="username" label="巡检人员" align="center">
            </el-table-column>
            <el-table-column prop="pointName" label="巡检点" align="center">
            </el-table-column>
            <el-table-column prop="num" label="巡检次数" align="center">
            </el-table-column>
            <el-table-column prop="delay" label="状态" align="center">
                <template #default="props">
                    <span style="color:green" :style="{ color: getState(props.row) == '已巡检' ? 'green' : 'red' }">{{
            getState(props.row) }}</span>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="names" label="值班人员" align="center">
        </el-table-column> -->
            <el-table-column type="expand" label="巡检照片" width="150">
                <template #default="props">
                    <el-image  class="image" preview-teleported="true" :preview-src-list="JSON.parse(props.row.paths)"
                        style="width: 100px; height: 100px" :src="item"
                        v-for="(item, i) in JSON.parse(props.row.paths) " :fit="fit" :key="i"></el-image>
                </template>
            </el-table-column>
        </el-table>

        <div class="page center">
            <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { getCookie } from '@/utils/cookie'
import dayjs from 'dayjs'
import {
    
    onMounted,
    reactive,
    toRefs
} from 'vue'
import status from '../Broadcast/status.vue'
export default {
    name: 'patroldetailrecord',
    setup() {
        const state = reactive({
            date: [],
            page: 1,
            size: 10,
            total: 0,
            list: [],
            status: null,
            keyword: null,
            plans: [],
        })
        state.date.push(dayjs().subtract(30, 'day').format('YYYY-MM-DD 00:00:00'));
        state.date.push(dayjs().format('YYYY-MM-DD 00:00:00'));

        const api = inject('$api')
        onMounted(() => {
            getPatrolRecord()
            getPlan();
        })
        const getPatrolRecord = () => {
            api.getSecurityDetailRecord({
                page: state.page,
                size: state.size,
                keyword: state.keyword,
                status: state.status,
                type: 5,
                bt:dayjs(state.date[0]).format('YYYY-MM-DD 00:00:00'),
                et: dayjs(state.date[1]).format('YYYY-MM-DD 23:59:59'),
            }).then(res => {
                state.list = res.data;
                state.total = res.total;
            });

        }
        const getPlan = () => {
            api.getPatrolPlan({
                projectId: getCookie('gh_projectId'),
            }).then(res => {
                state.plans = res.data;
            });
        }
        const search = () => {
            state.page = 1;
            getPatrolRecord();
        }

        const handleCurrentChange = (page) => {
            state.page = page
            getPatrolRecord();
        }
        const getState = (item) => {
            let name = "已巡检";
            if (item.logTime == null && dayjs().isAfter(item.endTime)) {
                name = "遗漏";
            } else if (item.delay) {
                name = "超时";
            } else if (item.logTime == null && dayjs().isBefore(dayjs(item.endTime))) {
                name = "未巡检";
            }
            return name;
        };



        const down = () => {
            api.getSecurityDetailRecordDown(
                {
                    keyword: state.keyword,
                    // type: 5,
                    status: state.status,
                    bt: typeof state.date[0] == 'string' ? state.date[0] : dayjs(state.date[0]).format('YYYY-MM-DD HH:mm:ss'),
                    et: typeof state.date[1] == 'string' ? state.date[1] : dayjs(state.date[1]).format('YYYY-MM-DD HH:mm:ss'),
                }
            ).then(res => {
                const link = document.createElement('a')
                const blob = new Blob([res], {
                    type: 'application/vnd.ms-excel'
                })
                link.style.display = 'none'
                link.href = URL.createObjectURL(blob)
                link.setAttribute('download', `巡检统计.xlsx`)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
        }
        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            getState,
            down,
        }
    },
}
</script>

<style lang="scss" scoped>
.wrapper {
    height: 100%;
}

.el-form--inline .el-form-item {
    margin-right: 5px;
}

.el-form-item__label {
    padding-right: 5px;
}


// :deep(.el-image-viewer__mask){
//     opacity: 1 !important;
// }
</style>
