<template>
    <div class="">
        <div class="left">
            <div class="header flex-start">
                <img src="@/assets/images/common/head.png">
                <div> 巡检点列表</div>
            </div>
            <div class="input">
                <el-input v-model="keyword" @change="search" :prefix-icon="Search" placeholder="按巡检点名称搜索"></el-input>
            </div>
            <div class="device">
                <el-scrollbar>
                    <div class="list space-between" v-for="item in list" :key="item.id">
                        <div class="center cursor">
                            <div>
                                <span class="iconfont icondingwei2"></span>
                            </div>
                            <div class="name">{{ item.name }}</div>
                        </div>
                        <div class="center state">
                            <!-- <div v-for="(p,j) in item.state" :key="j">11</div> -->
                        </div>
                        <div class="position cursor">
                            <img src="@/assets/images/common/position.png" />
                        </div>
                    </div>
                </el-scrollbar>

            </div>

            <div class="page center">
                <el-pagination prev-icon="CaretLeft" next-icon="CaretRight" :page-size="size" :current-page="page"
                    layout="total,prev, pager, next" @current-change="handleCurrentChange" :total="total">
                </el-pagination>
            </div>

        </div>


        <pop :show="activeMenus.popName ? true : false" :title="activeMenus.name || ''">
            <Transition name="fade" mode="out-in" appear>
                <component :is="activeMenus.popName"></component>
            </Transition>
        </pop>

        <div class="right">
            <div class="item" style="flex:1">
                <sub-title title='运行统计' />
                <div class="item-body order">
                    <div>
                        <div class="total center"></div>
                        <div class="order-left">
                            <div class="center">
                                <div></div>
                            </div>
                        </div>
                    </div>
                    <div class="order-right">
                        <div class="order-text">
                            <div class="dot" style="background:#1AAC1A"></div><span class="text">巡检点数:</span><span
                                class="num">{{ point_count }}</span>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C47F13"></div><span class="text">巡检计划:</span><span
                                class="num">{{ plan_count }}</span>
                        </div>
                        <div class="order-text">
                            <div class="dot" style="background:#C33838"></div><span class="text">巡检线路:</span><span
                                class="num">{{ line_count }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title title='任务统计' />
                <div class="item-body  energy_category">
                    <noData msg="暂无图表"
                        v-if="inspectionTask.COMPLETED == 0 && inspectionTask.PENDING_PROCESSING == 0 && inspectionTask.PROCESSING == 0 && inspectionTask.EXPIRED == 0">
                    </noData>
                    <energy-sub v-else :echartData="echartData" style="flex:1"></energy-sub>
                    <div class="energy_category-item center">
                        <div>
                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(0deg, #6CF8E7, #4CEDD9);" class="dot"></div>
                                    <div class="text">已完成</div>
                                </div>

                                <div class="num num1">{{ inspectionTask.COMPLETED || 0 }}</div>
                            </div>
                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(0deg, #FABB32, #E9A819);" class="dot"></div>
                                    <div class="text">待处理</div>
                                </div>

                                <div class="num num2">{{ inspectionTask.unCompleted || 0 }}</div>
                            </div>
                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(0deg, #71B0FF, #358FFF);" class="dot"></div>
                                    <div class="text">处理中</div>
                                </div>

                                <div class="num num3">{{ inspectionTask.PROCESSING || 0 }}</div>
                            </div>
                            <div>
                                <div class="center">
                                    <div style="background: linear-gradient(-26deg, #F36447, #EF5B34);" class="dot">
                                    </div>
                                    <div class="text">已过期</div>
                                </div>

                                <div class="num num4">{{ inspectionTask.EXPIRED || 0 }}</div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="item" style="flex:2">
                <sub-title title='计划统计' />
                <div class="item-body event">
                    <plancount :data="echartData1"></plancount>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import {
    defineComponent,
    
    reactive,
    toRefs,
    computed,
    onMounted,
} from 'vue';

import {
    getCookie
} from "@/utils/cookie";

import pop from '@/components/pop/index.vue'
import plancount from './components/plancount.vue';
import energySub from '@/components/home/<USER>';
import { useAppStore } from '@/stores/app';
export default defineComponent({
    name: "patrol",
    components: {
        pop,
        energySub,

        plancount
    },

    setup() {
        const api = inject('$api')
        const store = useAppStore();
        const state = reactive({
            keyword: "",
            size: 10,
            page: 1,
            total: 100,
            list: [],
            count: {
                yesterday: 0,
                month: 0,
                lastMonth: 0,
                year: 0
            },
            inspectionTask: {
                COMPLETED: 0,
                PENDING_PROCESSING: 0,
                PROCESSING: 0,
                EXPIRED: 0
            },
            echartData: [{
                name: '已完成',
                value: 1
            },
            {
                name: '待处理',
                value: 6
            },
            {
                name: '处理中',
                value: 3
            },
            {
                name: '已过期',
                value: 2
            },
            ],
            point_count: 0,
            line_count: 0,
            plan_count: 0,
            echartData1: [
                // {
                //     name: "已过期",
                //     value: 0
                // },

                // {
                //     name: "未启用",
                //     value:0
                // }
            ]

        })
        //当前激活的一级菜单
        const activeMenus = computed(() => {
            let menu = getCookie("funMenus");
            return store.funMenus ?
                store.funMenus :
                menu ?
                    JSON.parse(menu) :
                    "";
        });
        onMounted(() => {
            getPatrolPoint();
            getPatrolLine();
            getPatrolPlan();
            getTaskCount();
            getPlanCount();
        });
        const getPatrolPoint = async () => {
            let {
                data,
                total
            } = await api.getPatrolPoint({
                search: state.keyword,
                size: state.size,
                page: state.page,
                projectId: getCookie("gh_projectId")
            })
            state.total = data.result.totalElements;
            state.list = data.result.content;
            state.point_count = data.result.totalElements;

        };
        const getPatrolPlan = async () => {
            let {
                data,
            } = await api.getSecurityPlan({
                size: 1000,
                page: 1,
            })
            state.plan_count = data.totalElements;

        };
        const getPatrolLine = async () => {
            let {
                data,
            } = await api.getPatrolLine({
                size: 1000,
                page: 1,
            })

            state.line_count = data.result.totalElements;

        };
        const getTaskCount = async () => {
            let {
                data,
            } = await api.getTaskCount({
            })

            data.forEach(d => {
                state.inspectionTask[d.state] = d.count;
                state.echartData[0].value = state.inspectionTask.COMPLETED || 0;
                state.echartData[1].value = state.inspectionTask.PENDING_PROCESSING || 0;
                state.echartData[2].value = state.inspectionTask.PROCESSING || 0;
                state.echartData[3].value = state.inspectionTask.EXPIRED || 0;
            })

        };


        const getPlanCount = async () => {
            let {
                data,
            } = await api.getPlanCount({
            })
            state.echartData1 = [];
            data.forEach(d => {
                if (d.state == 'ENABLED') {

                    state.echartData1.push({
                        name: "已启用",
                        value: d.count
                    })
                } else if (d.state == 'DISABLED') {
                    state.echartData1.push({
                        name: "已过期",
                        value: d.count
                    })
                } else if (d.state == 'PENDING') {
                    state.echartData1.push({
                        name: "未启用",
                        value: d.count
                    })
                }
            })

        };



        const handleCurrentChange = (page) => {
            state.page = page;
            getPatrolPoint();
        }

        const search = () => {
            state.page = 1;
            getPatrolPoint();
        }




        return {
            ...toRefs(state),
            handleCurrentChange,
            search,
            activeMenus
        }
    }
});
</script>

<style lang="scss" scoped>
.energy_category {

    display: flex;
    height: calc(100% - 40px);
    align-items: center;

    .dot {
        width: 6px;
        height: 6px;
    }

    &-item {
        flex: 1;
        flex-direction: column;

        &>div>div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            margin-bottom: 14px;
        }
    }

    .text {

        font-size: 14px;
        font-family: "Alibaba-PuHuiTi";
        font-weight: normal;
        color: #FFFFFF;
        margin-left: 5px;
    }

    .num1 {
        background: linear-gradient(0deg, #6CF8E7 0%, #4CEDD9 100%);
    }

    .num2 {
        background: linear-gradient(0deg, #FABB32 0%, #E9A819 100%);
    }

    .num3 {
        background: linear-gradient(0deg, #71B0FF 0%, #358FFF 100%);
    }

    .num4 {
        background: linear-gradient(-26deg, #F36447 0%, #EF5B34 100%);
    }

    .num {

        font-size: 20px;
        font-family: "BEBAS";
        font-weight: 400;
        color: #FFFFFF;

        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: flex;
        align-self: flex-end;
        margin-left: 30px;
    }

}
</style>
