<template>
<div class="layout_wrapper">
    <el-row :gutter="10" class=" row">
        <div class="list" v-for="item in list" :key="item.id">
            <div class="icon h100">
                <span style="font-size:32px" class="iconfont icondianshiji"></span>
            </div>
            <div class="content">
                <div class="title" style="font-size:16px">名称:{{item.name}}</div>
                <div :style="{color:item.isOnline==0?'orange':'green'}"><span class="title">状态:</span>{{item.isOnline==0?'离线':'在线'}}</div>
            </div>
            <el-checkbox class="check" v-model="item.checked"></el-checkbox>
        </div>
    </el-row>

    <div class="btn center" v-if="list.length>0">
        <el-button type="primary" @click="cmd('shutdown')" size="small">关机</el-button>
        <el-button type="primary" @click="cmd('reboot')" size="small">重启</el-button>
        <el-button type="primary" @click="cmd('pause')" size="small">暂停</el-button>
        <el-button type="primary" @click="cmd('play')" size="small">播放</el-button>
        <el-button type="primary" @click="showVol" size="small">设置音量</el-button>
    </div>

    <el-dialog align-center :append-to-body="true" top="100px" draggable :model-value="show" @update:model-value="updateShow" title="音量设置" custom-class="cctv-dialog" width="400px">
        <el-form ref="form">
            <el-form-item label="音量">
                <el-input placeholder="请输入音量0-100" v-model="vol" size="small"></el-input>
            </el-form-item>
        </el-form>
        <!-- <div class="line left-top"></div>
        <div class="line right-top"></div>
        <div class="line left-buttom"></div>
        <div class="line right-buttom"></div> -->
        <template #footer class="dialog-footer">
            <el-button type="primary" @click="saveVol" size="mini">确 定</el-button>
        </template>
    </el-dialog>

</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    getCurrentInstance,
    onMounted
} from 'vue';
import {
    ElMessage
} from 'element-plus';


export default {
    name: 'InfoMonitor',
    setup() {
        const api = inject('$api');
        const state = reactive({
            list: [{
                    id: 11,
                    name: '33',
                    isOnline: 0,
                    checked: false,
                },
                {
                    id: 12,
                    name: '111',
                    isOnline: 0,
                    checked: false,
                },
            ],
            vol:0,
            show:false,
        });
        onMounted(() => {
          //  getTerminal();
        })
        const getTerminal = () => {
            api.getTerminal().then(res => {
                res.data.forEach(d => {
                    d.checked = false;
                })
                state.list = res.data;
            });
        }

        const cmd = (key) => {
            let ids = [];
            state.list.forEach(d => {
                if (d.checked) {
                    ids.push(d.id);
                }
            });
            if (ids.length == 0) {
                ElMessage.error("请选择要操作的终端");
                return;
            }
            api.cmd({
                ids: ids,
                cmd: key
            }).then(res => {
                if (res.success) {
                    ElMessage.success("操作成功");
                    getTerminal();
                }
            });
        }
        const saveVol = () => {
            if (!state.vol) {
                ElMessage.error("请设置0-100的音量");
                return;
            }
            let ids = [];
            state.list.forEach(d => {
                if (d.checked) {
                    ids.push(d.id);
                }
            });
            if (ids.length == 0) {
                ElMessage.error("请选择要操作的终端");
                return;
            }
            api.setVol({
                ids: ids,
                vol: state.vol
            }).then(res => {
                if (res.success) {
                    ElMessage.success("操作成功")
                }
                state.show = false;
            });
        }

        const showVol=()=> {
            state.show = true;
        }

        const updateShow = (value) => {
            state.show = value;
        }

        return {
            ...toRefs(state),
            cmd,
            saveVol,
            getTerminal,
            showVol,
            updateShow
        };
    },
};
</script>

<style lang="scss" scoped>
.layout_wrapper {
    padding: 0 10px;


    .row {

        display: flex;
        flex-wrap: wrap;
    }

    .list {
        border: solid 1px  #628083;
        width: 300px;
        height: 60px;
        display: flex;
        align-items: center;
        margin: 10px 10px 0 0;
        position: relative;
    }


    .icon {
        width: 50px;
        background-color: rgba(61,233,250,.5);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32px;
    }

    .content {
        width: 250px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-right: 20px;
     
    }
    .title{
        color:#c1dfe2;
    }

    .check {
        position: absolute;
        top: 5px;
        right: 5px;
    }

    .btn {
        width: 100%;
        position: fixed;
        bottom: 100px;
    }



}
</style>
