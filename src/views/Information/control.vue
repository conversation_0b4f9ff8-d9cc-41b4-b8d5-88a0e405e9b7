<template>
<div class="layout_wrapper control_wrapper">
    <sub-title2 title="终端状态">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
    </sub-title2>
    <div class="control_list">
        <div class="list">
            <div class="status_list">
                <div v-for="(item, index) in terminalList" :key="index" class="item">
                    <el-checkbox-group v-model="checkList" @change="handleCheckedChange">
                        <div class="icon">
                            <img :src="icon" alt="" />
                        </div>
                        <div class="info">
                            <!-- <div class="name">{{ item.name }}</div> -->
                            <div class="ip">{{ item.ip }}</div>
                            <div class="status">
                                <div class="list-title" >
                                    <div class="title-text">在线</div>
                                    <div>
                                        <img :src="run" alt />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <el-checkbox :label="item.id" :value="item.checked" class="check"></el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
        </div>
        <div class="con_bottom center">
            <div class="left-btn">

                <div class="item" @click="clickTiming('screenoff')">
                    <span class="iconfont iconguanji"></span>
                    关屏
                </div>

                <div class="item" @click="clickTiming('reboot',item)">
                    <span class="iconfont iconzhongqi"></span>
                    重启
                </div>

                <div class="item" @click="clickTiming('screenon')">
                    <span class="iconfont iconbofang3"></span>
                    开屏
                </div>

            </div>
            <!-- <div class="right"></div> -->
        </div>
    </div>

</div>
</template>

<script>
import {
    reactive,
    toRefs
} from "vue";
import {
    getCurrentInstance,
    onMounted
} from "vue";
import {
    ElMessage
} from "element-plus";

import {
    io
} from 'socket.io-client'

export default {
    name:'InfoControl',
    setup() {
        const socket = io("http://**************:8200");
        const api = inject('$api');

        const state = reactive({

            icon: require("@/assets/images/terminal.png"),
            run: require("@/assets/images/normal.png"),
            offLine: require("@/assets/images/offLine.png"),
            zy: require("@/assets/images/abnormal.png"),

            terminalList: [{
                    name: '终端1',
                    ip: '127.0.0.1',
                    status: 0
                },

            ],
            checkList: [],

            list: [{
                    time: [],
                },
                {
                    time: [],
                },
            ],

            isIndeterminate: true,
            checkAll: false,
            dialogData: {
                visible: false,
                title: "定时设置",
                form: {
                    radio: 1,
                    time: [],
                    weekList: [],
                },
            },
        });
        onMounted(() => {
            getTerminalPage();
        });

        const getTerminalPage = () => {
            api.getInfoTerminal().then((res) => {
                state.terminalList = res.data;
            });
        };

        const clickTiming = (cmd, item) => {
            if (state.checkList.length == 0) {
                ElMessage({
                    type: "warning",
                    message: "请选择终端",
                });
                return;
            }
            state.terminalList.forEach(t => {
                if (t.checked) {
                    let data = {
                        "source": "nbs_control",
                        "sourceip": "web",
                        "sourceguid": "5edf2d4733dbd",
                        "target": cmd == "screenoff" ? "web" : "nbs_app",
                        "targetip": t.ip,
                        "targetguid": t.guid,
                        "token": "5edf2d4733dbd",
                        "command": cmd,
                        "parameters": "",
                        "message": {}
                    }
                    socket.send(data, data => {

                    })
                }
            });
            ElMessage({
                type: "success",
                message: "指令发送成功",
            });

        };

        const clickAddTime = () => {
            state.list.push({
                time: [],
            });
        };

        const handleCheckedChange = (value) => {
            let checkedCount = value.length;
            state.checkAll = checkedCount === state.terminalList.length;
            state.isIndeterminate =
                checkedCount > 0 && checkedCount < state.terminalList.length;
            state.checkList = value;
        };

        const handleCheckAllChange = (val) => {
            if (val) {
                state.terminalList.forEach((item) => {
                    state.checkList.push(item.id);
                });
            } else {
                state.checkList = [];
            }
            state.isIndeterminate = false;
        };
        return {
            ...toRefs(state),
            getTerminalPage,
            clickTiming,
            clickAddTime,
            handleCheckedChange,
            handleCheckAllChange,
        };
    },
};
</script>

<style lang="scss" scoped>
.control_wrapper {
    padding: 0 10px;

    .control_list {
        display: flex;
        flex-direction: column;
        height: calc(100% - 49px);

        .list {
            height: calc(100% - 110px);
            margin-bottom: 15px;
            overflow-x: scroll;

            .status_list {
                display: flex;
                flex-wrap: wrap;

                .item {
                    position: relative;
                    width: calc(20% - 29px);
                    margin: 0 4px;
                    background: rgba(47, 54, 60, 0.3);
                    border: 1px solid #4a5966;
                    margin-bottom: 8px;
                    padding: 10px;

                    &:nth-child(5n + 1) {
                        margin-left: 0;
                    }

                    &:nth-child(5n + 0) {
                        margin-right: 0;
                    }

                    .icon {
                        width: 108px;
                        height: 108px;
                        background: #ffffff;
                        margin-right: 10px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .info {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;

                        .name {
                            font-size: 16px;
                            font-family: "PingFangSC-Medium", "PingFang SC";
                            font-weight: 500;
                            color: #ffffff;
                            margin-bottom: 6px;
                        }

                        .ip {
                            font-size: 14px;
                            font-family: "Alibaba-PuHuiTi";
                            font-weight: 400;
                            color: #9ca4b7;
                        }

                        .status {
                            display: flex;
                            text-align: center;

                            .list-title {
                                width: 40px;

                                .title-text {
                                    font-size: 12px;
                                    line-height: 28px;
                                    font-family: "Alibaba-PuHuiTi";
                                    font-weight: 400;
                                    color: #687287;
                                }
                            }
                        }
                    }

                    .check {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                    }
                }
            }
        }

        .con_bottom {
            flex: 1;
            display: flex;

            .left-btn,
            .right-btn {
                flex: 1;
            }

            .left-btn {
                display: flex;
                justify-content: center;

                .item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 64px;
                    height: 64px;
                    background: rgba(47, 54, 60, 0.8);
                    font-size: 12px;
                    font-family: "Alibaba-PuHuiTi";
                    font-weight: 400;
                    color: #9ca4b7;
                    margin: 0 3px;
                    cursor: pointer;

                    span {
                        color: #ffffff;
                        font-size: 22px;
                    }
                }
            }
        }

        .center{
            line-height: unset;
        }
    }
}
</style>
<style>
.el-checkbox-group {
    display: flex;
    width: 100%;
}

.el-checkbox__inner {
    background-color: #000;
    border: 1px solid #c7dfff;
}

.status_list .el-checkbox__label {
    display: none;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #000;
    border-color: rgba(255, 255, 255, 0.5);
}

.week .el-checkbox__inner:hover {
    border-color: rgba(255, 255, 255, 0.5);
}

.week .el-checkbox,
.week .el-checkbox__input.is-checked+.el-checkbox__label {
    color: #fff;
}

.sub_title .el-checkbox {
    margin-right: 15px;
}

.sub_title .el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgba(255, 255, 255, 0.5);
}
</style>
