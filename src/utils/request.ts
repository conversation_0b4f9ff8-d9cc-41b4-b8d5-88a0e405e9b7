import { ElMessage } from 'element-plus';
import { getCookie } from '@/utils/cookie'
import axios from 'axios'
import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios'
const service = axios.create({
        timeout: 10000,// 超时时间
        baseURL: window.apiUrl
})
service.interceptors.request.use(
        (config: InternalAxiosRequestConfig) => {
                if (getCookie('gh_token')) {
                        config.headers['Authorization'] = "bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ;
                }
                config.headers['project'] = getCookie('gh_projectId') || -1;
                return config
        },
        (error: any) => {
                console.log(error) // for debug
                return Promise.reject(error)
        }
)
service.interceptors.response.use(
        (response: AxiosResponse) => {
                const res = response.data
                if (!res.success && (res.config && res.config.type !== 'blob')) {
                        ElMessage.error(res.msg);
                        return Promise.reject(new Error(res.msg || 'Error'))
                } else if (!res.success&&!res.type) {
                        ElMessage.error(res.msg);
                        return false;
                }
                else {
                        return res
                }
        },
        (error: any) => {
                return Promise.reject(error)
        }
)
export default service