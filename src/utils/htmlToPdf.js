import html2canvas from "html2canvas"
import jsPDF from "jspdf"



  function getPdfFromHtml (ele, pdfFileName) {

    let eleW = ele.offsetWidth;

    let eleH = ele.scrollHeight;

    let eleOffsetTop = ele.offsetTop;

    let eleOffsetLeft = ele.OffsetLeft;

    var canvas = document.createElement("canvas");

    canvas.style.background = "rgba(64,68,114,0.8)";

    var abs = 0;

    let win_in = document.documentElement.clientWidth || document.body.clientWidth;  //获得当前可视窗口的宽度

    let win_out = window.innerWidth;   //获得当前窗口的宽度（包含滚动条）

    if (win_out > win_in) {

        abs = (win_out - win_in) / 2    //获得滚动天宽度一半

    }

    canvas.width = eleW * 2;   //将画布宽高放大两倍

    canvas.height = eleH * 2;

    var context = canvas.getContext('2d');

    context.scale(2, 2);  //增强图片清晰度

    context.translate(-eleOffsetLeft - abs, -eleOffsetTop);

    html2canvas(ele, { dpi: 300, backgroundColor: "rgba(64,68,114,0.8)", useCORS: true }).then(canvas => {

        var contentWidth = canvas.width;

        var contentHeight = canvas.height;

        var pageHeight = (contentWidth / 592.28) * 841.89;   //保持A4纸宽高比

        var leftHeight = contentHeight;

        var position = 0;

        var imgWidth = 595.28;

        var imgHeight = (595.28 / contentWidth) * contentHeight;

        var pageData = canvas.toDataURL("image/jpeg", 10);

        var pdf = new jsPDF("", 'pt', 'a4');

        if (leftHeight < pageHeight) {

            pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);

        } else {

            while (leftHeight > 0) {

                pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);

                leftHeight -= pageHeight;

                position -= 841.89;

                if (leftHeight > 0) {
                    pdf.addPage()
                }
            }

        }
        pdf.save(pdfFileName + '.pdf');
    })
}


export default getPdfFromHtml
