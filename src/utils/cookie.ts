import Cookies from 'js-cookie'

export function removeToken(key: string) {
        return Cookies.remove(key)
}

export function setCookie(key: string, token: string) {
         Cookies.set(key, token)
}

export function getCookie(key: any) {
        return Cookies.get(key)
}

export function setCookieExpires(key: any, value: any, expires: any) {
        return Cookies.set(key, value, { expires })
}
