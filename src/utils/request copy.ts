import { ElMessage } from 'element-plus';
import { getCookie, setCookie } from '@/utils/cookie'
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios'

// 是否取消重复请求开关
const cancelDuplicated = true

// 存储每个请求中的map
const pendingXHRMap = new Map()

// 取消请求类型定义 便于后期对此类型不做异常处理
const REQUEST_TYPE = {
        DUPLICATED_REQUEST: 'duplicatedRequest'
}

const duplicatedKeyFn = (config: AxiosRequestConfig) => {
        // 可在此设置用户自定义其他唯一标识 默认按请求方式 + 请求地址
        return `${config.method}${config.url}${JSON.stringify(config.params)}`
}
const addPendingXHR = (config: AxiosRequestConfig) => {
        if (!cancelDuplicated) {
                return
        }
        const duplicatedKey = JSON.stringify({
                duplicatedKey: duplicatedKeyFn(config),
                type: REQUEST_TYPE.DUPLICATED_REQUEST
        })
        config.cancelToken = config.cancelToken || new axios.CancelToken((cancel) => {
                if (duplicatedKey && !pendingXHRMap.has(duplicatedKey)) {
                        pendingXHRMap.set(duplicatedKey, cancel)
                }else if(duplicatedKey &&pendingXHRMap.has(duplicatedKey))
                {
                        cancel(duplicatedKey)
                }
        })
}

const removePendingXHR = (config:AxiosRequestConfig) => {
        if (!cancelDuplicated) {
                return
        }
        const duplicatedKey = JSON.stringify({
                duplicatedKey: duplicatedKeyFn(config),
                type: REQUEST_TYPE.DUPLICATED_REQUEST
        })
        
        if (duplicatedKey && pendingXHRMap.has(duplicatedKey)) {
                const cancel = pendingXHRMap.get(duplicatedKey)
                cancel(duplicatedKey)
                console.log(duplicatedKey+"----"+"delete")
                pendingXHRMap.delete(duplicatedKey)
        }
}


const service = axios.create({
        // timeout: 5000,// 超时时间
        baseURL: process.env.NODE_ENV === 'production' ? window.PROD_BASE_API : window.DEV_BASE_API
})

service.interceptors.request.use(
        (config: AxiosRequestConfig) => {
                if (getCookie('gh_token')) {
                        config.headers['Authorization'] = "bearer " + getCookie("gh_token");
                }
                config.headers['project'] = getCookie('gh_projectId') || -1;

                removePendingXHR(config)
                addPendingXHR(config)

                return config
        },
        (error: any) => {
                console.log(error) // for debug
                return Promise.reject(error)
        }
)
service.interceptors.response.use(
        (response: AxiosResponse) => {
                removePendingXHR(response.config)
                const res = response.data
                if (!res.success && (res.config && res.config.type !== 'blob')) {
                        ElMessage.error(res.msg);
                        return Promise.reject(new Error(res.msg || 'Error'))
                } else if (!res.success && !res.type) {
                        ElMessage.error(res.msg);
                        return false;
                }
                else {
                        return res
                }
        },
        // (error: any) => {
        //         let isDuplicatedType;
        //         try {
        //           const errorType = (JSON.parse(error.message) || {}).type
        //           isDuplicatedType = errorType === REQUEST_TYPE.DUPLICATED_REQUEST;
        //         } catch (error) {
        //           isDuplicatedType = false
        //         }

        //         return Promise.reject(error)
        // }
)
export default service