import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import SubTitle from '@/components/SubTitle.vue'
import SubTitle1 from '@/components/SubTitle1.vue'
import SubTitle2 from '@/components/SubTitle2.vue'
import LayoutHeader from '@/views/Layout/LayoutHeader.vue'

import Line from '@/components/line.vue'
import Menu from '@/components/Menu.vue'
import Floor from '@/components/Floor.vue'
import Panel from '@/components/device/Panel.vue'


import router from './router'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import * as echarts from 'echarts'



import '@/utils/rem.ts'
import '@/styles/index.scss'
import '@/assets/iconfont/iconfont.css'


import './permission'


const app = createApp(App)
app.use(createPinia())
app.use(ElementPlus)
app.use(router)
app.component('SubTitle', SubTitle)
app.component('SubTitle1', SubTitle1)
app.component('SubTitle2', SubTitle2)
app.component('LayoutHeader', LayoutHeader)
app.component('Line', Line)
app.component('GMenu', Menu)
app.component('Floor', Floor)
app.component('Panel', Panel)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.mount('#app')


const api = {} as ApiModules
// 使用 Vite 的 import.meta.glob 替代 require.context
const modules = import.meta.glob('./api/**/*.ts', { eager: true })
Object.entries(modules).forEach(([, module]) => {
  const moduleExports = module as Record<string, any>
  Object.entries(moduleExports).forEach(([key, value]) => {
    api[key] = value
  })
})



// 组件
// 导入 views 和 components 目录下的所有 Vue 文件
const ViewComponents: any = import.meta.glob('./views/**/*.vue', { eager: true })
const Components: any = import.meta.glob('./components/**/*.vue', { eager: true })

// 合并两个对象
const AllComponents = { ...ViewComponents, ...Components }

for (const path in AllComponents) {
  let name: string

  // 根据路径匹配不同的命名规则
  if (path.startsWith('./views/')) {
    name = path.match(/\.\/views\/[^/]+\/([^/]+)\.vue$/)?.[1] as string
  } else if (path.startsWith('./components/')) {
    name = path.match(/\.\/components\/[^/]+\/([^/]+)\.vue$/)?.[1] as string
  } else {
    // 兜底方案，提取文件名
    name = path.match(/\/([^/]+)\.vue$/)?.[1] as string
  }

  const component = AllComponents[path].default
  app.component(component.name || (component.__name || name), AllComponents[path].default)
}

app.provide('$api', api)

app.provide('ec', echarts)