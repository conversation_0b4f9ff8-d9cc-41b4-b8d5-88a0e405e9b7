{"name": "gh-ops", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/multimonth": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/vue3": "^6.1.17", "axios": "^1.10.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.10.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "mitt": "^3.0.1", "mqtt": "^5.13.1", "pinia": "^3.0.1", "postcss-px2rem": "^0.3.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "socket.io-client": "^2.5.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "sass": "^1.89.2", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}